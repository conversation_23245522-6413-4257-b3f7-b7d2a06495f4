<?php

namespace App\Console\Commands;

use App\Models\ConnectCustomer;
use App\Models\OrderDetail;
use App\Models\Referral;
use App\Models\ReferralLog;
use App\Services\MattermostLog\MattermostLogService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RemoveDuplicateReferral extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:duplicate-referral {order_id=all} {--id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->option('id')) {
            $id = $this->option('id');
        } else {
            $id = shopSetting(5, 'sca_duplicate_referral_id') - 200;
        }

        $startTime = Carbon::now();
        $this->removeDuplicateConnectCustomer($startTime);
        $lastReferral = Referral::select('id')->orderBy('id', 'desc')->first();
        log::info('Start remove duplicate referrals: ' . $startTime->toTimeString());
        $referralIds = Referral::select(DB::raw('MAX(id) as id, order_id, affiliate_id, shop_id, COUNT(*) AS total_record, total, commission'))
            ->where('id', '>=', $id)
            ->where('id', '<', $lastReferral->id)
            ->where('level', 0)
            ->whereNull('refund_id');
        $referralIds = $this->argument('order_id') == 'all' ? $referralIds->whereNotNull('order_id') : $referralIds->whereOrderId($this->argument('order_id'));
        //get list referral ids will delete
        $referralIds = $referralIds->groupBy(['order_id', 'affiliate_id', 'shop_id', 'total', 'commission'])
        ->having('total_record', '>', 1)->limit(200)->get()->keyBy('id')->toArray();
        $referralIdList = array_keys($referralIds);
        $refOrderIds = array_values(array_map(function ($item){
            return $item['id'] . '-' .$item['order_id'] . '-' . $item['shop_id'];
        }, $referralIds));

        //get list mlm referral ids will delete
        $mlmIds = Referral::select('id', 'order_id', 'shop_id')->whereIn('referral_id', $referralIds)->get()->keyBy('id')->toArray();
        $mlmIdList = array_keys($mlmIds);
        $mlmOrderIds = array_values(array_map(function ($item){
            return $item['id'] . '-' .$item['order_id'] . '-' . $item['shop_id'];
        }, $mlmIds));

        $removeOrderIds = array_unique (array_merge ($refOrderIds, $mlmOrderIds));
        $removeIds = array_unique (array_merge ($referralIdList, $mlmIdList));

        log::info('Duplicate referrals will be remove: ' . json_encode($removeOrderIds));
        $this->info('Duplicate referrals will be remove: ' . json_encode($removeOrderIds));
        if($removeIds) {
            DB::table('referrals')->whereIn('id', $removeIds)->delete();

            if (sizeof($removeOrderIds) > 0) {
                foreach ($removeOrderIds as $removeOrderId) {
                    $removeOrderIdArray = explode('-', $removeOrderId);
                    OrderDetail::where([
                        'shop_id' => $removeOrderIdArray[2],
                        'referral_id' => $removeOrderIdArray[0]
                    ])->delete();

                    // Remove log order time line https://prnt.sc/O-cAFvanxcsM
                    ReferralLog::query()
                        ->where('referral_id', $removeOrderIdArray[0])
                        ->delete();
                }
            }
        }
        $endTime = Carbon::now();
        $diffTime = $endTime->diffInMinutes($startTime);
        log::info('End remove duplicate: ' . $endTime . ' in ' . $diffTime . ' minutes');
        shopSetting(5, ['sca_duplicate_referral_id' => $lastReferral->id]);

        $this->getReferralDuplicate($id);
    }

    public function removeDuplicateConnectCustomer(Carbon $startTime) {
        Log::info('Start remove duplicate connect customer: ' . $startTime->toTimeString());

        $duplicateCustomers = ConnectCustomer::select(
            'shop_id',
            'customer_email',
            DB::raw('MAX(id) as max_id')
        )
        ->groupBy('shop_id', 'customer_email')
        ->havingRaw('COUNT(*) > 1')
        ->get();
        
        foreach ($duplicateCustomers as $customer) {
            $connect = ConnectCustomer::where('shop_id', $customer->shop_id)
                ->where('customer_email', $customer->customer_email)
                ->where('id', '<', $customer->max_id)
                ->delete();

            if ($connect) {
                Log::info('Deleted duplicate connect customer shopId = ' . $customer->shop_id . '. Customer email = ' . $customer->customer_email);
                $this->info('Deleted duplicate connect customer shopId = ' . $customer->shop_id . '. Customer email = ' . $customer->customer_email);
            }
        }

        $endTime = Carbon::now();
        $diffTime = $endTime->diffInMinutes($startTime);
        Log::info('End remove duplicate connect customer: ' . $endTime . ' in ' . $diffTime . ' minutes');
    }

    public function getReferralDuplicate(int $id)
    {
        logger('Start getReferralDuplicate');

        $hookUrl = config('api.mattermost.hooks');
        /** @var MattermostLogService $mattermost */
        $mattermost = app(MattermostLogService::class);

        $referrals = Referral::select(
            'order_id',
            DB::raw('COUNT(id) AS n_referral_id'),
            DB::raw('COUNT(DISTINCT affiliate_id) AS n_uniq_affiliate_id')
        )
        ->where('id', '>=', $id)
        ->whereNotNull('order_id')
        ->where('tracking_type', Referral::TRACKING_TYPE_LINK)
        ->whereNull('refund_id')
        ->groupBy('order_id')
        ->having('n_referral_id', '>', 1)
        ->get();

        $orderDuplicate = '';

        foreach ($referrals as $referral) {
            $orderDuplicate .= $referral->order_id ?? ' ';
        }

        if (!empty($orderDuplicate)) {
            $mattermost->pushMessage($hookUrl, 'Order duplicate: ', $orderDuplicate, 'up-duplicate-order');
        }

        logger('End getReferralDuplicate');
    }
}

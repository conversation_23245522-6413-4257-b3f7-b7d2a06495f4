<?php

namespace App\Console\Commands\CleanData;

use App\Models\ExtendedFramework\Shop;
use App\Models\MerchantSetting;
use App\Services\SendingEmailService\SendingEmailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Secomapp\Models\PlanSubscription;

class DeleteUnverifySES extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean:identity-ses {shop?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    protected $ignoreDomain = [
          'secomapp.com',
          'secomus.com',
          'uppromote.com',
          'mg.uppromote.com',
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->service = app(SendingEmailService::class);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $latestSubscriptions = PlanSubscription::query()
            ->select(DB::raw('MAX(id) as id, shop_id'))
            ->groupBy('shop_id');

        $shops = Shop::query()
            ->select(
        'shops.id',
                'merchant_settings.sending_email as email',
                'plan_subscriptions.plan_id',
                'shops.access_token'
            )
            ->join(DB::raw('(' .  $latestSubscriptions->toSql() . ') as ps'), function ($join) {
                $join->on('shops.id', '=', 'ps.shop_id');
            })
            ->join('plan_subscriptions', 'ps.id', '=', 'plan_subscriptions.id')
            ->join('merchant_settings', 'merchant_settings.shop_id', '=', 'shops.id');

        // run single shop
        if ($this->argument('shop')) {
            $shopId = $this->argument('shop');
            $shops = $shops->where('shops.id', $shopId);
        }
        $progressBar = $this->output->createProgressBar($shops->count());
        $progressBar->start();
        $shops->chunkById(50, function ($shops) use ($progressBar) {
            $listEmailAndDomain = [];
            $shops->each(function ($shop) use ($progressBar, &$listEmailAndDomain) {
                $domain = Str::after($shop->email, '@');
                if (!in_array($domain, $this->ignoreDomain)) {
                    $listEmailAndDomain[] = $domain;
                }

                // delete identity when shop uninstall or free plan
                if (empty($shop->access_token) || $shop->plan_id == 2) {
                    $count = 0;

                    // check all plan free
                    $merchantSettings = MerchantSetting::query()->where('sending_email', $shop->email)->get();
                    foreach ($merchantSettings as $merchantSetting) {
                        $shopFromId = shopFromId($merchantSetting->id);
                        if (helperPlan()->isPlanFree($shopFromId)) {
                            $count++;
                        }
                    }
                    if ($count == count($merchantSettings)) {
                        $this->service->deleteIdentity($shop->email);
                        sleep(1);
                    }
                    $progressBar->advance();
                    return true;
                }

                $listEmailAndDomain[] = $shop->email;
                $progressBar->advance();
            });

            if (!empty($listEmailAndDomain)) {
                # Limit rate: 1 call per second (100 emails)
                $statusIdentity = $this->service->getIdentityStatus($listEmailAndDomain);
                collect($statusIdentity)
                    ->filter(function ($item, $identity) {
                        $this->info('');
                        $this->info('Identity SES: ' . $identity . ' - ' . $item['VerificationStatus']);
                        return $item['VerificationStatus'] == 'Failed';
                    })
                    ->each(function ($emailVerification, $identity) {
                        $this->service->deleteIdentity($identity);
                        sleep(1);
                    });
            }
        }, 'shops.id', 'id');
    }
}

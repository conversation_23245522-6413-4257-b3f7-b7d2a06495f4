<?php

namespace App\Console\Commands;

use App\Models\MarketPlaceCategory;
use App\Models\MarketPlaceListing;
use App\Models\PromoteProduct;
use Illuminate\Console\Command;
use Secomapp\Models\Shop;

class ClearMarketplaceListingDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace-listing:clear-data {--shop_id=0}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Start clear data mkp listing');

        $this->info('Clear data mkp listing uninstall');

        Shop::query()->whereNull('access_token')->select('id')
            ->when(intval($this->option('shop_id')), function ($query) {
                $this->info('Clear data mkp listing shop id:' . intval($this->option('shop_id')));
                $query->where('id', intval($this->option('shop_id')));
            })
            ->chunkById(100, function ($shopsUninstall) {
                $shopsUninstallIds = $shopsUninstall->pluck('id')->toArray();

                $this->info('Clear data of shop ids:' . implode(',', $shopsUninstallIds));

                // delete mkp listing
                MarketPlaceListing::query()->whereIn('shop_id', $shopsUninstallIds)->delete();

                //delete mkp listing product
                PromoteProduct::query()->whereIn('shop_id', $shopsUninstallIds)->delete();

                // delete mkp category
                MarketPlaceCategory::query()->whereIn('shop_id', $shopsUninstallIds)->delete();
            });

        $this->info('Finish clear mkp data');
    }
}

<?php

namespace App\Console;

use App\Models\AutoPaypal\AutoPaypalSetting;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\ScanFreeFeature::class,
        Commands\orderItemsCommand::class,
        Commands\ScanCampaignFeature::class,
        Commands\AutoApproveOrderCommand::class,
        Commands\AutoAddBonusCommand::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('scan:freefeature')->daily()->at('00:00');
        $schedule->command('orderItems:deleteOldOrder')->daily()->at('00:10');
        $schedule->command('affiliate:unblock_limit_offer')->daily()->at('00:02');
        $schedule->command('scan:campaign')->daily()->at('01:00');
        $schedule->command('notifications:clean')->daily()->at('02:00');
        $schedule->command('clean:mails')->wednesdays()->at('04:00');
        $schedule->command('activity:clear')->tuesdays()->at('03:00');
        $schedule->command('auto-tier:reset')->daily()->at('00:00');
        $schedule->command('merchant:weekly-report')->mondays()->at('00:05');
        $schedule->command('marketplace:re-sync-promote-product')->tuesdays()->at('02:00');
        $schedule->command('marketplace:re-sync-promote-product')->fridays()->at('02:00');
        $schedule->command('autoapprove:cron')->dailyAt('00:10');
        $schedule->command('auto-paypal:remind-12-hours-before-pay', ['--today' => now()->toDateString()])->dailyAt('12:00');
        $schedule->command('auto-paypal:remind-2-days-before-pay', ['--today' => now()->toDateString()])->dailyAt('00:00');
        $schedule->command('auto-paypal:pay', ['--today' => now()->toDateString()])->dailyAt(AutoPaypalSetting::PROCESS_PAY_TIME);
        $schedule->command('bonus:auto_add')->daily()->at('00:20');
        $schedule->command('merchant:disconnect_lifetime_commission')->daily()->at('00:10');
        $schedule->command('merchant:auto_approve_order_by_tag')->daily()->at('20:00');
        $schedule->command('remove:duplicate-referral')->everyThirtyMinutes();
        $schedule->command('sending-email:scan-status')->everyTenMinutes();

        # Customize
        $schedule->command('auto-tier:special-reset 64637')
            ->timezone('America/New_York')
            ->dailyAt('00:00');

        $schedule->command('auto-tier:special-reset 164575')
            ->timezone('America/New_York')
            ->dailyAt('00:00');

        $schedule->command('auto-tier:special-reset 133874')
            ->timezone('America/Los_Angeles')
            ->dailyAt('00:00');

        $schedule->command('auto-tier:special-reset 139510')
            ->timezone('America/Los_Angeles')
            ->dailyAt('00:00');

        $schedule->command('auto-tier:special-reset 176125')
            ->timezone('America/Denver')
            ->dailyAt('00:00');

        # End customize

        $schedule->command('analytics:get-total-order-last-7-days')->daily()->at('00:00');
        $schedule->command('analytics:get-total-order-last-30-days')->daily()->at('00:30');

        $schedule->command('generate:1099-form-merchant')->cron('0 0 15-29 1 *');
        $schedule->command('generate:1099-form-affiliate')->cron('0 0 30 1 *');
        $schedule->command('notify:merchant-1099-form-generated')->cron('0 12 15-29 1 *');

        $schedule->command('promote-product:resync-currency')->daily()->at('00:30');

        $schedule->command('marketplace-listing:clear-data')->weekly(Carbon::MONDAY);
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}

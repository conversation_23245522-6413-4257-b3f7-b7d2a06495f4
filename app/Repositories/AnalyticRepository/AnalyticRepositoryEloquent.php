<?php

namespace App\Repositories\AnalyticRepository;

use App\Models\OrderItem;
use App\Models\OrderItemMongoDB;
use App\Models\ProductAnalytic;
use App\Repositories\Base\BaseRepository;
use MongoDB\BSON\UTCDateTime;

class AnalyticRepositoryEloquent extends BaseRepository implements AnalyticRepository
{
    public function getModel(): string
    {
        // Can replace another model
        return OrderItem::class;
    }

    public function getProductAnalytic(int $shopId, array $requestParams): array
    {
        //REMOVE ANALYTIC OLD
        $isNewProductAnalytic = shopSetting($shopId, ProductAnalytic::NEW_PRODUCT_ANALYTIC);
        $aggregateBuilder = [];
        $matchConditional = [
            '$match' => [
                '$or' => [
                    ['shop_id' => $shopId],
                    ['shop_id' => (string)$shopId]
                ]
            ]
        ];

        $aggregateBuilder[]['$addFields'] = [
            'total_sales' => [
                '$multiply' => [
                    $isNewProductAnalytic ? '$quantity' : '$quanity',
                    '$price'
                ]
            ]
        ];

        if (isset($requestParams['affiliate_id'])) {
            $matchConditional['$match']['affiliate_id']['$eq'] = (int)$requestParams['affiliate_id'];
        }

        if (isset($requestParams['is_referral'])) {
            if ($requestParams['is_referral'] == 1) {
                $matchConditional['$match']['affiliate_id']['$exists'] = true;
            }
        }

        $matchConditional['$match']['created_at'] = [
            '$gte' => new UTCDateTime($requestParams['from']),
            '$lte' => new UTCDateTime($requestParams['to'])
        ];

        if ($requestParams['query'] ?? null) {
            $matchConditional['$match']['product_name'] = [
                '$regex' => $requestParams['query'],
                '$options' => 'i'
            ];
        }

        $groupId = [
            'shop_id' => '$shop_id',
            'shop_key' => '$shop_key',
            'product_id' => '$product_id',
        ];

        if ($isNewProductAnalytic) {
            $groupId['product_name'] = '$product_name';
        }

        /** @noinspection SpellCheckingInspection */
        $groupBuilder = [
            '$group' => [
                '_id' => $groupId,
                'shop_id' => [
                    '$first' => '$shop_id'
                ],
                'product_name' => [
                    '$first' => '$product_name',
                ],
                'price' => [
                    '$first' => '$price'
                ],
                'total_quantity' => [
                    '$sum' => $isNewProductAnalytic ? '$quantity' : '$quanity'
                ],
                'img' => [
                    '$first' => '$img'
                ],
                'product_url' => [
                    '$first' => '$product_url'
                ],
                'total_sales' => [
                    '$sum' => '$total_sales'
                ]
            ]
        ];

        if ($requestParams['sort'] ?? null) {
            $sortConditional['$sort'] = [
                $requestParams['sort']['field'] => $requestParams['sort']['dir']
            ];
        }

        if (count($matchConditional)) {
            $aggregateBuilder[] = $matchConditional;
        }

        $aggregateBuilder[] = $groupBuilder;

        //REMOVE ANALYTIC OLD
        $collectionClass = $isNewProductAnalytic
        ? ProductAnalytic::query()
        : OrderItemMongoDB::query();

        $totalRecord = $collectionClass
            ->raw(function ($collection) use ($aggregateBuilder) {
                $query = $aggregateBuilder;
                $query[]['$count'] = 'total_record';
                return $collection->aggregate($query);
            })
            ->first();
        $totalRecord = $totalRecord ? $totalRecord->total_record : 0;

        if (isset($sortConditional)) {
            $aggregateBuilder[] = $sortConditional;
        }

        $page = intval($requestParams['page'] ?? 1);
        $perPage = intval($requestParams['length'] ?? 10);

        $aggregateBuilder[]['$skip'] = ($page - 1) * $perPage;
        $aggregateBuilder[]['$limit'] = $perPage;

        $items = $collectionClass
            ->raw(function ($collection) use ($aggregateBuilder) {
                return $collection->aggregate($aggregateBuilder);
            });

        return [
            'data' => $items,
            'total' => $totalRecord
        ];
    }
}
<?php

namespace App\Listeners\Affiliate;

use App\Constant\SynchronousModelType;
use App\Jobs\MixpanelAnalytics\MixpanelAnalyticsJob;
use App\Jobs\ReSyncAffiliateMarketplaceRankJob;
use App\Jobs\SendMailQueue;
use App\Models\InteractTracking;
use App\Models\ProgramMlm;
use App\Models\Referral;
use App\Models\SendMailLogs;
use App\Services\AffiliateCouponService\AffiliateCouponService;
use App\Services\AffiliateService\AffiliateService;
use App\Services\CustomerIOService\CustomerIOService;
use App\Services\KlaviyoApiNew;
use App\Services\PaymentService\PaymentService;
use App\Services\SendMail;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\AffiliateSetting;
use App\Models\AffiliateCoupon;
use App\Services\KlaviyoApi;
use App\Models\Affiliate;
use Carbon\Carbon;
use App\Models\Program;
use App\Models\VerifyAffiliateEmail;
use Secomapp\Models\Shop;

class AffiliateRegisteredListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {
        $user = $event->user;
        $affSetting = $event->userSetting;
        $merchant = User::where('shop_id', $user->shop_id)->with('setting')->first();
        $merchantSettings = $merchant->setting;
        $checkSendmail = false;
        $program = Program::find($user->program_id);
        $shop = Shop::find($user->shop_id);
        $urlLogin = route('aff.login', ['domain' => $merchant->subdomain]);
        if ($merchantSettings->custom_domain)
            $urlLogin = 'https://' . $merchantSettings->custom_domain . '/login';

        $shopInfo = $shop->info;
        $moneyFormat = !empty($shopInfo->money_format) ? $shopInfo->money_format : '${{amount}}';
        $commissionStructureText = get_commission_type_text($program->commission_type);
        $commissionAmountText = get_commission_text($program->commission_type, $program->rule, $program->commission_amount, $program->advance_amount, $moneyFormat);

        /**
         * Generate custom affiliate link
         * @var AffiliateService $affiliateService
         */
        $affiliateService = app(AffiliateService::class);
        $canUseAutoGenerateCustomLink = $affiliateService->checkAutoGenerateCustomLink($shop, $merchantSettings, false, [$user->id], true);
        $customAffiliateLink = null;
        if ($canUseAutoGenerateCustomLink) {
            $findTag = Affiliate::TAG_AUTO_GENERATE_CUSTOM_LINK;
            $affiliateService->processAutoGenerateCustomLink($shop, $merchantSettings, $findTag, null, false, [$user->id]);

            /** @var AffiliateService $affiliateService */
            $affiliateService = app(AffiliateService::class);
            $customAffiliateLink = $affiliateService->affiliateRepository->findByCondition(['id' => $user->id], ['custom_affiliate_link']); // re-query get custom link
            $customAffiliateLink = $customAffiliateLink->custom_affiliate_link;
        }

        $logo = '';
        if (isset($merchantSettings->affiliate_registration)) {
            if ($merchantSettings->affiliate_registration != null) {
                $affiliateRegistration = $merchantSettings->affiliate_registration;
                if (isset($affiliateRegistration['logo']) && $affiliateRegistration['logo'] != 'none') {
                    $logo = '<img style="max-width: 100%" src="' . asset_s3($affiliateRegistration['logo']) . '" />';
                }
            }
        }
        $affiliateCoupon = AffiliateCoupon::where('affiliate_id', $user->id)->where('shop_id', $user->shop_id)->get();
        $discountCode = '';
        $coupons = [];
        if ($affiliateCoupon) {
            foreach ($affiliateCoupon as $c) {
                $coupons[] = $c->coupon;
            }
            if (!empty($coupons)) {
                $discountCode = implode(", ", $coupons);
            }
        }
        if ($user->status) {
            $emailType = 'approve affiliate';
            $emailTemplate = DB::table('email_template')
                ->where('shop_id', $user->shop_id)
                ->where('slug', 'approve_affiliate')
                ->where('program_id', $user->program_id)
                ->first();

            if (!$emailTemplate) {
                $emailTemplate = DB::table('email_template')
                    ->where('shop_id', $user->shop_id)
                    ->where('slug', 'approve_affiliate')
                    ->first();
            }
            $tags = config('myconfig.email_tags.approve_affiliate');

            $affiliateLink = $merchantSettings->referral_link . '?sca_ref=' . $user->id . '.' . $user->hash_code;
            if ($customAffiliateLink) {
                $defaultAffiliateLinkFull = parse_url($merchantSettings->referral_link);
                $defaultCustomAffiliateLinkDomain = optional($defaultAffiliateLinkFull)['scheme'] . '://' . optional($defaultAffiliateLinkFull)['host'];
                $affiliateLink = $defaultCustomAffiliateLinkDomain . '/' . $customAffiliateLink;
            }

            $replaces = [$user->first_name, $user->last_name, $urlLogin, $discountCode, $affiliateLink, $program->name, $commissionStructureText, $commissionAmountText, $logo, $merchantSettings->brand_name];
            if ($user->verified) {
                $checkSendmail = true;
                if($merchantSettings->klaviyo && helperPlan()->planProfessionalUp($shop)) {
                    if($merchantSettings->klaviyo['status']) {
                        $apiKey = $merchantSettings->klaviyo['api_key'];
                        $klaviyoApi = new KlaviyoApiNew($apiKey);
                        $data = [
                            "data" => [
                                "type" => "event",
                                "attributes" => [
                                    "profile" => [
                                        "data" => [
                                            "type" => "profile",
                                            "attributes" => [
                                                "email" => "$user->email",
                                                "first_name" => $user->first_name,
                                                "last_name" => $user->last_name,
                                                "properties" => [
                                                    'Name - UpPromote' => $user->first_name . ' ' . $user->last_name,
                                                    'Referral Code - UpPromote' => $user->id . '.' . $user->hash_code,
                                                    'Program - UpPromote' => $program->name,
                                                    'Sign Up Source - UpPromote' => $user->registerMethodText($user->register_method),
                                                    'Affiliate Link - UpPromote' => genReferralLink($user, $merchant->setting->referral_link),
                                                    'Affiliate Login Link - UpPromote' => $urlLogin,
                                                    'Commission Structure - UpPromote' => $commissionStructureText,
                                                    'Commission Amount - UpPromote' => $commissionAmountText,
                                                    'First Name - UpPromote' => $user->first_name,
                                                    'Last Name - UpPromote' => $user->last_name,
                                                    'Coupon - UpPromote' => $discountCode
                                                ]
                                            ]
                                        ]
                                    ],
                                    "metric" => [
                                        "data" => [
                                            "type" =>  "metric",
                                            "attributes" => [
                                                "name" => "UpPromote - Approved Affiliate - $merchantSettings->brand_name"
                                            ]
                                        ]
                                    ],
                                    "properties" => [
                                        "first_name" => $user->first_name,
                                        "last_name" => $user->last_name,
                                        "affiliate_link" => genReferralLink($user,$merchantSettings->referral_link),
                                        "affiliate_login_url" => $urlLogin,
                                        "commission_structure" => $commissionStructureText,
                                        "commission_amount" => $commissionAmountText,
                                        "program_name" => $program->name,
                                        "coupon" => $discountCode
                                    ]
                                ]
                            ]
                        ];
                        $klaviyoApi->sendApiKlaviyo($data);
                    }
                }
            }
        } else {
            $emailType = 'pending affiliate';
            $emailTemplate = DB::table('email_template')
                ->where('shop_id', $user->shop_id)
                ->where('slug', 'pending_affiliate')
                ->where('program_id', $user->program_id)
                ->first();
            if (!$emailTemplate) {
                $emailTemplate = DB::table('email_template')
                    ->where('shop_id', $user->shop_id)
                    ->where('slug', 'pending_affiliate')
                    ->first();
            }
            $tags = config('myconfig.email_tags.pending_affiliate');
            $replaces = [$user->first_name, $user->last_name, $logo, $merchantSettings->brand_name];
            $checkSendmail = true;

            //api klaviyo pending affiliate
            if($merchantSettings->klaviyo && helperPlan()->planProfessionalUp($shop)) {
                if($merchantSettings->klaviyo['status']) {
                    $apiKey = $merchantSettings->klaviyo['api_key'];
                    $klaviyoApi = new KlaviyoApiNew($apiKey);
                    $data = [
                        "data" => [
                            "type" => "event",
                            "attributes" => [
                                "profile" => [
                                    "data" => [
                                        "type" => "profile",
                                        "attributes" => [
                                            "email" => "$user->email",
                                            "first_name" => $user->first_name,
                                            "last_name" => $user->last_name,
                                            "properties" => [
                                                'Name - UpPromote' => $user->first_name . ' ' . $user->last_name,
                                                'Referral Code - UpPromote' => $user->id . '.' . $user->hash_code,
                                                'Program - UpPromote' => $program->name,
                                                'Sign Up Source - UpPromote' => $user->registerMethodText($user->register_method),
                                                'Affiliate Link - UpPromote' => genReferralLink($user, $merchant->setting->referral_link),
                                                'Affiliate Login Link - UpPromote' => $urlLogin,
                                                'Commission Structure - UpPromote' => $commissionStructureText,
                                                'Commission Amount - UpPromote' => $commissionAmountText,
                                                'First Name - UpPromote' => $user->first_name,
                                                'Last Name - UpPromote' => $user->last_name,
                                                'Coupon - UpPromote' => $discountCode
                                            ]
                                        ]
                                    ]
                                ],
                                "metric" => [
                                    "data" => [
                                        "type" =>  "metric",
                                        "attributes" => [
                                            "name" => "UpPromote - Pending Affiliate - $merchantSettings->brand_name"
                                        ]
                                    ]
                                ],
                                "properties" => [
                                    "first_name"   => $user->first_name,
                                    "last_name"    => $user->last_name,
                                ]
                            ]
                        ]
                    ];
                    if ($user->facebook) {
                        $data['data']['attributes']['profile']['data']['attributes']['properties']['facebook'] = $user->facebook;
                    }
                    if ($user->instagram) {
                        $data['data']['attributes']['profile']['data']['attributes']['properties']['instagram'] = $user->instagram;
                    }
                    if ($user->youtube) {
                        $data['data']['attributes']['profile']['data']['attributes']['properties']['youtube'] = $user->youtube;
                    }
                    if ($user->twitter) {
                        $data['data']['attributes']['profile']['data']['attributes']['properties']['twitter'] = $user->twitter;
                    }
                    $klaviyoApi->sendApiKlaviyo($data);
                }
            }
        }

        if ($emailTemplate->status && $checkSendmail) {
            $content = html_entity_decode(replaceTagEmail($tags, $replaces, $emailTemplate->content));
            $subject = str_replace('{brand_name}', $merchantSettings->brand_name, html_entity_decode($emailTemplate->subject));
            $content = SendMail::attachUnsubscribeLink($content, $user, SendMail::AFFILIATE);

            try {
                $from = [];
                if ($merchantSettings->verify_status && $merchantSettings->use_sending_email) {
                    // Verified, send by Merchant email
                    $from['address'] = $merchantSettings->sending_email;
                    $from['name'] = $merchantSettings->brand_name;
                } else {
                    // Not verified, send by no-reply email
                    $from['address'] = config('mail.from.address');
                    $from['name'] = $merchantSettings->brand_name;
                }

                dispatch((new SendMailQueue($user->email, $from, $subject, $content, $merchant->shop_id, $emailType))->onQueue('sendmail'));

                // Increase sent count
                $sendLog = SendMailLogs::getLog($merchant->shop_id);
                SendMailLogs::updateLog($sendLog, 1);
            } catch (Exception $e) {
                report($e);
            }
        }

        // Send notification email to merchant
        if ($merchantSettings->notify_new_affiliate) {
            $this->sendNotiToMerchant($merchant, $user->email, $user, $merchantSettings->auto_active_affiliate);
        }

        // Send verify affiliate email
        if ($merchantSettings->verify_affiliate_email && !$user->verified) {
            $this->handelMailVerify($user, $logo, $shop, $program, $merchant, $urlLogin, $commissionStructureText, $commissionAmountText);
        }

        try {
            // Customer IO
            $allAffiliates = Affiliate::whereShopId($user->shop_id)
                ->where('email', '!=', $merchant->email)
                ->count();

            $activeAffiliate = Affiliate::whereShopId($user->shop_id)
                ->whereStatus(Affiliate::ACTIVE_AFFILIATE)->count();
            /**
             * @var CustomerIOService $customerIOService
             */
            $shop = $user->shop;
            $customerIOService = app(CustomerIOService::class);
            $customer = [];
            if ($allAffiliates == 1) {
                $customer['aff_affiliate_registered'] = 1;
            }
            $customer['aff_affiliate_total'] = $activeAffiliate;
            $customerIOService->update($shop,$customer);
        }catch (Exception $ex){
            report($ex);
        }

        $this->createSetting($user, $affSetting);

        if (in_array($user->shop_id, [34401, 34392])) {
            logger('Sync affiliate reg form shopId1 = '.$user->shop_id.'; email = '.(!empty($user->email) ? $user->email : ''));
        }

        $this->handleMultishop($user, $customAffiliateLink, $affSetting);
        if ($user->parent_id != 0 && $user->status == 1) {
            $this->recruitmentBonus($user->parent_id, $merchantSettings, $shopInfo);
            $affiliateActive = $event->user;
            $affiliateActive->is_first_approve_recruitment_bonus = 1;
            $affiliateActive->save();
        }

        try {
            if ($user->marketplace_affiliate_profile) {
                $userEmail = strtolower($user->email);
                $userIfExist = DB::connection('mysql2')->table('users')->whereRaw('lower(email) = ?', $userEmail)->select('id')->first();
                if ($userIfExist) {
                    Affiliate::where('id', $user->id)->update(['marketplace_user_id' => $userIfExist->id, 'apply_directly_via_mkp' => true]);
                }
            }
        } catch (Exception $e) {
            report($e);
        }

        if($event->user && ($event->user->marketplace_user_id || $event->user->marketplace_affiliate_profile)) dispatch(new ReSyncAffiliateMarketplaceRankJob(['affId' => $event->user->id]));
        try {
            if ($merchantSettings->sync_klaviyo_approved_affiliate) {
                if ($merchantSettings->auto_active_affiliate) {
                    $this->syncKlaviyo($user, $merchant,$shop);
                }
            } else {
                $this->syncKlaviyo($user, $merchant,$shop);
            }
        } catch (Exception $e) {
            report($e);
        }

        $this->trackEventMixpanel($user, $program, $merchant, $merchantSettings);
        $this->handleTrackEventMarketplace($user, $merchant, $shop);
    }

    protected function sendNotiToMerchant($merchant, $affiliateEmail, $affiliate, $autoApproved)
    {
        $from = config('mail.from');
        $customFrom = [
            'address' => '<EMAIL>',
            'name' => 'Emma',
        ];

        if ($autoApproved && $affiliate->status) {
            $emailType = 'new auto approved affiliate - merchant';
            $template = config('myconfig.email_notice.new_auto_approved_affiliate');
            if (!empty($affiliate->marketplace_user_id) && $affiliate->marketplace_user_id != 0) {
                $template = config('myconfig.email_notice.new_auto_approved_affiliate_mkp');
                $from = $customFrom;
            }
        } else {
            $emailType = 'new affiliate - merchant';
            $template = config('myconfig.email_notice.new_affiliate');
            if (!empty($affiliate->marketplace_user_id) && $affiliate->marketplace_user_id != 0) {
                $template = config('myconfig.email_notice.new_affiliate_mkp');
                $from = $customFrom;
            }
        }

        $subject = str_replace('{brand_name}', $merchant->setting->brand_name, $template['subject']);
        $subject = str_replace('{name}', "$affiliate->first_name $affiliate->last_name", $subject);
        $checkCustomizeShop = in_array($merchant->shop->shop, config('myconfig.customize.customize_list')) ?
            (($merchant->shop->redirect_domain && $merchant->shop->customize_name)
                ? $merchant->shop->redirect_domain : config('myconfig.app.app_uppromote_react_url'))
            : config('myconfig.app.app_uppromote_react_url');

        $content = str_replace(
            [
                '{name}',
                '{affiliate_email}',
                '{checkDomain}'
            ],
            [
                $merchant->first_name,
                $affiliateEmail,
                $checkCustomizeShop
            ],
            view($template['content'])->render()
        );
        $content = SendMail::attachUnsubscribeLink($content, $merchant, SendMail::MERCHANT);

        // Send mail
        try {
            dispatch((new SendMailQueue($merchant->setting->sending_email, $from, $subject, $content, $merchant->shop_id, $emailType))->onQueue('sendmail'));

            // Increase sent count
            $sendLog = SendMailLogs::getLog($merchant->shop_id);
            SendMailLogs::updateLog($sendLog, 1, false, true);
        } catch (Exception $e) {
            report($e);
        }
    }

    protected function handelMailVerify($user, $logo, $shop, $program, $merchant, $urlLogin, $commissionStructureText, $commissionAmountText)
    {
        $emailTemplate = DB::table('email_template')->where('shop_id', $user->shop_id)
            ->where('slug', 'verify_affiliate')
            ->where('program_id', $user->program_id)
            ->first();
        if (!$emailTemplate) {
            $emailTemplate = DB::table('email_template')->where('shop_id', $user->shop_id)
                ->where('slug', 'verify_affiliate')
                ->first();
        }
        $verifyAffiliateEmail = VerifyAffiliateEmail::create([
            'affiliate_id' => $user->id,
            'verify_code' => str_random(40)
        ]);
        $verifyLink = route('aff.verify_affiliate_email', ['domain' => $merchant->subdomain, 'token' => $verifyAffiliateEmail->verify_code]);
        if ($merchant->setting->custom_domain) {
            $verifyLink = 'https://' . $merchant->setting->custom_domain . '/' . $merchant->subdomain . '/verify_email/' . $verifyAffiliateEmail->verify_code;
        }
        //api klaviyo
        $klaviyoSetting = $merchant->setting->klaviyo;
        if($klaviyoSetting &&  helperPlan()->planProfessionalUp($shop)) {
            if($klaviyoSetting['status']) {
                $apiKey = $klaviyoSetting['api_key'];
                $klaviyoApi = new KlaviyoApiNew($apiKey);
                $data = [
                    "data" => [
                        "type" => "event",
                        "attributes" => [
                            "profile" => [
                                "data" => [
                                    "type" => "profile",
                                    "attributes" => [
                                        "email" => "$user->email",
                                        "first_name" => $user->first_name,
                                        "last_name" => $user->last_name,
                                        "properties" => [
                                            'Name - UpPromote' => $user->first_name . ' ' . $user->last_name,
                                            'Referral Code - UpPromote' => $user->id . '.' . $user->hash_code,
                                            'Program - UpPromote' => $program->name,
                                            'Sign Up Source - UpPromote' => $user->registerMethodText($user->register_method),
                                            'Affiliate Link - UpPromote' => genReferralLink($user, $merchant->setting->referral_link),
                                            'Affiliate Login Link - UpPromote' => $urlLogin,
                                            'Commission Structure - UpPromote' => $commissionStructureText,
                                            'Commission Amount - UpPromote' => $commissionAmountText,
                                            'First Name - UpPromote' => $user->first_name,
                                            'Last Name - UpPromote' => $user->last_name
                                        ]
                                    ]
                                ]
                            ],
                            "metric" => [
                                "data" => [
                                    "type" =>  "metric",
                                    "attributes" => [
                                        "name" => "UpPromote - Affiliate Verification - " .$merchant->setting->brand_name
                                    ]
                                ]
                            ],
                            "properties" => [
                                "first_name"   => $user->first_name,
                                "last_name"    => $user->last_name,
                                "verify_link" => $verifyLink,
                            ]
                        ]
                    ]
                ];
                if ($user->facebook) {
                    $data['data']['attributes']['profile']['data']['attributes']['properties']['facebook'] = $user->facebook;
                }
                if ($user->instagram) {
                    $data['data']['attributes']['profile']['data']['attributes']['properties']['instagram'] = $user->instagram;
                }
                if ($user->youtube) {
                    $data['data']['attributes']['profile']['data']['attributes']['properties']['youtube'] = $user->youtube;
                }
                if ($user->twitter) {
                    $data['data']['attributes']['profile']['data']['attributes']['properties']['twitter'] = $user->twitter;
                }

                if ($user->coupons) {
                    $coupons = [];
                    foreach ($user->coupons as $c) {
                        $coupons[] = $c->coupon;
                    }
                    if (!empty($coupons)) {
                        $data['data']['attributes']['profile']['data']['attributes']['properties']['Coupon - UpPromote'] = implode(",", $coupons);
                    }
                }
                $klaviyoApi->sendApiKlaviyo($data);
            }
        }
        if ($emailTemplate->status == 1) {
            $brandName = $merchant->setting->brand_name;
            $tags = config('myconfig.email_tags.verify_affiliate');
            $replaces = [$user->first_name, $user->last_name, $verifyLink, $logo, $brandName];
            $subject = replaceTagEmail($tags, $replaces, $emailTemplate->subject);
            $content = html_entity_decode(replaceTagEmail($tags, $replaces, $emailTemplate->content));
            $content = SendMail::attachUnsubscribeLink($content, $user, SendMail::AFFILIATE);
            $from = [];
            if ($merchant->setting->verify_status && $merchant->setting->use_sending_email) {
                // Verified, send by Merchant email
                $from['address'] = $merchant->setting->sending_email;
                $from['name'] = $brandName;
            } else {
                // Not verified, send by no-reply email
                $from['address'] = config('mail.from.address');
                $from['name'] = $brandName;
            }
            // Send mail
            try {
                dispatch((new SendMailQueue($user->email, $from, $subject, $content, $user->shop_id, 'verify affiliate'))->onQueue('sendmail'));

                // Increase sent count
                $sendLog = SendMailLogs::getLog($user->shop_id);
                SendMailLogs::updateLog($sendLog, 1);
            } catch (Exception $e) {
                report($e);
            }
        }
    }

    protected function createSetting($user, $affSetting = null, $affAfter = null, $sync = false)
    {
        $affPaymentMethod = isSettingPaymentDefault($user, $affAfter, $sync);
        $setting = new AffiliateSetting;
        $setting->affiliate_id = $user->id;
        $setting->payment_method = $affPaymentMethod;
        $isSyncPaymentInfo = null;
        if ($sync && isset($affAfter) && $affAfter->setting->payment_method == $affPaymentMethod )
        {
            $isSyncPaymentInfo = $affSetting['payment_info'];
        }
        $setting->payment_info = $isSyncPaymentInfo;

        //custom cho thang 9743
        if ($user->shop_id == 9743) {
            foreach ($user->additional_info as $v) {
                if ($v['label'] == "Stripe Account") {
                    $setting->payment_info = ['other' => $v['value']];
                }
            }
            $setting->payment_method = "other";
        }
        $setting->save();

        if ($affSetting && !$sync) {
            if(isset($affSetting['payment_method']) && isset($affSetting['payment_info'])){
                $paymentService = app(PaymentService::class);
                $paymentSupport = $paymentService->getPaymentSupport($user->shop_id, $user->program_id);

                if(!in_array($affSetting['payment_method'], collect($paymentSupport)->keys()->toArray())){
                    unset($affSetting['payment_method']);
                    unset($affSetting['payment_info']);
                }
            }

            if($affSetting) $setting->update($affSetting);
        }
    }

    protected function syncKlaviyo($user, $merchant,$shop)
    {
        if(helperPlan()->planProfessionalUp($shop)) {
            if ($merchant->setting->klaviyo) {
                if ($merchant->setting->klaviyo['status']) {
                    $apiKey = $merchant->setting->klaviyo['api_key'];
                    $listId = $merchant->setting->klaviyo['list_id'];
                    $klaviyoApi = new KlaviyoApi($apiKey);
                    $idSearch = $klaviyoApi->searchProfile($user->email);
                    if($idSearch) {
                        $klaviyoApi->addMembersFromIdSearch($listId, $idSearch);
                    }
                    if ($user->status == 0) {
                        if($merchant->setting->sync_klaviyo_approved_affiliate) {
                            $idSearch = $klaviyoApi->searchProfile($user->email);
                            if($idSearch) {
                                $klaviyoApi->deleteMembers($listId, $idSearch);
                            }
                        }
                    }
                }
            }
        }
    }

    protected function checkExistAndGetRandomHashcode()
    {
        while (1) {
            $hashcode = generateRandomString();
            $check = Affiliate::where('hash_code', $hashcode)->first();
            if (!$check) {
                break;
            }
        }
        return $hashcode;
    }

    protected function handleMultishop($affiliate, $customAffiliateLink, $affSetting = null)
    {
        dispatchSyncDataSignal(
            SynchronousModelType::AFFILIATES_AND_INFORMATION,
            SynchronousModelType::AFFILIATES_AND_INFORMATION_ACTION['ON_CREATED']
        )
            ->setArguments($affiliate->id, null, $customAffiliateLink, 'reg-form')
            ->setIdentity($affiliate->shop_id)
            ->dispatch();
    }

    /**
     * Create coupon code for child shops
     *
     * @param $user
     * @param $parent
     */
    protected function createCoupon($user, $parent)
    {
        /** @var AffiliateCouponService $affiliateCouponService */
        $affiliateCouponService = app(AffiliateCouponService::class);
        $affiliateCouponService->generateCouponForConnectedShop($user, $parent);
    }

    public function recruitmentBonus($affiliateId, $merchantSetting, $shopInfo)
    {
        $newRef = new Referral;
        $affiliate = Affiliate::find($affiliateId);
        $proMlm = ProgramMlm::where('program_id', $affiliate->program_id)->first();
        $currentTime = Carbon::now();
        if (!empty($affiliate) && $proMlm && $proMlm->is_enable && $proMlm->recruitment_bonus != 0) {
            $newRef->shop_id = $affiliate->shop_id;
            $newRef->affiliate_id = $affiliateId;
            $newRef->commission = $proMlm->recruitment_bonus;
            $newRef->program_id = $affiliate->program_id;
            $newRef->total = 0;
            $newRef->tracking_type = 8;
            $newRef->comment = 'Bonus for recruiting another affiliate via the network link.';
            $newRef->status = $merchantSetting->auto_approve_order;
            $newRef->created_at_year = $currentTime->year;
            $newRef->created_at_month = $currentTime->month;
            $newRef->currency = $shopInfo->currency;
            $newRef->save();
        }
    }

    /**
     * @param $affiliate
     * @param $program
     * @param $userMerchant
     * @param $merchantSettings
     * @return void
     */
    private function trackEventMixpanel($affiliate, $program, $userMerchant, $merchantSettings) {
        /******** Start Mixpanel ********/
        try {
            $shop = Shop::findOrFail($affiliate->shop_id);
            $shopInfo = DB::table('shop_infos')->where('shop_id', $affiliate->shop_id)->first();
            $inputs = [
                'shop' => $shop,
                'shop_infos' => $shopInfo,
                'settings' => $merchantSettings,
                'users' => $userMerchant
            ];

            $currentPathName = $clientData['current_pathname'] ?? '/' . $userMerchant->subdomain . ($program->id ? '/register?p=' . $program->id : '/register');

            dispatch(new MixpanelAnalyticsJob('user-profile', $affiliate->shop_id, $inputs, null, session()->has('sudo')));
            dispatch(new MixpanelAnalyticsJob('track-event', $affiliate->shop_id, [
                'screen_size' => !empty($_COOKIE['screenSize']) ? $_COOKIE['screenSize'] : null,
                'operating_system' => !empty($_COOKIE['operationSystem']) ? $_COOKIE['operationSystem'] : null,
                'Affiliate email' => $affiliate->email ?? null,
                'Program id' => $program->id ?? null,
                'Signup source' => $affiliate->register_method == Affiliate::REG_BY_REG_PAGE && $affiliate->marketplace_user_id > 0
                                ? 'Marketplace' : registerMethodAff($affiliate->register_method)
            ], [
                'shop' => $shop,
                'event_name' => 'Affiliate signed up',
                'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                'params' => !empty(request()->all()) ? request()->all() : null,
                'current_pathname' => $currentPathName,
            ]));
        } catch (Exception $exception) {
            logger($exception->getMessage() . ' - ' . $exception->getMessage());
        }
        /******** End Mixpanel ********/
    }

    private function handleTrackEventMarketplace($user, $merchant, $shop)
    {
        if(isset($user->marketplace_user_id) && $user->marketplace_user_id > 0) {
            InteractTracking::create([
                'event_name' => 'Join Offer',
                'marketplace_user_id' => $user->marketplace_user_id,
                'path_name' => "$merchant->subdomain/register",
                'properties' => json_decode(json_encode(["Offer_id" => $user->program_id, "Shop_domain" => $shop->shop])
                ),
            ]);
        }
    }
}

<?php

namespace App\Listeners\Merchant;

use App\Events\Merchant\AssignAffiliateCoupon;
use App\Jobs\MixpanelAnalytics\MixpanelAnalyticsJob;
use App\Jobs\ReSyncAffiliateMarketplaceRankJob;
use App\Jobs\SendMailQueue;
use App\Jobs\SyncCustomerShopifyJob;
use App\Jobs\SyncKlaviyoBulkApproveJob;
use App\Models\AffiliateCoupon;
use App\Models\BonusSetting;
use App\Models\Gift;
use App\Models\GiftAffiliate;
use App\Models\MerchantSetting;
use App\Models\ProgramMlm;
use App\Models\PurchasePopup;
use App\Models\Referral;
use App\Models\SendMailLogs;
use App\Models\ShopifyCustomerConvert;
use App\Services\CustomerIOService\CustomerIOService;
use App\Services\GiftService\GiftService;
use App\Services\MultipleShopService\MultipleShopService;
use App\Services\SendMail;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Services\MerchantSendMail;
use App\Models\ShopRelationship;
use App\Models\Program;
use App\Models\Affiliate;
use Illuminate\Support\Facades\Hash;
use Secomapp\Models\ShopInfo;

class ActiveAffiliateListener
{
    public function handle($event)
    {
        $aff = $event->aff;
        $originAffiliate = $aff->toArray();
        if ($aff->register_method == Affiliate::REG_BY_CUSTOMER_REFERRAL) return;

        $merchant = Auth::user();
        $shop = $merchant->shop;
        $shopInfo = $shop->info;
        $firstApprove = $event->firstApprove;
        $settings = $merchant->setting;
        $urlLogin = route('aff.login', ['domain' => $merchant->subdomain]);

        if ($settings->custom_domain) {
            $urlLogin = 'https://' . $settings->custom_domain . '/login';
        }

        $program = $aff->program;
        // handle multi shop
        $status = $aff->status;
        $checkMultiShop = ShopRelationship::where('children_shop', $aff->shop_id)->first();
        if ($checkMultiShop) {
            $aff = ShopRelationship::join('affiliates', 'shop_relationship.children_shop', '=', 'affiliates.shop_id')
                ->where('shop_relationship.parent_shop', $checkMultiShop->parent_shop)
                ->where('affiliates.email', $aff->email)
                ->select('affiliates.*')
                ->orderBy('created_at')
                ->first();

            if ($merchant->setting->custom_domain) {
                $urlLogin = 'https://' . $merchant->setting->custom_domain . '/login';
            } else {
                $urlLogin = route('aff.login', ['domain' => $merchant->subdomain]);
            }

            $aff->status = $status;
        }
        $affOrigin = $event->aff;
        if ($aff->register_method == Affiliate::REG_BY_PURCHASE_POPUP) {
            $this->processSendMailPopPurchaseAndConvertCustomer($affOrigin, $merchant, $urlLogin, 'purchase popup');
        } else if ($aff->register_method == Affiliate::REG_BY_CONVERT_CUSTOMER) {
            $this->processSendMailPopPurchaseAndConvertCustomer($affOrigin, $merchant, $urlLogin, 'convert customer');
        } else {
            if ($aff->status) {
                $type = 'approve_affiliate';
                $commissionStructureText = get_commission_type_text($program->commission_type);
                $commissionAmountText = get_commission_text($program->commission_type, $program->rule, $program->commission_amount, $program->advance_amount, session('money_format'));
                $affiliateCoupon = AffiliateCoupon::where('affiliate_id', $aff->id)->where('shop_id', $aff->shop_id)->get();

                $discountCode = '';
                if ($affiliateCoupon) {
                    foreach ($affiliateCoupon as $c) {
                        $coupons[] = $c->coupon;
                    }
                    if (!empty($coupons)) {
                        $discountCode = implode(", ", $coupons);
                    }
                }

                $referralLink = $settings->referral_link . '?sca_ref=' . $aff->id . '.' . $aff->hash_code;
                if ($aff->custom_affiliate_link && helperPlan()->planProfessionalUp()) {
                    $referralLink = $settings->referral_link . '/' . $aff->custom_affiliate_link;
                }
                $replaces = [$aff->first_name, $aff->last_name, $urlLogin, $discountCode, $referralLink, $program->name, $commissionStructureText, $commissionAmountText];
                if ($aff->verified) {
                    if ($settings->klaviyo && helperPlan()->planProfessionalUp()) {
                        if ($settings->klaviyo['status']) {
                            dispatch(new SyncKlaviyoBulkApproveJob($affOrigin, $merchant, $program,null, $discountCode, $type));
                        }
                    }

                    MerchantSendMail::send($merchant, $aff, $type, $replaces);
                }
            } else if ($aff->status == Affiliate::DEACTIVATE_AFFILIATE && $aff->is_pending == Affiliate::IS_NOT_PENDING) {
                if ($aff->verified) {
                    $type = 'denied_affiliate';
                    if ($settings->klaviyo && helperPlan()->planProfessionalUp()) {
                        if ($settings->klaviyo['status']) {
                            dispatch(new SyncKlaviyoBulkApproveJob($affOrigin, $merchant, $program, null,null,$type));
                        }
                    }
                    $replaces = [$aff->first_name, $aff->last_name];
                    MerchantSendMail::send($merchant, $aff, $type, $replaces);
                }
            }
        }
        if ($aff->status) { //auto send gift
            if (helperPlan()->planProfessionalUp()) {
                $this->autoSendGift($event->aff);
            }

            $this->trackEventMixpanel($aff, $shop);
        }

        $this->activeAffiliateMultiShop($aff);
        //kiểm tra và add bonus cho affiliate khi giới thiệu affiliate khác.
        if( $firstApprove == 0 && $aff->parent_id != 0 && $aff->status == 1) {
            $this->recruitmentBonus($aff->parent_id, $settings, $shopInfo);
            $affiliateActive = $event->aff;
            $affiliateActive->is_first_approve_recruitment_bonus = 1;
            $affiliateActive->save();
        }

        try {
            if (helperPlan()->planProfessionalUp()) {
                /** @var Collection $bonuses */
                $bonuses = BonusSetting::where('target_amount', 0)
                    ->where('shop_id', $aff->shop_id)
                    ->get();

                $allBonusIds = $bonuses->pluck('id')->toArray();

                $createdBonusIds = Referral::where('affiliate_id', $aff->id)
                    ->where('shop_id', $aff->shop_id)
                    ->whereIn('bonus_id', $allBonusIds)
                    ->groupBy('bonus_id')
                    ->pluck('bonus_id')
                    ->toArray();

                $bonusesNotCreatedYet = $bonuses->filter(function ($bonus) use ($createdBonusIds) {
                    return !in_array($bonus->id, $createdBonusIds);
                });

                $bonusesOfAffiliateProgram = $bonusesNotCreatedYet->filter(function ($bonus) use ($originAffiliate) {
                    $programIds = is_array($bonus->program_id)
                        ? $bonus->program_id
                        : json_decode($bonus->program_id);
                    if (!$bonus->program_id) return true;
                    return in_array($originAffiliate['program_id'], $programIds);
                });

                $this->performanceBonus($aff, $settings, $bonusesOfAffiliateProgram);

                dispatch(new SyncKlaviyoBulkApproveJob($affOrigin, $merchant, $program, null,null,null));
            }
            if ($aff->shop_id == 37230 && $aff->status == Affiliate::ACTIVE_AFFILIATE) { // custom shopID = 37230
                $this->approveSyncCustomer37230($aff, $shop);
            }
        } catch (Exception $e) {
            report($e);
        }

        if ($event->aff && $event->aff->marketplace_user_id) {
            dispatch(new ReSyncAffiliateMarketplaceRankJob(['affId' => $event->aff->id]));
        }

        try {
            $countActiveAffiliate = Affiliate::whereShopId($aff->shop_id)->whereStatus(Affiliate::ACTIVE_AFFILIATE)->count();
            /**
             ** @var CustomerIOService $customerIoService
             */
            $customerIoService = app(CustomerIOService::class);
            $customerIoService->update(Auth::user()->shop, [
                'aff_affiliate_total' => $countActiveAffiliate
            ]);
        } catch (Exception $ex) {
            report($ex);
        }

        /******** Start Mixpanel ********/
        try {
            $inputs = [
                'shop' => $shop,
                'shop_infos' => $shopInfo,
                'settings' => $settings,
                'users' => $merchant
            ];

            dispatch(new MixpanelAnalyticsJob('user-profile', $shop->id, $inputs, null, session()->has('sudo')));
        } catch (Exception $exception) {
            logger($exception->getMessage() . ' - ' . $exception->getMessage());
        }
        /******** End Mixpanel ********/
    }

    /**
     * @param $affiliate
     * @param $shop
     * Start Mixpanel
     * @return void
     */
    private function trackEventMixpanel($affiliate, $shop)
    {
        try {
            $program = Program::find($affiliate->program_id);
            dispatch(new MixpanelAnalyticsJob('track-event', $affiliate->shop_id, [
                'Current page' => 'Affiliates',
                'Screen size' => !empty($_COOKIE['screenSize']) ? $_COOKIE['screenSize'] : null,
                'Operation system' => !empty($_COOKIE['operationSystem']) ? $_COOKIE['operationSystem'] : null,
                'Affiliate email' => $affiliate->email ?? null,
                'Program Name' => $program->name ?? null,
                'Signup source' => registerMethodAff($affiliate->register_method)
            ], [
                'shop' => $shop,
                'event_name' => 'Active affiliate',
                'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                'params' => !empty(request()->all()) ? request()->all() : null,
            ], session()->has('sudo')));
        } catch (Exception $exception) {
            logger($exception->getMessage() . ' - ' . $exception->getMessage());
        }
    }

    private function activeAffiliateMultiShop($affiliate)
    {
        /** @var MultipleShopService $multipleShopService */
        $multipleShopService = app(MultipleShopService::class);
        $canUseMultipleShop = $multipleShopService->canUseMultipleShop($affiliate->shop_id);
        if ($canUseMultipleShop) {
            $shopRelationship = $multipleShopService->getShopRelationship($affiliate->shop_id, $affiliate->shop_id);
            foreach ($shopRelationship as $s) {
                DB::table('affiliates')
                    ->where('email', $affiliate->email)
                    ->where('shop_id', $s['shop_id'])
                    ->update(['status' => $affiliate->status, 'is_pending' => 0]);
            }
        }
    }

    private function processSendMailPopPurchaseAndConvertCustomer($aff, $merchant, $urlLogin, $type)
    {
        if (!$aff->first_approve && $aff->status) {
            $program = Program::find($aff->program_id);
            $commissionStructure = get_commission_type_text($program->commission_type);
            $shopInfo = ShopInfo::where('shop_id', $aff->shop_id)->select('money_format')->first();
            if ($program->commission_type != 2) {
                $commission_amount = currnency_format(strip_tags($shopInfo->money_format), $program->commission_amount);
            } else {
                $commission_amount = ($program->commission_amount) . '%';
            }
            $affiliateCoupon = AffiliateCoupon::where('affiliate_id', $aff->id)->where('shop_id', $aff->shop_id)->get();
            $discountCode = '';
            if ($affiliateCoupon) {
                foreach ($affiliateCoupon as $c) {
                    $coupons[] = $c->coupon;
                }
                if (!empty($coupons)) {
                    $discountCode = implode(", ", $coupons);
                }
            }
            $tempPassword = generateRandomString();
            $referralLink = $merchant->setting->referral_link . '?sca_ref=' . $aff->id . '.' . $aff->hash_code;
            $affiliate = Affiliate::find($aff->id);
            $affiliate->password = Hash::make($tempPassword);
            $affiliate->first_approve = 1;
            $affiliate->save();
            $finds = ['{customer_name}', '{first_name}', '{last_name}', '{affiliate_link}', '{commission_amount}', '{commission_structure}', '{affiliate_login_link}', '{temporary_password}', '{brand_name}', '{coupon}', '{logo}'];
            $replaces = [$aff->first_name, $aff->first_name, $aff->last_name, $referralLink, $commission_amount, $commissionStructure, $urlLogin, $tempPassword, $merchant->setting->brand_name, $discountCode];
            if ($merchant->setting->affiliate_registration != null) {
                if (is_array($merchant->setting->affiliate_registration)) {
                    $settingRegistration = $merchant->setting->affiliate_registration;
                    if (isset($settingRegistration['logo'])) {
                        if ($settingRegistration['logo'] != 'none') {
                            $replaces[] = '<img style="width: 100%" src="' . asset_s3($settingRegistration['logo']) . '" />';
                        } else {
                            $replaces[] = '';
                        }
                    } else {
                        $replaces[] = '';
                    }
                } else {
                    $settingRegistration = json_decode($merchant->setting->affiliate_registration);
                    if (isset($settingRegistration->logo)) {
                        if ($settingRegistration->logo != 'none') {
                            $replaces[] = '<img style="max-width: 100%" src="' . asset_s3($settingRegistration->logo) . '" />';
                        } else {
                            $replaces[] = '';
                        }
                    } else {
                        $replaces[] = '';
                    }
                }
            } else {
                $replaces[] = '';
            }
            if ($type == 'purchase popup') {
                $purchase = PurchasePopup::where('shop_id', $aff->shop_id)->first();
                if ($purchase->promotion_method == 2) {
                    $contentEmail = str_replace($finds, $replaces, $purchase->email_coupon);
                } else {
                    $contentEmail = str_replace($finds, $replaces, $purchase->email);
                }
                $subject = $purchase->subject;
                $isStatusEmail = $purchase->email_status;
            } else {
                $convertCustomer = ShopifyCustomerConvert::where('shop_id', $aff->shop_id)->first();
                $contentEmail = str_replace($finds, $replaces, $convertCustomer->email);
                $subject = $convertCustomer->subject;
                $isStatusEmail = $convertCustomer->email_status;
            }
            $contentEmail = SendMail::attachUnsubscribeLink($contentEmail, $aff, SendMail::AFFILIATE);
            if (!$subject) {
                $subject = "Your affiliate account on {brand_name} has been created, check it out!";
            }
            $subject = str_replace(['{brand_name}'], [$merchant->setting->brand_name], $subject);
            $from = [];
            if ($merchant->setting->verify_status && $merchant->setting->use_sending_email) {
                $from['address'] = $merchant->setting->sending_email;
                $from['name'] = $merchant->setting->brand_name;
                // Verified, send by Merchant email
            } else {
                $from['address'] = config('mail.from.address');
                $from['name'] = $merchant->setting->brand_name;
                // Not verified, send by no-reply email
            }
            $to = $aff->email;
            //send mail
            if ($isStatusEmail) {
                dispatch((new SendMailQueue($to, $from, $subject, $contentEmail, $aff->shop_id, $type))->onQueue('sendmail'));
            }

            // Increase sent count
            $sendLog = SendMailLogs::getLog($aff->shop_id);
            SendMailLogs::updateLog($sendLog, 1);

            //api klaviyo metric
            if ($merchant->setting->klaviyo && helperPlan()->planProfessionalUp()) {
                if ($merchant->setting->klaviyo['status']) {
                    dispatch(new SyncKlaviyoBulkApproveJob($aff, $merchant, $program, $tempPassword, $discountCode, $type));
                }
            }
        }
    }


    public function recruitmentBonus($affiliateId, $merchantSetting, $shopInfo)
    {
        $affiliate = Affiliate::find($affiliateId);

        if ($affiliate) {
            $proMlm = ProgramMlm::where('program_id', $affiliate->program_id)->first();
            $currentTime = Carbon::now();
            if ($proMlm && $proMlm->is_enable && $proMlm->recruitment_bonus != 0) {
                $newRef = new Referral;
                $newRef->shop_id = $affiliate->shop_id;
                $newRef->affiliate_id = $affiliateId;
                $newRef->commission = $proMlm->recruitment_bonus;
                $newRef->program_id = $affiliate->program_id;
                $newRef->total = 0;
                $newRef->tracking_type = 8;
                $newRef->comment = 'Bonus for recruiting another affiliate via the network link.';
                $newRef->status = $merchantSetting->auto_approve_order;
                $newRef->created_at_year = $currentTime->year;
                $newRef->created_at_month = $currentTime->month;
                $newRef->currency = $shopInfo->currency;
                $newRef->save();
            }
        }
    }

    public function performanceBonus($affiliate, $merchantSetting, $bonuses)
    {
        $affiliate = Affiliate::find($affiliate->id);
        if ($affiliate->status == 1 && $affiliate->is_pending == 0) {
            $shopInfo = ShopInfo::query()->where('shop_id', $affiliate->shop_id)->select('currency')->first();

            foreach ($bonuses as $bonus) {
                if ($affiliate->created_at >= $bonus->created_at) {
                    try {
                        $currentTime = Carbon::now();
                        $referral = new Referral;
                        $referral->shop_id = $affiliate->shop_id;
                        $referral->affiliate_id = $affiliate->id;
                        $referral->commission = $bonus->bonus_amount;
                        $referral->program_id = $affiliate->program_id;
                        $referral->total = 0;
                        $referral->tracking_type = Referral::TRACKING_TYPE_PERFORMENT_BONUS;
                        $referral->comment = 'Bonus for joining the affiliate program';
                        $referral->bonus_id = $bonus->id;
                        $referral->status = $merchantSetting->auto_approve_order && $merchantSetting->delay_auto_approve_order == 0 ? $merchantSetting->auto_approve_order : 0;
                        $referral->created_at_year = $currentTime->year;
                        $referral->created_at_month = $currentTime->month;
                        $referral->currency = $shopInfo->currency;
                        $referral->save();
                    } catch (Exception $e) {
                        report($e);
                    }
                }
            }
        }
    }

    public function approveSyncCustomer37230($affiliate, $shop)
    {
        dispatch((new SyncCustomerShopifyJob([$affiliate], $shop->shop, $shop->id, $shop->access_token))->onQueue('integration-customer-shopify'));
    }

    public function autoSendGift($aff)
    {
        $merchant = Auth::user();
        $merchantSetting = MerchantSetting::where([
            'shop_id' => auth()->user()->shop_id,
        ])->select('id', 'shop_id', 'custom_domain', 'gift_email_subject', 'gift_email_content', 'sending_email', 'verify_status', 'use_sending_email', 'sending_email', 'brand_name')->first();

        $affCreateDate = Carbon::createFromTimestamp(intval($aff->created_at))->toDateTimeString();
        $giftSettings = Gift::where('shop_id', $aff->shop_id)
            ->where('is_automatic_send', 1)
            ->where('created_at', '<=', $affCreateDate)
            ->where('program_id', 'like', '%"' . $aff->program_id . '"%')
            ->get();
        /**
         * @var GiftService $giftService
         */
        $giftService = app(GiftService::class);

        if (isset($giftSettings)) {
            foreach ($giftSettings as $giftSetting) {
                $oneSendGifAff = GiftAffiliate::select('affiliate_id')->where('affiliate_id', $aff->id)->where('gift_id', $giftSetting->id)->first();
                if (!$oneSendGifAff) {
                    $giftService->sendGiftAndCheckStock($aff, $giftSetting->id, $merchant, $merchantSetting, null, Gift::ACTION_SEND_GIFT_AUTO_AND_BULK);
                }
            }
        }
    }
}

<?php

namespace App\Listeners\Merchant;

use App\Constant\ProgramCaching;
use App\Constant\Tracking\TodoList;
use App\Jobs\PostbackUrlWhenPaid;
use App\Jobs\ReSyncAffiliateMarketplaceRankJob;
use App\Jobs\SendMailQueue;
use App\Jobs\SyncOrderTagJob;
use App\Models\MerchantSetting;
use App\Models\OrderDetail;
use App\Models\OrderItemMongoDB;
use App\Models\OrderTracking;
use App\Models\ProductAnalytic;
use App\Models\Program;
use App\Models\Referral;
use App\Models\SendMailLogs;
use App\Services\KlaviyoApiNew;
use App\Services\MixpanelAnalyticsService\MixpanelAnalyticService;
use App\Services\ReferralService\ReferralService;
use App\Services\SendMail;
use Carbon\Carbon;
use Exception;
use App\Services\MerchantSendMail;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Secomapp\Contracts\ClientApiContract;
use Secomapp\Models\Shop;
use Secomapp\Models\ShopInfo;
use Secomapp\Resources\Order;

class NewReferralListener implements ShouldQueue
{
    public $queue = 'new-referral';

    public $timeout = 300;

    public $connection = 'redis';

    public $tries = 1;

    public $clientApi;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(ClientApiContract $clientApi)
    {
        $this->clientApi = $clientApi;
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {
        $aff = $event->aff;
        $ref = $event->ref;
        if (!isset($ref->id)) return;
        $shop = Shop::find($aff->shop_id);
        $this->updateOrderItemRecord($event->ref);

        /** @var MixpanelAnalyticService $mixpanelService */
        $mixpanelService = app(MixpanelAnalyticService::class);
        $mixpanelService->handleNewReferralPushMixpanel($shop, $ref, $aff);

        $merchant = User::where('shop_id', $aff->shop_id)->with('setting')->first();
        $settings = MerchantSetting::where('shop_id', $merchant->shop_id)
            ->select(
                'auto_approve_order',
                'notify_new_referral',
                'delay_auto_approve_order',
                'track_appstle_order',
                'track_seal_order',
                'track_recurpay_order',
                'klaviyo',
                'brand_name',
                'referral_link',
                'enable_postback_url'
            )->first();
        $isPlanProfessionalUp = helperPlan()->planProfessionalUp($shop);

        $urlReferral = route('aff.commission.index', ['domain' => $merchant->subdomain]);
        if ($ref->tracking_type != Referral::TRACKING_TYPE_REFER_CUSTOMER) {
            if ($settings->klaviyo && $isPlanProfessionalUp) {
                if ($settings->klaviyo['status']) {
                    $apiKey = $settings->klaviyo['api_key'];
                    $klaviyoApi = new KlaviyoApiNew($apiKey);
                    $data = [
                        'data' => [
                            'type' => 'event',
                            'attributes' => [
                                'profile' => [
                                    "data" => [
                                        "type" => "profile",
                                        "attributes" => [
                                            "email" => "$aff->email"
                                        ]
                                    ]
                                ],
                                'metric' => [
                                    "data" => [
                                        "type" => "metric",
                                        "attributes" => [
                                            'name' => "UpPromote - New Referral - $settings->brand_name"
                                        ]
                                    ]
                                ],
                                'properties' => [
                                    'first_name' => $aff->first_name,
                                    'last_name' => $aff->last_name,
                                    'referral_id' => $ref->id,
                                    'affiliate_link' => genReferralLink($aff, $settings->referral_link),
                                    'order_id' => $ref->order_id,
                                    'order_number' => $ref->order_number,
                                ]
                            ]
                        ]
                    ];
                    $klaviyoApi->sendApiKlaviyo($data);
                }
            }
        }

        $customerName = !empty($ref->customer->full_name) ? $ref->customer->full_name : 'null';
        $replaces = [$aff->first_name, $aff->last_name, $ref->id, $urlReferral, $ref->order_id, $ref->order_number, $customerName];

        $sendEmailNewReferral = false;
        if ($isPlanProfessionalUp && $settings->auto_approve_order == 1) {
            if ($settings->delay_auto_approve_order != 0) {
                $sendEmailNewReferral = true;
            }
        } else {
            $sendEmailNewReferral = true;
        }

        if ($settings->notify_new_referral == 1) {
            $this->sendNotiToMerchant($merchant, $aff->email, $ref);
        }

        // Add order tag
        try {
            dispatch(new SyncOrderTagJob($merchant, $ref));
        } catch (Exception $e) {
            logger($e->getMessage() . ' ' . $e->getTraceAsString());
        }

        $this->clientApi->setShopName($merchant->shop_name);
        $this->clientApi->setAccessToken($shop->access_token);
        if ($settings->track_appstle_order) {
            $this->appstleSubscription($this->clientApi, $ref, $merchant, $aff);
        }

        if ($settings->track_seal_order) {
            $this->sealSubscriptions($this->clientApi, $ref, $merchant, $aff);
        }

        if ($settings->track_recurpay_order) {
            $this->recurpaySubscription($this->clientApi, $ref, $merchant, $aff);
        }

        // Call PostbackUrl to system affiliate
        try {
            $inWhiteListEmail = $aff->marketplace_user_id && optional(\App\Models\Marketplace\User::find($aff->marketplace_user_id))->enable_postback_url;
            $shopAvailable = $isPlanProfessionalUp && $settings->enable_postback_url == 1;
            if ($inWhiteListEmail || $shopAvailable) {
                dispatch((new PostbackUrlWhenPaid($ref->id))->onQueue('postbackurl'));
            }
        } catch (Exception $e) {
            report($e);
        }

        if ($sendEmailNewReferral) {
            MerchantSendMail::send($merchant, $aff, 'new_referral', $replaces);
        }

        if ($aff && $aff->marketplace_user_id) {
            dispatch(new ReSyncAffiliateMarketplaceRankJob(['affId' => $aff->id]));
        }

        if (!in_array($ref->tracking_type, [Referral::TRACKING_TYPE_MANUALLY_ADDED, Referral::TRACKING_TYPE_MANUALLY_IMPORT,
                                            Referral::TRACKING_TYPE_RECRUITMENT_BONUS, Referral::TRACKING_TYPE_PERFORMENT_BONUS]))
        {
            /**
             * @var ReferralService $referralService
             */
            $referralService = app(ReferralService::class);
            $referralService->handleReferralTotal($aff->shop_id);
        }

        try {
            $shopName = shopNameFromDomain($shop->shop);
            ProgramCaching::clearCache(Program::ALL_PROGRAMS, $shopName);
            TodoList::clearCacheReferral($shopName);
        } catch (Exception $exception) {
            logger('delete caching program NewReferralListener - ' . $exception->getMessage());
        }
    }

    /**
     * Check if this is first order of Seal Subscriptions
     * @param $clientApi
     * @param $ref
     * @param $merchant
     * @param $aff
     * @return void
     */
    public function sealSubscriptions($clientApi, $ref, $merchant, $aff)
    {
        try {
            $order = new Order($clientApi);
            $orderInfo = json_decode(json_encode($order->get($ref->order_id)), true);

            if ($orderInfo) {
                if ($this->isSealFirstOrder($orderInfo['tags'])) {
                    $this->storeSealSubscriptionTracking($aff->id, $orderInfo, $merchant->shop_name); // Process store order tracking for Seal Subscription
                }
            }
        } catch (Exception $e) {
            logger($e->getMessage() . ' ' . $e->getTraceAsString());
        }
    }

    /**
     * Check if this is first order of Appstle Subscription
     * @param $clientApi
     * @param $ref
     * @param $merchant
     * @param $aff
     * @return void
     */
    public function appstleSubscription($clientApi, $ref, $merchant, $aff)
    {
        try {
            $order = new Order($clientApi);
            $orderInfo = json_decode(json_encode($order->get($ref->order_id)), true);

            if ($orderInfo) {
                if ($this->isAppstleFirstOrder($orderInfo['tags'])) {
                    $this->storeAppstleTracking($aff->id, $orderInfo, $merchant->shop_name); // Process store order tracking for Appstle Subscription
                }
            }
        } catch (Exception $e) {
            logger($e->getMessage() . ' ' . $e->getTraceAsString());
        }
    }

    /**
     * Check if this is first order of Recurpay Subscription
     * @param $clientApi
     * @param $ref
     * @param $merchant
     * @param $aff
     * @return void
     */
    public function recurpaySubscription($clientApi, $ref, $merchant, $aff)
    {
        try {
            $order = new Order($clientApi);
            $orderInfo = json_decode(json_encode($order->get($ref->order_id)), true);

            if ($orderInfo) {
                if ($this->isRecurpayFirstOrder($orderInfo['tags'])) {
                    // Process store order tracking for Recurpay Subscription
                    $this->storeRecurpayTracking($aff->id, $orderInfo, $merchant->shop_name);
                }
            }
        } catch (Exception $e) {
            logger($e->getMessage() . ' ' . $e->getTraceAsString());
        }
    }

    public function sendNotiToMerchant($merchant, $affiliateEmail, $ref)
    {
        $from = config('mail.from');
        $template = config('myconfig.email_notice.new_referral');
        $checkCustomizeShop = in_array($merchant->shop->shop, config('myconfig.customize.customize_list')) ?
            (($merchant->shop->redirect_domain && $merchant->shop->customize_name)
                ? $merchant->shop->redirect_domain : config('myconfig.app.app_uppromote_react_url'))
            : config('myconfig.app.app_uppromote_react_url');
        $subject = str_replace('{brand_name}', $merchant->setting->brand_name, $template['subject']);
        $content = str_replace(
            [
                '{name}',
                '{affiliate_email}',
                '{order_link}',
                '{order_id}',
                '{referral_id}',
                '{checkDomain}'
            ],
            [
                $merchant->first_name,
                $affiliateEmail,
                getShopUrlByName($merchant->shop_name, 'admin/orders/' . $ref->order_id),
                $ref->order_id,
                $ref->id,
                $checkCustomizeShop
            ],
            view($template['content'])->render()
        );
        $content = SendMail::attachUnsubscribeLink($content, $merchant, SendMail::MERCHANT);

        dispatch((new SendMailQueue($merchant->setting->sending_email, $from, $subject, $content, $merchant->shop_id, 'new referral - merchant'))->onQueue('sendmail'));

        // Increase sent count
        $sendLog = SendMailLogs::getLog($merchant->shop_id);
        SendMailLogs::updateLog($sendLog, 1, false, true);
    }

    /**
     * @param $orderTag
     * @return bool
     */
    public function isAppstleFirstOrder($orderTag)
    {
        if (strpos($orderTag, 'appstle_subscription_first_order') !== false) {
            return true;
        }

        return false;
    }

    /**
     * @param $affiliateId
     * @param $input
     * @param $shopName
     */
    public function storeAppstleTracking($affiliateId, $input, $shopName)
    {
        $expired = Carbon::now()->addDays(365);

        foreach ($input['line_items'] as $l) {
            $browserFingerprints = md5('appstle' . $input['customer']['email'] . $l['variant_id'] . $l['product_id']);
            $newOrderTracking = new OrderTracking;
            $newOrderTracking->affiliate_id = $affiliateId;
            $newOrderTracking->shop_name = $shopName;
            $newOrderTracking->created_at = Carbon::now();
            $newOrderTracking->expired_at = $expired;
            $newOrderTracking->browser_fingerprints = $browserFingerprints;
            $newOrderTracking->cart_token = $input['cart_token'];
            $newOrderTracking->save();
        }
    }

    /**
     * @param $orderTag
     * @return bool
     */
    public function isSealFirstOrder($orderTag)
    {
        if (strpos($orderTag, 'seal_new_subscription') !== false) {
            return true;
        }

        return false;
    }

    /**
     * @param $affiliateId
     * @param $input
     * @param $shopName
     */
    public function storeSealSubscriptionTracking($affiliateId, $input, $shopName)
    {
        $expired = Carbon::now()->addDays(365);

        foreach ($input['line_items'] as $l) {
            $browserFingerprints = md5('seal' . $input['customer']['email'] . $l['variant_id'] . $l['product_id']);
            $newOrderTracking = new OrderTracking;
            $newOrderTracking->affiliate_id = $affiliateId;
            $newOrderTracking->shop_name = $shopName;
            $newOrderTracking->created_at = Carbon::now();
            $newOrderTracking->expired_at = $expired;
            $newOrderTracking->browser_fingerprints = $browserFingerprints;
            $newOrderTracking->cart_token = $input['cart_token'];
            $newOrderTracking->save();
        }
    }

    /**
     * @param $orderTag
     * @return bool
     */
    public function isRecurpayFirstOrder($orderTag)
    {
        if (
            strpos($orderTag, 'recurpay') !== false
            && strpos($orderTag, 'subscription-first-order') !== false
        ) {
            return true;
        }

        return false;
    }

    /**
     * @param $affiliateId
     * @param $input
     * @param $shopName
     */
    public function storeRecurpayTracking($affiliateId, $input, $shopName)
    {
        $expired = Carbon::now()->addDays(365);

        foreach ($input['line_items'] as $l) {
            $browserFingerprints = md5('recurpay' . $input['customer']['email'] . $l['variant_id'] . $l['product_id']);
            $newOrderTracking = new OrderTracking;
            $newOrderTracking->affiliate_id = $affiliateId;
            $newOrderTracking->shop_name = $shopName;
            $newOrderTracking->created_at = Carbon::now();
            $newOrderTracking->expired_at = $expired;
            $newOrderTracking->browser_fingerprints = $browserFingerprints;
            $newOrderTracking->cart_token = $input['cart_token'];
            $newOrderTracking->save();
        }
    }

    private function updateOrderItemRecord(Referral $referral)
    {
        if (!$referral->order_id) return;

        // REMOVE ANALYTIC OLD
        $orderItem = OrderItemMongoDB::query()
            ->where(function ($builder) use ($referral) {
                $builder
                    ->where('shop_id', $referral->shop_id)
                    ->orWhere('shop_id', "$referral->shop_id");
            })
            ->where(function ($builder) use ($referral) {
                $builder
                    ->where('order_id', $referral->order_id)
                    ->orWhere('order_id', "$referral->order_id");
            });

        $productItem = ProductAnalytic::query()
            ->where('shop_id', $referral->shop_id)
            ->where('order_id', $referral->order_id);

        $detail = OrderDetail::where(['shop_id' => $referral->shop_id, 'referral_id' => $referral->id])->first();
        if ($detail && !empty($detail->item_detail['line_items'])) {
            $productIdRemove = [];
            foreach ($detail->item_detail['line_items'] as $item) {
                if (isset($item['exclude_product_collection']) && isset($item['product_id'])) {
                    $productIdRemove[] = $item['product_id'];
                }
            }

            if (count($productIdRemove) > 0) {
                // REMOVE ANALYTIC OLD
                $orderItem = $orderItem->whereNotIn('product_id', $productIdRemove);

                $productItem = $productItem->whereNotIn('product_id', $productIdRemove);
            }
        }

        // REMOVE ANALYTIC OLD
        $orderItem->update([
            'is_referral' => true,
            'affiliate_id' => $referral->affiliate_id
        ]);

        $productItem->update([
            'is_referral' => true,
            'affiliate_id' => $referral->affiliate_id
        ]);
    }
}

<?php

namespace App\Listeners\Merchant;

use App\Jobs\MixpanelAnalytics\MixpanelAnalyticsJob;
use App\Models\Affiliate;
use App\Models\AffiliateCoupon;
use App\Models\MerchantSetting;
use App\Models\SendMailLogs;
use App\Services\AffiliateService\AffiliateService;
use App\Services\CustomerIOService\CustomerIOService;
use App\Services\KlaviyoApiNew;
use App\Services\MultipleShopService\MultipleShopService;
use App\Services\SendMail;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Jobs\SendMailQueue;
use App\Models\AffiliateSetting;
use App\Models\EmailTemplate;
use App\Models\Program;
use Secomapp\Models\Shop;
use Secomapp\Models\ShopInfo;

class AffiliateImportedListener
{
    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {
        try {
            $user = Auth::user();
            $affiliate = $event->aff;
            $coupon = $event->coupon;
            $import = $event->import;
            $shop = Shop::findOrFail($affiliate->shop_id);
            $merchantSettings = MerchantSetting::where('shop_id', $affiliate->shop_id)->first();

            $this->createSetting($affiliate);

            // Handle auto generate custom link
            /**
             * Generate custom affiliate link
             * @var AffiliateService $affiliateService
             */
            $affiliateService = app(AffiliateService::class);
            $canUseAutoGenerateCustomLink = $affiliateService->checkAutoGenerateCustomLink($shop, $merchantSettings, false, [$affiliate->id], true);
            $customAffiliateLink = null;
            if ($canUseAutoGenerateCustomLink) {
                $findTag = Affiliate::TAG_AUTO_GENERATE_CUSTOM_LINK;
                $affiliateService->processAutoGenerateCustomLink($shop, $merchantSettings, $findTag, null, false, [$affiliate->id]);
                $customAffiliateLink = $affiliateService->affiliateRepository->findByCondition(['id' => $affiliate->id], ['custom_affiliate_link']); // re-query get custom link
                $customAffiliateLink = $customAffiliateLink->custom_affiliate_link;
            }

            $this->notification($affiliate, $coupon, $customAffiliateLink);
            $this->trackEventMixpanel($shop, $affiliate, $import, $user);

            unset($affiliate['temp_password']);
            unset($affiliate['notification']);
            $this->handleMultishop($affiliate, $coupon);

            // send customer IO
            try {
                /**
                 * @var CustomerIOService $customerIO
                 */
                $customerIO = app(CustomerIOService::class);

                $customerIO->update(Shop::find($affiliate->shop_id), [
                    'aff_affiliate_total' => Affiliate::whereShopId($affiliate->shop_id)->whereStatus(Affiliate::ACTIVE_AFFILIATE)->count()
                ]);

            }catch (Exception $ex){
                report($ex);
            }
        } catch (Exception $e) {
            Log::error($e->getMessage() . $e->getTraceAsString());
        }
    }

    /**
     * @param $shop
     * @param $affiliate
     * @param $import
     * @param $user
     * @return void
     */
    private function trackEventMixpanel($shop, $affiliate, $import, $user) {
        try {
            $program = Program::find($affiliate->program_id);

            dispatch(new MixpanelAnalyticsJob('track-event', $affiliate->shop_id, [
                'Current page' => 'Affiliates',
                'Screen size' => !empty($_COOKIE['screenSize']) ? $_COOKIE['screenSize'] : null,
                'Operation system' => !empty($_COOKIE['operationSystem']) ? $_COOKIE['operationSystem'] : null,
                'Affiliate email' => $affiliate->email ?? null,
                'Program Name' => $program->name ?? null,
                'Signup source' => registerMethodAff($affiliate->register_method)
            ], [
                'shop' => $shop,
                'event_name' => $import ? 'Manually import affiliates' : 'Manually add affiliate',
                'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                'params' => !empty(request()->all()) ? request()->all() : null,
            ], session()->has('sudo')));
        } catch (Exception $exception) {
            logger('AffiliateImportedListener trackEventMixpanel' . $exception->getMessage());
        }
    }
    /******** End Mixpanel ********/

    public function createSetting($newAff, $affiliate = null,bool $sync = false)
    {
        $setting = new AffiliateSetting;
        $setting->affiliate_id = $newAff->id;
        $setting->payment_method = isSettingPaymentDefault($newAff, $affiliate, $sync);
        $setting->save();
    }

    public function notification($affiliate, $coupon, $customAffiliateLink)
    {
        $program = Program::find($affiliate->program_id);
        $commissionStructure = get_commission_type_text($program->commission_type);

        $settings = DB::table('merchant_settings')
            ->join('users', 'merchant_settings.shop_id', '=', 'users.shop_id')
            ->where('merchant_settings.shop_id', $affiliate->shop_id)
            ->select('merchant_settings.id', 'merchant_settings.use_sending_email', 'merchant_settings.sending_email',
                'merchant_settings.verify_status', 'merchant_settings.brand_name', 'merchant_settings.klaviyo', 'merchant_settings.affiliate_registration',
                'merchant_settings.referral_link', 'users.subdomain', 'merchant_settings.custom_domain')
            ->first();
        $commissionAmount = get_commission_text($program->commission_type, $program->rule, $program->commission_amount, $program->advance_amount, session('money_format'));

        if ($settings->custom_domain) {
            $loginLink = 'https://' . $settings->custom_domain . '/login';
        } else {
            $user = Auth::user();
            if ($user) {
                $loginLink = url('/') . '/' . $user->subdomain . '/login';
            } else {
                $loginLink = url('/') . '/login';
            }
        }
        $coupon_aff = $coupon ?: '';
        if (!$coupon_aff) {
            $affiliateCoupons = AffiliateCoupon::where('affiliate_id', $affiliate->id)->where('shop_id', $affiliate->shop_id)->get();
            foreach ($affiliateCoupons as $c) {
                $coupons[] = $c->coupon;
            }
            if (!empty($coupons)) {
                $coupon_aff = implode(", ", $coupons);
            }
        }

        if ($settings->klaviyo && helperPlan()->planProfessionalUp()) {
            $settingCheck = json_decode($settings->klaviyo,true);
            if ($settingCheck['status']) {
                $apiKey = $settingCheck['api_key'];
                $klaviyoApi = new KlaviyoApiNew($apiKey);
                $data = [
                    "data" => [
                        "type" => "event",
                        "attributes" => [
                            "profile"=>[
                                "data"=>[
                                    "type"=> "profile",
                                    "attributes"=>[
                                        "email"=>"$affiliate->email",
                                        "first_name" => $affiliate->first_name,
                                        "last_name" => $affiliate->last_name,
                                        "properties" => [
                                            "Name - UpPromote" => $affiliate->first_name . ' ' . $affiliate->last_name,
                                            "Referral Code - UpPromote" => $affiliate->id . '.' . $affiliate->hash_code,
                                            "Program - UpPromote" => $program->name,
                                            "Sign Up Source - UpPromote" => $affiliate->registerMethodText($affiliate->register_method),
                                            "Affiliate Link - UpPromote" => genReferralLink($affiliate, $settings->referral_link),
                                            "Affiliate Login Link - UpPromote" => $loginLink,
                                            "Commission Structure - UpPromote" => $commissionStructure,
                                            "Commission Amount - UpPromote" => $commissionAmount,
                                            "First Name - UpPromote" => $affiliate->first_name,
                                            "Last Name - UpPromote" => $affiliate->last_name,
                                            "Coupon- UpPromote" => $coupon_aff
                                        ]
                                    ]
                                ]
                            ],
                            "metric"=>[
                                "data"=>[
                                    "type"=>"metric",
                                    "attributes"=>[
                                        "name" => "UpPromote - Added Affiliate - " .$settings->brand_name
                                    ]
                                ]
                            ],
                            "properties" => [
                                "first_name" => $affiliate->first_name,
                                "last_name" => $affiliate->last_name,
                                "affiliate_link" => genReferralLink($affiliate, $settings->referral_link),
                                "affiliate_login_url" => $loginLink,
                                "commission_structure" => $commissionStructure,
                                "commission_amount" => $commissionAmount,
                                "temporary_password" => $affiliate->temp_password,
                                "coupon" => $coupon_aff
                            ]
                        ]
                    ]
                ];
                $klaviyoApi->sendApiKlaviyo($data);
            }
        }
        if ($affiliate->notification) {
            $shopInfo = ShopInfo::where('shop_id', $affiliate->shop_id)->first();
            if ($shopInfo) {
                if ($shopInfo->plan_name == 'trial') return;
            }

            $emailTemplate = EmailTemplate::where('shop_id', $affiliate->shop_id)->where('program_id', [$affiliate->program_id])->where('type', 7)->first();
            if (!$emailTemplate) {
                $emailTemplate = EmailTemplate::where('shop_id', $affiliate->shop_id)->where('type', 7)->first();
            }
            if (!$emailTemplate) {
                $user = DB::table('users')->where('shop_id', $affiliate->shop_id)->first();
                $defaultTemplate = config('myconfig.default_email_template.invitation_affiliate');
                $emailTemplate = new EmailTemplate;
                $emailTemplate->user_id = $user->id;
                $emailTemplate->shop_id = $user->shop_id;
                $emailTemplate->name = $defaultTemplate['name'];
                $emailTemplate->subject = $defaultTemplate['subject'];
                $emailTemplate->content = htmlentities(view($defaultTemplate['content'])->render());
                $emailTemplate->status = 1;
                $emailTemplate->type = $defaultTemplate['type'];
                $emailTemplate->slug = $defaultTemplate['slug'];
                $emailTemplate->save();
            }
            if ($emailTemplate->status == 1) {
                $referralLink = $settings->referral_link . '?sca_ref=' . $affiliate->id . '.' . $affiliate->hash_code;
                if ($customAffiliateLink) {
                    $defaultAffiliateLinkFull = parse_url($settings->referral_link);
                    $defaultCustomAffiliateLinkDomain = optional($defaultAffiliateLinkFull)['scheme'] . '://' . optional($defaultAffiliateLinkFull)['host'];
                    $referralLink = $defaultCustomAffiliateLinkDomain . '/' . $customAffiliateLink;
                }

                $finds = ['{affiliate_name}', '{last_name}', '{affiliate_link}', '{commission_amount}', '{commission_structure}', '{affiliate_login_link}', '{temporary_password}', '{coupon}', '{brand_name}', '{logo}'];
                $replaces = [$affiliate->first_name, $affiliate->last_name, $referralLink, $commissionAmount, $commissionStructure, $loginLink, $affiliate->temp_password, $coupon_aff, $settings->brand_name];
                if ($settings->affiliate_registration != null) {
                    $settingRegistration = json_decode($settings->affiliate_registration);
                    if (isset($settingRegistration->logo)) {
                        if ($settingRegistration->logo != 'none') {
                            $replaces[] = '<img style="width: 100%" src="' . asset_s3($settingRegistration->logo) . '" />';
                        } else {
                            $replaces[] = '';
                        }
                    } else {
                        $replaces[] = '';
                    }
                } else {
                    $replaces[] = '';
                }

                $contentEmail = str_replace($finds, $replaces, html_entity_decode($emailTemplate->content));
                $contentEmail = SendMail::attachUnsubscribeLink($contentEmail, $affiliate, SendMail::AFFILIATE);
                $subject = str_replace('{brand_name}', $settings->brand_name, html_entity_decode($emailTemplate->subject));
                $from = [];
                if ($settings->verify_status && $settings->use_sending_email) {
                    $from['address'] = $settings->sending_email;
                    $from['name'] = $settings->brand_name;
                    // Verified, send by Merchant email
                } else {
                    $from['address'] = config('mail.from.address');
                    $from['name'] = $settings->brand_name;
                    // Not verified, send by no-reply email
                }
                $to = $affiliate->email;
                //send mail
                dispatch((new SendMailQueue($to, $from, $subject, $contentEmail, $affiliate->shop_id, 'import affiliate'))->onQueue('sendmail'));

                // Increase sent count
                $sendLog = SendMailLogs::getLog($affiliate->shop_id);
                SendMailLogs::updateLog($sendLog, 1);
            }
        }
    }

    protected function checkExistAndGetRandomHashcode()
    {
        while (1) {
            $hashcode = generateRandomString();
            $check = Affiliate::where('hash_code', $hashcode)->first();
            if (!$check) {
                break;
            }
        }
        return $hashcode;
    }

    protected function handleMultishop($affiliate, $coupon)
    {
        /** @var MultipleShopService $multipleShopService */
        $multipleShopService = app(MultipleShopService::class);
        $canUseMultipleShop = $multipleShopService->canUseMultipleShop($affiliate->shop_id);
        if ($canUseMultipleShop) {
            $shopRelationship = $multipleShopService->getShopRelationship($affiliate->shop_id, $affiliate->shop_id);
            foreach ($shopRelationship as $r) {
                $isExist = Affiliate::where('email', $affiliate->email)->where('shop_id', $r['shop_id'])->first();
                if (!$isExist) {
                    $programDefault = Program::where('shop_id', $r['shop_id'])->where('is_default', 1)->first();
                    $newAff = $affiliate->replicate();
                    $newAff->shop_id = $r['shop_id'];
                    $newAff->program_id = $programDefault->id;
                    $newAff->hash_code = $this->checkExistAndGetRandomHashcode();
                    $newAff->created_at = Carbon::createFromTimestamp($affiliate->created_at)->addSeconds(1);
                    $newAff->updated_at = Carbon::createFromTimestamp($affiliate->created_at)->addSeconds(1);
                    $parentId = 0;
                    $oldAff = Affiliate::where('id', $newAff->parent_id)->select('email')->first();
                    if ($oldAff) {
                        $newParent = Affiliate::where(['email' => $oldAff->email, 'shop_id' => $r['shop_id']])->select('id')->first();
                        if ($newParent) {
                            $parentId = $newParent->id;
                        }
                    }
                    $newAff->parent_id = $parentId;
                    $newAff->is_first_approve_recruitment_bonus = 1;
                    $newAff->save();
                    $this->createSetting($newAff, $affiliate, true);
                    $this->createCoupon($newAff, $coupon);
                }
            }
        }
    }

    protected function createCoupon($affiliate, $coupon)
    {
        if ($coupon) {
            $isExistCoupon = AffiliateCoupon::where('shop_id', $affiliate->shop_id)->where('coupon', $coupon)->first();
            if (!$isExistCoupon) {
                $newCoupon = new AffiliateCoupon;
                $newCoupon->shop_id = $affiliate->shop_id;
                $newCoupon->affiliate_id = $affiliate->id;
                $newCoupon->coupon = $coupon;
                $newCoupon->save();
            }
        }
    }
}

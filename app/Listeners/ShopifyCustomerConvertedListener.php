<?php

namespace App\Listeners;

use App\Constant\SynchronousModelType;
use App\Jobs\MixpanelAnalytics\MixpanelAnalyticsJob;
use App\Jobs\SendMailQueue;
use App\Jobs\SyncCustomerShopifyJob;
use App\Models\Affiliate;
use App\Models\AffiliateCoupon;
use App\Models\ProgramsSetting;
use App\Models\SendMailLogs;
use App\Models\ShopifyCustomerConvert;
use App\Services\AffiliateService\AffiliateService;
use App\Services\KlaviyoApiNew;
use App\Services\SendMail;
use App\Models\User;
use App\Models\Program;
use App\Models\AffiliateSetting;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Secomapp\Models\Shop;

class ShopifyCustomerConvertedListener
{
    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        try{
            $affiliate = $event->user;
            $shop = Shop::find($affiliate->shop_id);
            $this->createRegisteredPurchasePopup($affiliate->id);
            $this->createSetting($affiliate);
            $merchant = User::where('shop_id', $affiliate->shop_id)->with('setting')->first();
            $settings = $merchant->setting;
            $affiliateLink = !empty($affiliate->referral_link) ? $affiliate->referral_link : ''; // no move position
            if ($settings->default_affiliate_link_program && !empty($affiliate->program->setting->affiliate_link_destination)) {
                $affiliateLink = $affiliate->program->setting->affiliate_link_destination . '?sca_ref=' . $affiliate->id . '.' . $affiliate->hash_code;
                $settings->referral_link = $affiliate->program->setting->affiliate_link_destination;
            }
            /**
             * Generate custom affiliate link
             * @var AffiliateService $affiliateService
             */
            $affiliateService = app(AffiliateService::class);
            $canUseAutoGenerateCustomLink = $affiliateService->checkAutoGenerateCustomLink($shop, $settings, false, [$affiliate->id], true);
            $customAffiliateLink = null; 
            if ($canUseAutoGenerateCustomLink) {
                $findTag = Affiliate::TAG_AUTO_GENERATE_CUSTOM_LINK;
                $affiliateService->processAutoGenerateCustomLink($shop, $settings, $findTag, null, false, [$affiliate->id]);

                /** @var AffiliateService $affiliateService */
                $affiliateService = app(AffiliateService::class);
                $customAffiliateLink = $affiliateService->affiliateRepository->findByCondition(['id' => $affiliate->id], ['custom_affiliate_link']); // re-query get custom link
                $customAffiliateLink = $customAffiliateLink->custom_affiliate_link;
            }

            if($affiliate->status == Affiliate::ACTIVE_AFFILIATE && $affiliate->is_pending == Affiliate::IS_NOT_PENDING) {
                $program = Program::find($affiliate->program_id);
                $commissionStructure = get_commission_type_text($program->commission_type);

                $shopInfo = DB::table('shop_infos')->where('shop_id', $affiliate->shop_id)->select('money_format')->first();
                if ($program->commission_type != Program::PERCENT_OF_SALE) {
                    $commission_amount = currnency_format(strip_tags($shopInfo->money_format), $program->commission_amount);
                } else {
                    $commission_amount = ($program->commission_amount) . '%';
                }

                $affiliateCoupon = AffiliateCoupon::where('affiliate_id', $affiliate->id)->where('shop_id', $affiliate->shop_id)->get();
                $discountCode = '';
                if ($affiliateCoupon) {
                    foreach ($affiliateCoupon as $c) {
                        $coupons[] = $c->coupon;
                    }
                    if (!empty($coupons)) {
                        $discountCode = implode(', ', $coupons);
                    }
                }

                $loginLink = config('app.url') . '/' . $merchant->subdomain . '/login';
                if ($settings->custom_domain) {
                    $loginLink = 'https://' . $settings->custom_domain . '/login';
                }
                $finds = ['{customer_name}', '{first_name}', '{last_name}', '{affiliate_link}', '{commission_amount}', '{commission_structure}', '{affiliate_login_link}', '{temporary_password}', '{brand_name}', '{coupon}', '{logo}'];
                if ($customAffiliateLink) {
                    $defaultAffiliateLinkFull = parse_url($settings->referral_link);
                    $defaultCustomAffiliateLinkDomain = optional($defaultAffiliateLinkFull)['scheme'] . '://' . optional($defaultAffiliateLinkFull)['host'];
                    $affiliateLink = $defaultCustomAffiliateLinkDomain . '/' . $customAffiliateLink;
                }

                $replaces = [$affiliate->first_name, $affiliate->first_name, $affiliate->last_name, $affiliateLink, $commission_amount, $commissionStructure, $loginLink, $affiliate->temp_password, $settings->brand_name, $discountCode];
                if ($settings->affiliate_registration != null) {
                    if (is_array($settings->affiliate_registration)) {
                        $settingRegistration = $settings->affiliate_registration;
                        if (isset($settingRegistration['logo'])) {
                            if ($settingRegistration['logo'] != 'none') {
                                $replaces[] = '<img style="width: 100%" src="' . asset_s3($settingRegistration['logo']) . '" />';
                            } else {
                                $replaces[] = '';
                            }
                        } else {
                            $replaces[] = '';
                        }
                    } else {
                        $settingRegistration = json_decode($settings->affiliate_registration);
                        if (isset($settingRegistration->logo)) {
                            if ($settingRegistration->logo != 'none') {
                                $replaces[] = '<img style="max-width: 100%" src="' . asset_s3($settingRegistration->logo) . '" />';
                            } else {
                                $replaces[] = '';
                            }
                        } else {
                            $replaces[] = '';
                        }
                    }
                } else {
                    $replaces[] = '';
                }

                $contentEmail = str_replace($finds, $replaces, $affiliate->purchase->email);
                $contentEmail = SendMail::attachUnsubscribeLink($contentEmail, $affiliate, SendMail::AFFILIATE);
                $subject = $affiliate->purchase->subject;
                if (!$subject) {
                    $subject = 'Your affiliate account on {brand_name} has been created, check it out!';
                }
                $subject = str_replace(['{brand_name}'], [$affiliate->brand_name], $subject);
                $from = [];
                if ($settings->verify_status && $settings->use_sending_email) {
                    $from['address'] = $settings->sending_email;
                    $from['name'] = $settings->brand_name;
                    // Verified, send by Merchant email
                } else {
                    $from['address'] = config('mail.from.address');
                    $from['name'] = $settings->brand_name;
                    // Not verified, send by no-reply email
                }
                $to = $affiliate->email;
                //send mail
                $isStatusEmail = ShopifyCustomerConvert::select('email_status')->where('shop_id', $affiliate->shop_id)->first();
                if (isset($isStatusEmail) && $isStatusEmail->email_status)
                {
                    dispatch((new SendMailQueue($to, $from, $subject, $contentEmail, $affiliate->shop_id, 'convert customer'))->onQueue('sendmail'));
                }

                // Increase sent count
                $sendLog = SendMailLogs::getLog($affiliate->shop_id);
                SendMailLogs::updateLog($sendLog, 1);

                //api klaviyo metric
                if($settings->klaviyo && helperPlan()->planProfessionalUp(Shop::find($affiliate->shop_id))) {
                    if($settings->klaviyo['status']) {
                        $apiKey = $settings->klaviyo['api_key'];
                        $klaviyoApi = new KlaviyoApiNew($apiKey);
                        $data = [
                            "data" => [
                                "type" => "event",
                                "attributes" => [
                                    "profile" => [
                                        "data" => [
                                            "type" => "profile",
                                            "attributes" => [
                                                "email" => "$affiliate->email"
                                            ]
                                        ]
                                    ],
                                    "metric" => [
                                        "data" => [
                                            "type" => "metric",
                                            "attributes" => [
                                                "name" => "UpPromote - Convert signed up customer - $settings->brand_name"
                                            ]
                                        ]
                                    ],
                                    "properties" => [
                                        "first_name" => $affiliate->first_name,
                                        "last_name" => $affiliate->last_name,
                                        "affiliate_link" => genReferralLink($affiliate,$settings->referral_link),
                                        "commission_structure" => $commissionStructure,
                                        "commission_amount" => $commission_amount,
                                        "temporary_password" => $affiliate->temp_password,
                                        "coupon" => $discountCode,
                                        "affiliate_login_link" => $loginLink
                                    ]
                                ]
                            ]
                        ];

                        $klaviyoApi->sendApiKlaviyo($data);
                    }
                }


                // Send notification email to merchant
                if ($settings->notify_new_affiliate) {
                    $this->sendNotiToMerchant($merchant, $affiliate->email, $settings->auto_active_affiliate);
                }
            }

            // resync tag when customer convert to aff
            $shop = Shop::find($affiliate->shop_id);
            if($shop){
                $shopName = $shop->shop;
                $accessToken = $shop->access_token;
                dispatch((new SyncCustomerShopifyJob(collect([$affiliate]), $shopName, $affiliate->shop_id, $accessToken))->onQueue('integration-customer-shopify')); // resync queue job;
            }

            $this->handleMultipleShop($affiliate, $customAffiliateLink);

            /******** Start Mixpanel ********/
            try {
                dispatch(new MixpanelAnalyticsJob('track-event', $affiliate->shop_id, [
                    'screen_size' => !empty($_COOKIE['screenSize']) ? $_COOKIE['screenSize'] : null,
                    'operation_system' => !empty($_COOKIE['operationSystem']) ? $_COOKIE['operationSystem'] : null,
                    'Affiliate email' => $affiliate->email ?? null,
                    'Program id' => $affiliate->program_id ?? null,
                    'Signup source' => 'Signed up customer'
                ], [
                    'shop' => $shop,
                    'event_name' => 'Affiliate signed up',
                    'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                    'params' => null,
                ]));
            } catch (Exception $exception) {
                logger($exception->getMessage() . ' - ' . $exception->getMessage());
            }
            /******** End Mixpanel ********/
        }catch(Exception $e){
            Log::error($e->getMessage() . $e->getTraceAsString());
        }
    }

    public function handleMultipleShop($affiliate, $customAffiliateLink)
    {
        dispatchSyncDataSignal(
            SynchronousModelType::AFFILIATES_AND_INFORMATION,
            SynchronousModelType::AFFILIATES_AND_INFORMATION_ACTION['ON_CREATED']
        )
            ->setArguments($affiliate->id, null, $customAffiliateLink)
            ->setIdentity($affiliate->shop_id)
            ->dispatch();
    }

    public function createRegisteredPurchasePopup($affId)
    {
        DB::table('registered_purchase_popup')->insert(['affiliate_id'=>$affId,'type'=>1]);
    }

    public function createSetting($aff)
    {
        $setting = new AffiliateSetting;
        $setting->affiliate_id = $aff->id;
        $setting->payment_method = isSettingPaymentDefault($aff);
        $setting->save();
    }

    protected function sendNotiToMerchant($merchant, $affiliateEmail, $autoApproved)
    {
        $from = config('mail.from');
        if ($autoApproved) {
            $emailType = 'new auto approved affiliate - merchant';
            $template = config('myconfig.email_notice.new_auto_approved_affiliate');
        } else {
            $emailType = 'new affiliate - merchant';
            $template = config('myconfig.email_notice.new_affiliate');
        }
        $checkCustomizeShop = in_array($merchant->shop->shop, config('myconfig.customize.customize_list')) ?
            (($merchant->shop->redirect_domain && $merchant->shop->customize_name)
                ? $merchant->shop->redirect_domain : config('myconfig.app.app_uppromote_react_url'))
            : config('myconfig.app.app_uppromote_react_url');


        $subject = str_replace('{name}', $merchant->first_name, $template['subject']);
        $content = str_replace(
            [
                '{name}',
                '{affiliate_email}',
                '{checkDomain}'
            ],
            [
                $merchant->first_name,
                $affiliateEmail,
                $checkCustomizeShop
            ],
            view($template['content'])->render()
        );
        $content = SendMail::attachUnsubscribeLink($content, $merchant, SendMail::MERCHANT);

        // Send mail
        try {
            dispatch((new SendMailQueue($merchant->setting->sending_email, $from, $subject, $content, $merchant->shop_id, $emailType))->onQueue('sendmail'));

            // Increase sent count
            $sendLog = SendMailLogs::getLog($merchant->shop_id);
            SendMailLogs::updateLog($sendLog, 1, false, true);
        } catch (Exception $e) {
            report($e);
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Jobs\HandleReferral;
use App\Jobs\StoreProductAnalytics;
use App\Jobs\SyncProductAnalyticsJob;
use App\Services\OrderReferralService\OrderReferral;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\OrdersModels;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Merchant\OrderTrackingController;

class OrdersController extends OrderTrackingController
{
    /**
     * @param Request $request
     * @param $shop
     * @param OrderReferral $orderReferral
     * @return JsonResponse
     */
    public function listenWebhookOrdersCreate(Request $request, $shop, OrderReferral $orderReferral)
    {
        try {
            $shopSetting = $this->getShopSetting($shop);
            $input = $request->all();
            $input['shopId'] = $shop;

            if (($shopSetting && $shopSetting->record_pending_order && $input['source_name'] != 'shopify_draft_order')
                || ($shopSetting && $shopSetting->record_pending_order
                    && $input['app_id'] == 1354745 && $input['source_name'] == 'shopify_draft_order')
                || ($shopSetting && $input['source_name'] != 'shopify_draft_order'
                    && ($input['financial_status'] == 'paid' || $input['financial_status'] == 'partially_paid'))
            ) {
                if (config('app.env') == 'local') {
                    dispatch(new HandleReferral($input, $shopSetting))->onQueue('referral');
                } else {
                    if ($orderReferral->notDuplicateOrder($request->header('X-Shopify-Webhook-Id'), $input)) {
                        dispatch(new HandleReferral($input, $shopSetting))
                            ->onConnection('redis')
                            ->delay(now()->addSeconds(rand(10, 12)))
                            ->onQueue('referral');
                    }
                }
            }

            if ($shopSetting && $shopSetting->enable_analytics && helperPlan()->planProfessionalUp($shopSetting->shop)) {
                // REMOVE ANALYTIC OLD
                dispatch(new StoreProductAnalytics($request->getContent(), $shop));

                // new product analytics
                dispatch(new SyncProductAnalyticsJob($shopSetting->shop_id, 'single', $input));
            }
        } catch (Exception $e) {
            report($e);
        }

        return response()->json([
            'status' => 'ok',
            'message' => 'success'
        ], 200);
    }

    public function syncOrders(Request $request)
    {
        $content = $request->getContent();
        $content = json_decode($content, true);
        $user = Auth::user();
        $res = OrdersModels::syncOrders($content, $user->shop_id);
        $activityLog = 'synced orders for Product analytics';
        activity('sync_orders')
            ->causedBy($user)
            ->log($activityLog);
        echo json_encode($res);
    }
}

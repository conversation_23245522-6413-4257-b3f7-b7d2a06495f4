<?php

namespace App\Http\Controllers\Merchant;

use App\Constant\SynchronousModelType;
use App\Constant\Tracking\ClickTracking;
use App\Http\Controllers\Controller;
use App\Jobs\HandleReferral;
use App\Jobs\ReferralCreateListenerJob;
use App\Jobs\ReferralRefund;
use App\Jobs\ReferralRefundCreateListenerJob;
use App\Models\Affiliate;
use App\Models\AffiliateCoupon;
use App\Models\AssignDownLinePending;
use App\Models\Click;
use App\Models\ConnectCustomer;
use App\Models\ConnectProductAffiliate;
use App\Models\CustomerInfo;
use App\Models\EncryptableBaseModel\EncryptableBaseModel;
use App\Models\MerchantSetting;
use App\Models\OrderDetail;
use App\Models\OrderTracking;
use App\Models\OrderTrackingMongoDB;
use App\Models\OrderTrackingReport;
use App\Models\Product;
use App\Models\Program;
use App\Models\Referral;
use App\Models\ShopRelationship;
use App\Models\User;
use App\Services\ConvertSignUpCustomerService\ConvertSignUpCustomerService;
use App\Services\OrderPropertyService\OrderPropertyService;
use App\Services\OrderReferralService\OrderReferral;
use App\Services\OrderTrackingService\OrderTrackingService;
use App\Services\SellingPromoteProductService\SellingPromoteProductService;
use App\Services\SettingCommissionSubscriptionsService\SettingCommissionSubscriptionsService;
use App\Traits\AdvanceCommissionStructure;
use App\Utils\CustomerIO;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Secomapp\Models\Shop;
use Secomapp\Resources\Webhook;

class OrderTrackingController extends Controller
{
    use AdvanceCommissionStructure;

    private $lineItems;
    public $orderReferral;
    protected $isDuplicate;
    /** @var ConvertSignUpCustomerService $convertSignUpCustomerService */
    protected $convertSignUpCustomerService;

    private $commissionSubscriptionsService;

    public function __construct(
        OrderReferral                $orderReferral,
        ConvertSignUpCustomerService $convertSignUpCustomerService,
        SettingCommissionSubscriptionsService $commissionSubscriptionsService
    )
    {
        $this->orderReferral = $orderReferral;
        $this->isDuplicate = false;
        $this->convertSignUpCustomerService = $convertSignUpCustomerService;
        $this->commissionSubscriptionsService = $commissionSubscriptionsService;
    }
    public function clickTracking(Request $request): JsonResponse
    {
        $inputs = $request->all();
        $affiliateId = (int)($inputs['aid'] ?? false);
        $shopifyDomain = $request->get('shopify_domain');
        if (!$affiliateId || !$shopifyDomain) return errorResponse(404, 'Affiliate not found!');

        if (!empty($inputs['sca_source'])) {
            $inputs['sca_source'] = trim($inputs['sca_source']);
        }
        $sca_source = empty($inputs['sca_source']) ? null : substr($inputs['sca_source'], 0, 255);

        $now = Carbon::now();
        $browserFingerprints = $this->genBrowserFingerprints($inputs);
        if (isset($inputs['s']) && isValidDomain($inputs['s'])) {
            $inputs['s'] = shopNameFromDomain($inputs['s']);
        }

        $affiliate = $this->getAffiliate($inputs);
        if (!$affiliate) {
            return response()->json(['status' => 'error', 'message' => 'Not found'], 404);
        }
        $affiliateDataForMessageBar = [
            'affiliate_name' => $affiliate->first_name . ' ' . $affiliate->last_name,
            'affiliate_firstname' => $affiliate->first_name,
            'company' => $affiliate->company,
            'personal_detail' => $affiliate->personal_detail
        ];

        $expired = Carbon::now()->addDays($affiliate->cookie_serialize);
        $this->storeClick($affiliate->id, $sca_source);
        $enableAssignDownLineFeature = $this->convertSignUpCustomerService->enableAssignDownLineFeature(
            $affiliate->shop_id
        );

        if (empty($inputs['tid'])) {
            $tid = DB::table('order_trackings')
                ->insertGetId([
                    'affiliate_id' => $affiliate->id,
                    'shop_name' => $inputs['s'],
                    'expired_at' => $expired,
                    'created_at' => $now,
                    'browser_fingerprints' => $browserFingerprints,
                    'sca_source' => $sca_source
                ]);
            return response()->json([
                'status' => 'ok',
                'message' => 'success',
                'tid' => $tid,
                'ep' => $expired != null ? $expired->getTimestamp() : null,
                'afd' => $affiliateDataForMessageBar,
                'afcookie' => $affiliate->cookie_serialize,
                'program_id' => $affiliate->program_id,
                'enable_assign_down_line' => $enableAssignDownLineFeature
            ]);
        } else {
            $orderTracking = DB::table('order_trackings')->where('id', $inputs['tid'])->first();
            if ($orderTracking) {
                DB::table('order_trackings')->where('id', $inputs['tid'])
                    ->update([
                        'affiliate_id' => $affiliate->id,
                        'expired_at' => $expired,
                        'sca_source' => $sca_source
                    ]);
                $tid = $inputs['tid'];
            } else {
                $tid = DB::table('order_trackings')
                    ->insertGetId([
                        'affiliate_id' => $affiliate->id,
                        'shop_name' => $inputs['s'],
                        'expired_at' => $expired,
                        'created_at' => $now,
                        'browser_fingerprints' => $browserFingerprints,
                        'sca_source' => $sca_source
                    ]);
            }
            return response()->json([
                'status' => 'ok',
                'message' => 'success',
                'tid' => $tid,
                'ep' => $expired != null ? $expired->getTimestamp() : null,
                'afd' => $affiliateDataForMessageBar,
                'afcookie' => $affiliate->cookie_serialize,
                'program_id' => $affiliate->program_id,
                'enable_assign_down_line' => $enableAssignDownLineFeature
            ]);
        }
    }


    private function storeClick($affiliateId, $scaSource)
    {
        $today = Carbon::now()->toDateString();
        $click = Click::firstOrNew(['affiliate_id' => $affiliateId, 'created_date' => $today, 'sca_source' => $scaSource]);
        $click->count = $click->count + 1;
        $click->save();
    }

    public function getAffiliate($inputs)
    {
        if (
            empty($inputs['hc'])
            || empty($inputs['aid'])
            || empty($inputs['s'])
        ) return null;
        list($affiliateId, $hashCode, $shopName) = [
            (int)$inputs['aid'],
            $inputs['hc'],
            $inputs['s']
        ];

        $caching = ClickTracking::getCachedCTData(
            $affiliateId,
            $shopName
        );
        if ($caching) return $caching == '>null<' ? null : json_decode($caching);

        $shopUrl = $shopName . '.myshopify.com';
        /** @var Affiliate|null $affiliate */
        $affiliate = Affiliate::where('affiliates.id', $affiliateId)
            ->where('affiliates.status', 1)
            ->where('affiliates.hash_code', $hashCode)
            ->join('shops', 'affiliates.shop_id', '=', 'shops.id')
            ->select(
                'affiliates.id',
                'affiliates.email',
                'affiliates.first_name',
                'affiliates.last_name',
                'affiliates.company',
                'affiliates.personal_detail',
                'affiliates.shop_id',
                'affiliates.hash_code',
                'affiliates.status',
                'affiliates.program_id',
                'shops.shop'
            )->first();
        if (!$affiliate || !$affiliate->cookie) {
            ClickTracking::storeCachingCTData(
                $affiliateId,
                $shopName,
                '>null<'
            );
            return null;
        }

        $affiliate->setAttribute('cookie_serialize', $affiliate->cookie);
        if ($affiliate->shop == $shopUrl) {
            $affiliateObject = (object)$affiliate->getAttributes();
            ClickTracking::storeCachingCTData(
                $affiliateId,
                $shopName,
                json_encode($affiliateObject)
            );
            return $affiliateObject;
        }

        $shop = Shop::where('shop', $shopUrl)
            ->select('id', 'shop')
            ->first();
        $shopRelationship = ShopRelationship::where('children_shop', $shop->id)->first();
        if (!$shopRelationship) {
            ClickTracking::storeCachingCTData(
                $affiliateId,
                $shopName,
                '>null<'
            );
            return null;
        }

        $affiliate = Affiliate::where('email', $affiliate->email)
            ->where('shop_id', $shop->id)
            ->first();
        if (!$affiliate) {
            ClickTracking::storeCachingCTData(
                $affiliateId,
                $shopName,
                '>null<'
            );
            return null;
        }

        $affiliate->setAttribute('cookie_serialize', $affiliate->cookie);
        $affiliateObject = (object)$affiliate->getAttributes();
        ClickTracking::storeCachingCTData(
            $affiliateId,
            $shopName,
            json_encode($affiliateObject)
        );
        return $affiliateObject;
    }

    private function genBrowserFingerprints($inputs)
    {
        $clientIp = request()->ip();
        $browserFingerprints = null;
        if (isset($inputs['ug'])) {
            $browserFingerprints = md5($clientIp . $inputs['ug']);
        }
        return $browserFingerprints;
    }

    public function storeCartToken(Request $request)
    {
        $inputs = $request->all();
        $now = Carbon::now();
        Validator::make($inputs, [
            'tid' => 'required|string',
            'ctk' => 'required|string',
            'aid' => 'integer',
            'sca_source' => 'string'
        ])->validate();

        $browserFingerprints = $this->genBrowserFingerprints($inputs);

        $tracking = OrderTracking::find($inputs['tid']);
        if ($tracking) {
            if ($tracking->expired_at > $now) {
                $cartToken = Str::before($inputs['ctk'], '?key=');
                $arrayTemp = explode('%', $cartToken);
                $cartToken = !empty($arrayTemp[0]) ? $arrayTemp[0] : $cartToken;
                $tracking->cart_token = $cartToken;
                $tracking->save();

                if (!empty($inputs['s'])) {
                    $newTracking = OrderTracking::create([
                        'affiliate_id' => $tracking->affiliate_id,
                        'shop_name' => $inputs['s'],
                        'expired_at' => $tracking->expired_at,
                        'created_at' => $now,
                        'browser_fingerprints' => $browserFingerprints,
                        'sca_source' => empty($inputs['sca_source']) ? null : substr($inputs['sca_source'], 0, 255)
                    ]);
                }
                return response()->json([
                    'status' => 'ok',
                    'message' => 'success',
                    'tid' => isset($newTracking) ? $newTracking->id : $tracking->id,
                    'ep' => $tracking->expired_at != null ? Carbon::parse($tracking->expired_at)->getTimestamp() : null
                ]);
            } else {
                $tracking->delete();
                return response()->json([
                    'status' => 'error',
                    'message' => 'expired',
                ], 400);
            }

        }

        return response()->json([
            'status' => 'error',
            'message' => 'expired',
        ], 400);
    }

    public function storeCheckoutToken(Request $request)
    {
        $inputs = $request->all();

        if (empty($inputs['s'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot get shop name'
            ], 400);
        }

        if (isValidDomain($inputs['s'])) {
            $inputs['s'] = shopNameFromDomain($inputs['s']);
        }

        $affiliate = $this->getAffiliate($inputs);
        if ($affiliate) {
            $expired = Carbon::now()->addDays($affiliate->cookie_serialize);
            OrderTracking::create([
                'affiliate_id' => $affiliate->id,
                'checkout_token' => $inputs['ct_tk'],
                'shop_name' => $inputs['s'],
                'expired_at' => $expired,
                'created_at' => Carbon::now(),
                'sca_source' => empty($inputs['sca_source']) ? null : substr($inputs['sca_source'], 0, 255),
                'order_id' => empty($inputs['order_id']) ? null : $inputs['order_id']
            ]);
        }

        return response()->json([
            'status' => 'ok',
            'message' => 'success'
        ]);
    }


    public function paid(Request $request)
    {
        $input = $request->all();
        $shopSetting = $this->getShopSetting($input['shopId']);

        if (($shopSetting && $input['source_name'] != 'shopify_draft_order')
            || ($shopSetting && $input['app_id'] == 1354745 && $input['source_name'] == 'shopify_draft_order')
            || ($input['app_id'] == 1175642 || $input['source_name'] == 'partial.ly')
        ) {
            if ($this->orderReferral->notDuplicateOrder($request->header('X-Shopify-Webhook-Id'), $input)) {
                dispatch(new HandleReferral($input, $shopSetting))
                    ->onConnection('redis')
                    ->delay(now()->addSeconds(rand(13, 16)))
                    ->onQueue('referral');
            }
        }

        return response()->json([
            'status' => 'ok'
        ], 200);
    }

    public function checkPartialy($input, $referral)
    {
        // Check referral partially.
        if ($input['app_id'] == 1175642 || $input['source_name'] == 'partial.ly') {
            if ($input['financial_status'] == 'paid') {

                switch ($referral->tracking_type) {
                    case Referral::TRACKING_TYPE_COUPON_PARTIALY_OPEN :
                    {
                        $newTrackingType = Referral::TRACKING_TYPE_COUPON_PARTIALY_PAID;
                        break;
                    }
                    case Referral::TRACKING_TYPE_LINK_PARTIALY_OPEN :
                    {
                        $newTrackingType = Referral::TRACKING_TYPE_LINK_PARTIALY_PAID;
                        break;
                    }
                    case Referral::TRACKING_TYPE_CONNECT_CUSTOMER_PARTIALY_OPEN :
                    {
                        $newTrackingType = Referral::TRACKING_TYPE_CONNECT_CUSTOMER_PARTIALY_PAID;
                        break;
                    }
                    case Referral::TRACKING_TYPE_CONNECT_PRODUCT_PARTIALY_OPEN :
                    {
                        $newTrackingType = Referral::TRACKING_TYPE_CONNECT_PRODUCT_PARTIALY_PAID;
                        break;
                    }
                    default:
                    {
                        $newTrackingType = null;
                        break;
                    }
                }

                $ref = Referral::where('order_id', $input['id'])->get();
                $ref->each(function ($record) use ($newTrackingType) {
                    $record->update(['tracking_type' => $newTrackingType]);
                });
            }
        }
    }

    public function handleOrder($input, $shopSetting)
    {
        $uniqueTimestamp = now()->timestamp;
        try {
            // Check duplicate
            $ref = Referral::where('order_id', $input['id'])->first();
            if ($ref) {
                $this->checkPartialy($input, $ref); // update status partially

                return response()->json([
                    'status' => 'ok',
                    'message' => 'success',
                ], 200);
            }

            /** @var SellingPromoteProductService $sellingPromoteProductService */
            $sellingPromoteProductService = app(SellingPromoteProductService::class);
            $sellingPromoteProductService->updateProductSales($shopSetting->shop_id, $input);
            /** @var OrderTrackingService $orderTrackingService */
            $orderTrackingService = app(OrderTrackingService::class);
            $shopId = $input['shopId'];
            $scaSource = $orderTrackingService->getSourceByCartTokenAndCheckoutToken(
                $shopId,
                $input
            );

            (new OrderPropertyService())->storeOrderProperties((int)$shopId, $input);

            // Tracking by connect customer
            $trackByConnectCustomer = $this->trackingByConnectCustomer($shopSetting, $input, $shopId, $uniqueTimestamp, $scaSource);
            if ($trackByConnectCustomer || $this->isDuplicate) {
                return response()->json([
                    'status' => 'ok',
                    'message' => 'success'
                ], 200);
            }

            if ($shopId == 41249) {
                // Customize for shop 41249 - x-gamer-energy.myshopify.com
                // Tracking by link
                $trackByLink = $this->trackingByAffiliateLink($shopSetting, $input, $shopId, $scaSource);
                if ((isset($trackByLink['status']) && $trackByLink['status']) ||
                    $this->isDuplicate) {
                    return response()->json([
                        'status' => 'ok',
                        'message' => 'success'
                    ], 200);
                }

                // Tracking by coupon
                $trackByCoupon = $this->trackingByCoupon($shopSetting, $input, $shopId, $uniqueTimestamp, $scaSource);
                if ($trackByCoupon || $this->isDuplicate) {
                    return response()->json([
                        'status' => 'ok',
                        'message' => 'success'
                    ], 200);
                }
            } else {
                // All shops
                // Tracking by coupon
                $trackByCoupon = $this->trackingByCoupon($shopSetting, $input, $shopId, $uniqueTimestamp, $scaSource);
                if ($trackByCoupon || $this->isDuplicate) {
                    return response()->json([
                        'status' => 'ok',
                        'message' => 'success'
                    ], 200);
                }

                // Tracking by link
                $trackByLink = $this->trackingByAffiliateLink($shopSetting, $input, $shopId, $scaSource);
            }

            // Tracking product connect to affiliate
            if (!empty($trackByLink['affiliate'])) {
                $this->trackingByConnectProduct($input, $shopSetting, $trackByLink['affiliate'], $scaSource);
                if (isset($input['customer'])) {
                    $this->checkLifeTimeCommission($trackByLink['affiliate'], $input['customer'], $input);
                }
            } else {
                $this->trackingByConnectProduct($input, $shopSetting);
            }

            return response()->json([
                'status' => 'ok',
                'message' => 'success'
            ], 200);
        } catch (Exception $e) {
            if (stripos($e->getMessage(), 'A non well formed numeric value encountered') !== false) {
                logger('A non well log: '.json_encode($input));
            }

            report($e);
            return response()->json([
                'status' => 'ok',
                'message' => 'success'
            ], 200);
        }
    }

    /**
     * @param $shopSetting
     * @param $input
     * @param $shopId
     * @param $uniqueTimestamp
     * @param $sourceName
     * @return bool|void
     */
    private function trackingByConnectCustomer($shopSetting, $input, $shopId, $uniqueTimestamp, $sourceName = null)
    {
        $shop = Shop::find($shopId);
        if (!helperPlan()->planProfessionalUp($shop)) {
            return;
        }
        if ($input['email']) {
            if (!empty($input['customer']['id'])) {
                $connectCustomer = ConnectCustomer::where('shop_id', $shopId)
                    ->where(function ($query) use ($input) {
                        $query->where('customer_email', strtolower($input['email']))
                            ->orWhere('customer_shopify_id', $input['customer']['id']);
                    })
                    ->first();
            } else {
                $connectCustomer = ConnectCustomer::where([
                    'shop_id' => $shopId,
                    'customer_email' => strtolower($input['email'])
                ])->first();
            }

            if ($connectCustomer) {
                $affiliate = Affiliate::where('id', $connectCustomer->affiliate_id)
                    ->where('shop_id', $shopId)
                    ->where('status', 1)
                    ->with('program')
                    ->first();

                if ($affiliate && $this->checkSelfReferral($affiliate, $input['email'])) {
                    $trackingType = Referral::TRACKING_TYPE_CONNECT_CUSTOMER; // 0

                    if ($input['app_id'] == 1175642 || $input['source_name'] == 'partial.ly') {
                        if ($input['financial_status'] == 'paid') {
                            $trackingType = Referral::TRACKING_TYPE_CONNECT_CUSTOMER_PARTIALY_PAID; // 15
                        } else {
                            $trackingType = Referral::TRACKING_TYPE_CONNECT_CUSTOMER_PARTIALY_OPEN; // 11
                        }
                    }

                    $newRef = $this->storeReferral($affiliate, $input, $shopSetting, $trackingType, $sourceName, false, true);
                    if (!$newRef) {
                        return false;
                    }

                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param $shopSetting
     * @param $input
     * @param $shopId
     * @param $uniqueTimestamp
     * @param null $sourceName
     * @return bool
     */
    private function trackingByCoupon($shopSetting, $input, $shopId, $uniqueTimestamp, $sourceName = null)
    {
        if ($shopSetting && $shopSetting->enable_coupon) {
            $couponShopify = $this->orderReferral->getArrayCouponCode($input); 
            $couponsYard = [];
            if (!empty($input['note_attributes'])) {
                $couponsYard = $this->orderReferral->getCouponYard($input['note_attributes']);
            }
            $coupons = array_merge($couponShopify, $couponsYard);
            $couponInDatabase = AffiliateCoupon::where('shop_id', $shopId)->whereIn('coupon', $coupons)->first();

            if ($couponInDatabase) {
                $affiliate = Affiliate::where('id', $couponInDatabase->affiliate_id)
                    ->where('shop_id', $shopId)
                    ->where('status', 1)
                    ->with('program')
                    ->first();

                if ($affiliate && $this->checkSelfReferral($affiliate, $input['email'])) {
                    $trackingType = Referral::TRACKING_TYPE_COUPON; // default 1

                    if ($input['app_id'] == 1175642 || $input['source_name'] == 'partial.ly') {
                        if ($input['financial_status'] == 'paid') {
                            $trackingType = Referral::TRACKING_TYPE_COUPON_PARTIALY_PAID; // 13
                        } else {
                            $trackingType = Referral::TRACKING_TYPE_COUPON_PARTIALY_OPEN; // 9
                        }
                    }

                    $newRef = $this->storeReferral($affiliate, $input, $shopSetting, $trackingType, $sourceName);
                    if (!$newRef) {
                        return false;
                    }

                    if (isset($input['customer'])) {
                        $this->checkLifeTimeCommission($affiliate, $input['customer'], $input);
                    }

                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Tracking by affiliate link
     *
     * @param $shopSetting
     * @param $input
     * @param $shopId
     * @param null $sourceName
     * @return array
     */
    private function trackingByAffiliateLink($shopSetting, $input, $shopId, $sourceName = null)
    {
        $affiliate = false;
        $now = Carbon::now();
        $cartToken = $input['cart_token'];
        $checkoutToken = $input['checkout_token'];

        $byPixel = OrderTrackingMongoDB::query()
            ->where([
                'shop_id' => (int) $shopId,
                'order_id' => (int) $input['id']
            ])
            ->first();

        // Check cart_token or checkout_token in order_tracking table
        $byToken = OrderTracking::where('cart_token', $cartToken)
            ->whereNotNull('cart_token')
            ->orwhere(function ($query) use ($checkoutToken) {
                $query->where('checkout_token', $checkoutToken);
                $query->whereNotNull('checkout_token');
            })
            ->first();


        if ($byToken || $byPixel) {
            OrderTrackingReport::query()->create([
                'order_id' => $input['id'],
                'by_token' => (int) !is_null($byToken),
                'by_pixel' => (int) !is_null($byPixel),
            ]);
        }

        $orderTracking = $byPixel ?: $byToken;

        // Check by link bold or partially or landing_site
        if (!$orderTracking) {
            if ($input['app_id'] == 457101 || $input['app_id'] == 4370661) { // Check by link bold
                $browserFingerprints = md5($input['customer']['email'] . $input['line_items'][0]['variant_id'] . $input['line_items'][0]['product_id']);

                // Check track recurring order
                if ($shopSetting->track_recurring_bold_order) {
                    $orderTracking = $this->getOrderTrackingSubscription($input, $browserFingerprints);
                } else {
                    OrderTracking::where('browser_fingerprints', $browserFingerprints)->delete();
                }
            } else if ($input['app_id'] == 1175642 || $input['source_name'] == 'partial.ly') {
                // Check by link partially
                $browserFingerprints = md5($input['customer']['email'] . $input['line_items'][0]['variant_id'] . $input['line_items'][0]['product_id']);
                $orderTrackingPartially = OrderTracking::where('browser_fingerprints', $browserFingerprints)
                    ->whereNotNull('browser_fingerprints')
                    ->orderBy('id', 'desc')
                    ->first();

                if ($orderTrackingPartially) {
                    $affiliate = Affiliate::where('id', $orderTrackingPartially->affiliate_id)
                        ->where('shop_id', $shopId)
                        ->where('status', 1)
                        ->with('program')
                        ->first();

                    if ($affiliate && $this->checkSelfReferral($affiliate, $input['email'])) {
                        $trackingType = Referral::TRACKING_TYPE_LINK_PARTIALY_OPEN; // 10
                        if ($input['financial_status'] == 'paid') {
                            $trackingType = Referral::TRACKING_TYPE_LINK_PARTIALY_PAID; // 14
                        }

                        $newRef = $this->storeReferral($affiliate, $input, $shopSetting, $trackingType, $orderTrackingPartially->sca_source);
                        if ($affiliate->register_method != 5) {
                            $this->assignDownLineForCustomer($input['customer']['email'], $shopId, $affiliate->id, $input['created_at']);
                        }
                        if (!$newRef) {
                            return [
                                'status' => false,
                                'affiliate' => $affiliate
                            ];
                        }
                        OrderTracking::where('id', $orderTrackingPartially->id)->update(['expired_at' => $now]);

                        return [
                            'status' => true,
                            'affiliate' => $affiliate
                        ];
                    }
                }
            } elseif ($input['app_id'] == 4877949) {
                // Check recurring order from Appstle Subscription
                $browserFingerprints = md5('appstle' . $input['customer']['email'] . $input['line_items'][0]['variant_id'] . $input['line_items'][0]['product_id']);

                // Check track recurring order
                if ($shopSetting->track_appstle_order) {
                    $orderTracking = $this->getOrderTrackingSubscription($input, $browserFingerprints);
                } else {
                    OrderTracking::where('browser_fingerprints', $browserFingerprints)->delete();
                }
            } elseif ($input['app_id'] == 3501525) {
                // Check recurring order from Seal Subscription
                $browserFingerprints = md5('seal' . $input['customer']['email'] . $input['line_items'][0]['variant_id'] . $input['line_items'][0]['product_id']);

                // Check track recurring order
                if ($shopSetting->track_seal_order) {
                    $orderTracking = $this->getOrderTrackingSubscription($input, $browserFingerprints);
                } else {
                    OrderTracking::where('browser_fingerprints', $browserFingerprints)->delete();
                }
            } elseif ($input['app_id'] == 5471895) {
                // Check recurring order from Recurpay Subscription app
                $browserFingerprints = md5('recurpay' . $input['customer']['email'] . $input['line_items'][0]['variant_id'] . $input['line_items'][0]['product_id']);

                // Check track recurring order
                if ($shopSetting->track_recurpay_order) {
                    $orderTracking = $this->getOrderTrackingSubscription($input, $browserFingerprints);
                } else {
                    OrderTracking::where('browser_fingerprints', $browserFingerprints)->delete();
                }
            } elseif ((!empty($input['landing_site']) && str_contains($input['landing_site'], 'sca_ref='))
                || (!empty($input['referring_site']) && str_contains($input['referring_site'], 'sca_ref='))
            ) { // check landing_site, referring_site
                if ($shopId == 84514) {
                    return [
                        'status' => false,
                        'affiliate' => $affiliate
                    ];
                }

                $siteData = str_contains($input['landing_site'], 'sca_ref=') ? $input['landing_site'] : $input['referring_site'];
                $param = explode("sca_ref=", $siteData);

                if (!empty($param[1])) {
                    $sca = explode("&", $param[1]);

                    if (!empty($sca[0])) {
                        $sca = explode("?", $sca[0]);
                        $sca_ref = explode('.', $sca[0]);
                        $affiliateId = $sca_ref[0];
                        $affiliate = Affiliate::where('id', $affiliateId)
                            ->where('shop_id', $shopId)
                            ->where('status', 1)
                            ->with('program')
                            ->first();
                        if (isset($affiliate->program->setting->cookie) && $affiliate->program->setting->cookie < 1)
                            $affiliate = null;

                        if ($affiliate && $this->checkSelfReferral($affiliate, $input['email'])) {
                            $trackingType = Referral::TRACKING_TYPE_LINK; // 2 default track by link

                            if ($input['app_id'] == 457101 || $input['app_id'] == 4370661) { // Check by link bold
                                $trackingType = Referral::TRACKING_TYPE_APP_RECURRING; // app recurring
                            }

                            $orderTrackingScaSource = NULL;
                            $newRef = $this->storeReferral($affiliate, $input, $shopSetting, $trackingType, $orderTrackingScaSource);
                            if ($affiliate->register_method != 5) {
                                $this->assignDownLineForCustomer($input['customer']['email'], $shopId, $affiliate->id, $input['created_at']);
                            }
                            if (!$newRef) {
                                return [
                                    'status' => false,
                                    'affiliate' => $affiliate
                                ];
                            }

                            return [
                                'status' => true,
                                'affiliate' => $affiliate
                            ];
                        }
                    }
                }
            } else {
                // Integrate with Razorpay app
                if (!empty($input['note_attributes'])) {
                    foreach ($input['note_attributes'] as $note) {
                        if ($note['name'] == 'cart_token' || $note['name'] == '_cart_token') {
                            $cartToken = Str::before(trim($note['value']), '?key=');
                            $arrayTemp = explode('%', $cartToken);
                            $cartToken = !empty($arrayTemp[0]) ? $arrayTemp[0] : $cartToken;
                            $orderTracking = OrderTracking::where('cart_token', $cartToken)
                                ->whereNotNull('cart_token')
                                ->first();
                        }
                    }
                }
            }
        }

        if ($orderTracking && $shopSetting) {
            $affiliate = Affiliate::where('id', $orderTracking->affiliate_id)
                ->where('shop_id', $shopId)
                ->where('status', 1)
                ->with('program')
                ->first();

            if ($affiliate && $this->checkSelfReferral($affiliate, $input['email'])) {
                $trackingType = Referral::TRACKING_TYPE_LINK; // 2 default track by link

                if (in_array($input['app_id'], [457101, 4370661, 4877949, 3501525, 5471895])) {
                    $trackingType = Referral::TRACKING_TYPE_APP_RECURRING; // app recurring
                }

                // code by Bui Tien Vinh
                // Start code block
                if ($orderTracking->sca_source) {
                    $scaSource = $orderTracking->sca_source;
                } else {
                    $scaSource = $sourceName;
                }

                $newRef = $this->storeReferral($affiliate, $input, $shopSetting, $trackingType, $scaSource);
                // End code block by Bui Tien Vinh

                if ($affiliate->register_method != 5) {
                    $this->assignDownLineForCustomer($input['customer']['email'], $shopId, $affiliate->id, $input['created_at']);
                }

                if (!$newRef) {
                    return [
                        'status' => false,
                        'affiliate' => $affiliate
                    ];
                }

                try {
                    // Ignore removing order tracking with Appstle, Bold, Seal, Recurpay
                    if (!in_array($input['app_id'], [457101, 4370661, 4877949, 3501525, 5471895])) {
                        $detail = OrderDetail::where([
                            'shop_id' => $shopId,
                            'referral_id' => $newRef->id
                        ])->first();
                        $createdDateOrderTracking = $orderTracking instanceof OrderTrackingMongoDB
                            ? $orderTracking->getAttribute('created_at')->timestamp
                            : Carbon::parse($orderTracking->getAttribute('created_at'))->timestamp;

                        $detail->other_input = [
                            'click_date' => $createdDateOrderTracking
                        ];
                        $detail->save();

                        $orderTracking->update(['expired_at' => $now]);
                    }
                } catch (\Exception $e) {
                }

                return [
                    'status' => true,
                    'affiliate' => $affiliate
                ];
            }
        }
        return [
            'status' => false,
            'affiliate' => $affiliate
        ];
    }

    /**
     * Customize for shop asseenonyt-np.myshopify.com
     *
     * @param $shopSetting
     * @param $input
     * @param $shopId
     * @param $uniqueTimestamp
     * @param $affiliateEmail
     * @param $sourceName
     * @return bool
     */
    private function trackingByStaffAccount($shopSetting, $input, $shopId, $uniqueTimestamp, $affiliateEmail, $sourceName = null)
    {
        $affiliate = Affiliate::where('email', $affiliateEmail)
            ->where('shop_id', $shopId)
            ->where('status', 1)
            ->with('program')
            ->first();

        if ($affiliate && $this->checkSelfReferral($affiliate, $input['email'])) {
            $trackingType = Referral::TRACKING_TYPE_CONNECT_CUSTOMER;
            $newRef = $this->storeReferral($affiliate, $input, $shopSetting, $trackingType, $sourceName);
            if (!$newRef) {
                return false;
            }

            return true;
        }

        return false;
    }

    protected function getShopSetting($shopId)
    {
        return MerchantSetting::where('shop_id', $shopId)
            ->select(
                'id',
                'shop_id',
                'auto_approve_order',
                'enable_coupon',
                'enable_analytics',
                'is_store_customer',
                'record_pending_order',
                'track_recurring_bold_order',
                'track_appstle_order',
                'track_seal_order',
                'track_recurpay_order',
                'delay_auto_approve_order'
            )->first();
    }

    /**
     * @param $affiliate
     * @param $customerEmail
     * @return bool
     */
    private function checkSelfReferral($affiliate, $customerEmail)
    {
        $program = $affiliate->program;
        if ($affiliate->register_method == Affiliate::REG_BY_CUSTOMER_REFERRAL) {
            $program = Program::select('ignore_self_referral')
                ->where('id', $affiliate->program_id)
                ->whereNotNull('refer_customer_incentive')
                ->whereNotNull('deleted_at')
                ->onlyTrashed()
                ->first();
        }

        if ($program->ignore_self_referral && !empty($customerEmail)) {
            if (strcasecmp($affiliate->email, $customerEmail) != 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * @param $items
     * @return float|int
     */
    private function getSubtotalPriceProductConnect($items)
    {
        $totalPrice = 0;
        foreach ($items as $i) {
            $totalDiscount = $this->getTotalDiscount($i['discount_allocations']);
            $totalPrice += (($i['price'] * $i['quantity']) - $totalDiscount);
        }
        return $totalPrice;
    }

    /**
     * @param $discounts
     * @return int
     */
    private function getTotalDiscount($discounts)
    {
        $totalDiscount = 0;
        foreach ($discounts as $d) {
            $totalDiscount += $d['amount'];
        }

        return $totalDiscount;
    }

    /**
     * @param $productConnected
     * @param $orderLineItem
     * @return array
     */
    private function getProductConnectLineItem($productConnected, $orderLineItem)
    {
        $productConnectLineItem = [];
        foreach ($productConnected as $p) {
            foreach ($orderLineItem as $i) {
                if ($p->variant_id == $i['variant_id']) {
                    $productConnectLineItem[] = $i;
                }
            }
        }
        return $productConnectLineItem;
    }

    /**
     * @param $input
     * @param $shopSetting
     * @param $affiliate
     * @param $sourceName
     * @return false|void
     */
    private function trackingByConnectProduct($input, $shopSetting, $affiliate = null, $sourceName = null)
    {
        $variantIds = $this->getVartiantId($input['line_items']);
        $productConnect = ConnectProductAffiliate::whereIn('variant_id', $variantIds)->get();
        $isLinkBefore = $affiliate !== null;

        $shopId = $shopSetting->shop_id;
        $shop = Shop::find($shopId);
        if (!helperPlan()->planProfessionalUp($shop)) {
            return;
        }

        if ($productConnect) {
            $productConnect = $productConnect->groupBy('affiliate_id');

            foreach ($productConnect as $affId => $pc) {
                if ($affiliate && $affId == $affiliate->id) {
                    continue;
                }
                $productConnectLineItem = $this->getProductConnectLineItem($pc, $input['line_items']);
                $subtotalPriceProductConnect = $this->getSubtotalPriceProductConnect($productConnectLineItem);
                $newDataInput = $input;
                $newDataInput['line_items'] = $productConnectLineItem;
                $newDataInput['subtotal_price'] = $subtotalPriceProductConnect;
                $newDataInput['total_tax'] = $this->orderReferral->calTaxByProduct($productConnectLineItem);
                $affiliate = Affiliate::where('id', $affId)->where('status', 1)->with('program')->first();

                if ($affiliate && $this->checkSelfReferral($affiliate, $input['email'])) {
                    $trackingType = Referral::TRACKING_TYPE_CONNECT_PRODUCT; // 3

                    if ($input['app_id'] == 1175642 || $input['source_name'] == 'partial.ly') {
                        if ($input['financial_status'] == 'paid') {
                            $trackingType = Referral::TRACKING_TYPE_CONNECT_PRODUCT_PARTIALY_PAID; // 16
                        } else {
                            $trackingType = Referral::TRACKING_TYPE_CONNECT_PRODUCT_PARTIALY_OPEN; // 12
                        }
                    }

                    $newRef = $this->storeReferral($affiliate, $newDataInput, $shopSetting, $trackingType, $sourceName, $isLinkBefore);
                    if (!$newRef) {
                        return false;
                    }
                }
            }
        }
    }

    private function checkLifeTimeCommission($affiliate, $customer, $input): bool
    {
        $shop = Shop::find($affiliate->shop_id);

        if ($affiliate->register_method == Affiliate::REG_BY_CUSTOMER_REFERRAL ||
            !(helperPlan()->planProfessionalUp($shop))
        ) {
            return true;
        } else {
            if ($affiliate->program->lifetime_commission && $customer) {
                if ($customer['email']) {
                    $customerAddress = !empty($input['shipping_address']) ? $this->getShippingAddress($input['shipping_address']) : NULL;
                    if (empty($customerAddress) && !empty($customer['default_address'])) {
                        $customerAddress = $this->getShippingAddress($customer['default_address']);
                    }

                    $customerPhone = !empty($customer['default_address']['phone']) ? $customer['default_address']['phone'] : NULL;
                    if (empty($customerPhone)) {
                        try {
                            $client = new Client(['allow_redirects' => true]);
                            $request = new \GuzzleHttp\Psr7\Request('GET', "https://{$shop->shop}/admin/api/" . config('shopify.api_version') . "/customers/search.json?query=email:{$customer['email']}", [
                                'X-Shopify-Access-Token' => $shop->access_token
                            ]);
                            $response = $client->send($request);
                            $content = $response->getBody()->getContents();
                            $customerData = json_decode($content, true);
                            $customerData = !empty($customerData['customers'][0]) ? $customerData['customers'][0] : [];

                            if ($customerData) {
                                $customerPhone = !empty($customerData['phone']) ? $customerData['phone'] : NULL;

                                if (empty($customerPhone) && !empty($customerData['addresses'])) {
                                    foreach ($customerData['addresses'] as $val) {
                                        if ((!empty($val['phone']))) {
                                            $customerPhone = $val['phone'];
                                            break;
                                        }
                                    }
                                }
                            }
                        } catch (Exception $exception) {
                            logger()->error($exception->getMessage());
                        }
                    }

                    $connect = ConnectCustomer::firstOrCreate(
                        [
                            'customer_email' => strtolower($customer['email']),
                            'shop_id' => $affiliate->shop_id
                        ],
                        [
                            'affiliate_id' => $affiliate->id,
                            'customer_name' => $customer['first_name'] . ' ' . $customer['last_name'],
                            'customer_shopify_id' => $customer['id'],
                            'customer_address' => $customerAddress,
                            'customer_phone' => $customerPhone,
                            'customer_type' => ConnectCustomer::CUSTOMER_TYPE_LIFETIME
                        ]
                    );

                    // Handle sync multiple shop connected customer
                    dispatchSyncDataSignal(
                        SynchronousModelType::CONNECT_CUSTOMER,
                        SynchronousModelType::CONNECT_CUSTOMER_ACTION['ON_SET']
                    )
                        ->setArguments($connect->id)
                        ->setIdentity($affiliate->shop_id)
                        ->dispatch();
                }
            }
        }

        return true;
    }

    private function getVartiantId($items)
    {
        $variantIds = [];
        foreach ($items as $i) {
            $variantIds[] = $i['variant_id'];
        }
        return $variantIds;
    }

    private function classifyProduct($items, $programId)
    {
        $variantIds = $this->getVartiantId($items);
        $specialVariant = Product::whereIn('variant_id', $variantIds)
            ->where('program_id', $programId)
            ->select('variant_id', 'commission_type', 'commission_amount')
            ->get();

        if ($specialVariant->count() > 0) {
            foreach ($specialVariant as $sv) {
                foreach ($items as $k => $i) {
                    if ($sv['variant_id'] == $i['variant_id']) {
                        $items[$k]['commission_type'] = $sv['commission_type'];
                        $items[$k]['commission_amount'] = $sv['commission_amount'];
                    }
                }
            }

            $this->lineItems = $items;
            return $items;
        } else {
            return false;
        }
    }

    private function calDiscount($discountAllocations, $input, $specialProduct = false)
    {
        $total = 0;
        foreach ($discountAllocations as $d) {
            $total += $d['amount'];
        }

        if ($specialProduct) {
            return $total;
        }

        // handle exclude discount product/ collection
        if (!empty($input['line_items']) && is_array($input['line_items'])) {
            foreach ($input['line_items'] as $item) {
                if (isset($item['exclude_product_collection']) && isset($item['discount_allocations'])) {
                    foreach ($item['discount_allocations'] as $value) {
                        $total -= $value['amount'];
                    }
                }
            }
        }

        return $total;
    }

    /**
     * @param $refundItem
     * @return float|int
     */
    private function calDiscountRefund($refundItem)
    {
        $total = 0;
        if (!isset($refundItem['exclude_product_collection']))
        {
            $discountAllocations = $refundItem['line_item']['discount_allocations'];
            foreach ($discountAllocations as $d) {
                $total += $d['amount'] / $refundItem['line_item']['quantity'] * $refundItem['quantity'];
            }
        }

        return $total;
    }

    private function calCommissionSpecial($item, $programAmount, $taxesIncluded, $program, $shop, $input)
    {
        $discount = $this->calDiscount($item['discount_allocations'], $input, true);

        if ($item['commission_type'] == Program::FLAT_RATE_PER_ORDER) {
            return $item['commission_amount'];
        } else {
            if ($item['commission_type'] == Program::FLAT_RATE_PER_ITEM) {
                if (($item['price'] * $item['quantity'] == $discount) && ($item['price'] != 0)) { //check free-gift
                    return 0;
                } else {
                    return $item['commission_amount'] * $item['quantity'];
                }
            } else { // 2: Percent Of Sale
                if (!empty($program) && $program->exclude_tax) {
                    if ($program->include_eu_vat) {
                        $taxLinePrice = !empty($item['tax_lines'][0]['price']) ? $item['tax_lines'][0]['price'] : 0;
                        return ($item['quantity'] * $item['price'] - $taxLinePrice - $discount) * $item['commission_amount'] / 100; // EU
                    }
                }

                return ($item['quantity'] * $item['price'] - $discount) * ($item['commission_amount'] / 100);
            }
        }
    }

    private function calCommission($commissionType, $commissionAmount, $totalItem, $sales, $input, $programId, $program, $taxesIncluded, $shop)
    {
        $items = $input['line_items'];
        $commission = 0;
        $classifiedProducts = $this->classifyProduct($items, $programId);
        if ($classifiedProducts) {
            $classifiedProducts  = array_filter($classifiedProducts, function ($specialProduct) { // Remove item isset key: exclude_product_collection
                return !isset($specialProduct['exclude_product_collection']);
            });
        }

        if ($commissionType == Program::FLAT_RATE_PER_ORDER) {
            $commission += $commissionAmount;
            if ($classifiedProducts) {
                foreach ($classifiedProducts as $p) {
                    if (array_key_exists('commission_type', $p)) {
                        $commission += $this->calCommissionSpecial($p, $commissionAmount, $taxesIncluded, $program, $shop, $input);
                    }
                }
            }
        } else if ($commissionType == Program::FLAT_RATE_PER_ITEM) {
            if ($classifiedProducts) {
                foreach ($classifiedProducts as $p) {
                    if (array_key_exists('commission_type', $p)) {
                        $commission += $this->calCommissionSpecial($p, $commissionAmount, $taxesIncluded, $program, $shop, $input);
                    } else {
                        $discount = $this->calDiscount($p['discount_allocations'], $input, true);
                        if ($p['price'] * $p['quantity'] != $discount) {
                            $commission += $commissionAmount * $p['quantity'];
                        }
                    }
                }
            } else {
                $commission = $commissionAmount * $totalItem;
            }
        } else {
            if ($classifiedProducts) {
                foreach ($classifiedProducts as $p) {
                    if (array_key_exists('commission_type', $p)) {
                        $commission += $this->calCommissionSpecial($p, $commissionAmount, $taxesIncluded, $program, $shop, $input);
                    } else {
                        $discount = $this->calDiscount($p['discount_allocations'], $input, true);
                        $commission += ($p['price'] * $p['quantity'] - $discount) * ($commissionAmount / 100);
                        if ($program->exclude_tax) {
                            if ($program->include_eu_vat) {
                                $taxLinePrice = !empty($p['tax_lines'][0]['price']) ? $p['tax_lines'][0]['price'] : 0;
                                $commission = $commission - $taxLinePrice * $commissionAmount / 100;
                            }
                        }
                    }
                }

                if (!$program->include_eu_vat && !$program->exclude_tax) {
                    // Include tax US
                    $commission = $commission + $input['total_tax'] * $commissionAmount / 100;
                }

                if (!$program->exclude_shipping) {
                    $commission = $commission + $input['total_shipping_price_set']['shop_money']['amount'] * $commissionAmount / 100;
                }
            } else {
                $commission = $sales * $commissionAmount / 100;
            }
        }

        return $commission;
    }

    /**
     * @param $order
     * @param $refundItems
     * @param $itemsDetail
     * @param $totalRefunded
     * @return float|int
     */
    private function calRefundCommission($order, $refundItems, &$itemsDetail, $totalRefunded)
    {
        $orderDetail = json_decode($order->item_detail);
        if (!isset($orderDetail->subtotal_price)) {
            $preItems = $orderDetail;
        } else {
            $preItems = $orderDetail->line_items;
        }

        $commission = 0;
        $specialProduct = false;
        foreach ($preItems as $p) {
            foreach ($refundItems as $r) {
                if ($p->variant_id == $r['line_item']['variant_id']) {
                    $discount = $this->calDiscountRefund($r);

                    if (!isset($p->exclude_product_collection))
                    {
                        if (isset($p->commission_amount)) { // product commission
                            $specialProduct = true;
                            if ($p->commission_type == Program::FLAT_RATE_PER_ITEM) {
                                $commission += $r['quantity'] * $p->commission_amount;
                            } else if ($p->commission_type == Program::PERCENT_OF_SALE) {
                                $commission += ($r['quantity'] * $r['line_item']['price'] - $discount) * $p->commission_amount / 100;
                            }
                        } else {
                            if ($order->commission_type == Program::FLAT_RATE_PER_ITEM) {
                                $commission += $order->commission_amount * $r['quantity'];
                            } else if ($order->commission_type == Program::PERCENT_OF_SALE) {
                                $commission += $r['subtotal'] * $order->commission_amount / 100;
                            }
                        }

                        if ($order->commission_type == Program::FLAT_RATE_PER_ORDER) {
                            $totalRefunded = DB::table('referrals')
                                ->where('order_id', $order->order_id)
                                ->whereNotNull('refund_id')
                                ->sum('total');

                            if ((-1) * $totalRefunded == ($order->total - $r['subtotal'])) {
                                $commission += $order->commission_amount;
                            }
                        }
                    }

                    $p->quantity = $r['quantity'];
                    $itemsDetail[] = $p;
                }
            }
        }

        $itemsDetail = json_encode($itemsDetail);
        if ($order->commission_type == Program::PERCENT_OF_SALE && !$specialProduct) {
            return $totalRefunded * $order->commission_amount / 100;
        }

        return (-1) * $commission;
    }

    private function checkGiftCard($order)
    {
        if (isset($order['payment_gateway_names'])) {
            foreach ($order['payment_gateway_names'] as $v) {
                if ($v == 'gift_card') {
                    return true;
                }
            }
        }

        return false;
    }

    private function getShippingAddress($shippingAddress)
    {
        $address = '';
        if ($shippingAddress['name']) {
            $address = $address . $shippingAddress['name'];
        }
        if (isset($shippingAddress['address1'])) {
            $address = $address . ',' . $shippingAddress['address1'];
        }
        if (isset($shippingAddress['address2'])) {
            $address = $address . ',' . $shippingAddress['address2'];
        }
        if (isset($shippingAddress['city'])) {
            $address = $address . ',' . $shippingAddress['city'];
        }
        if (isset($shippingAddress['province'])) {
            $address = $address . ',' . $shippingAddress['province'];
        }
        if (isset($shippingAddress['country'])) {
            $address = $address . ',' . $shippingAddress['country'];
        }
        if (isset($shippingAddress['zip'])) {
            $address = $address . ',zipcode:' . $shippingAddress['zip'];
        }

        return $address;

    }

    private function storeCustomerInfo($shopId, $referralIds, $customer, $shippingAddress)
    {
        foreach ($referralIds as $id) {
            if ($id) {
                CustomerInfo::create([
                    'shop_id' => $shopId,
                    'referral_id' => $id,
                    'email' => strtolower($customer['email']),
                    'full_name' => $customer['first_name'] . ' ' . $customer['last_name'],
                    'customer_id' => $customer['id'],
                    'shipping_address' => $shippingAddress
                ]);
            }
        }
    }

    /**
     * Refund hook from Shopify
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function refunds(Request $request)
    {
        try {
            if ($this->orderReferral->notDuplicateOrder($request->header('X-Shopify-Webhook-Id'), $request->all())) {
                dispatch(new ReferralRefund($request->all()))
                    ->onQueue('referral_refund')
                    ->delay(now()->addSeconds(17));
            }
        } catch (Exception $e) {
            report($e);
        }

        return response()->json([
            'status' => 'ok',
            'message' => 'success'
        ], 200);
    }

    /**
     * @param $input
     * @param $referralOriginal
     * @param $detail
     * @return JsonResponse|void
     */
    public function processRefunds($input, $referralOriginal, $detail)
    {
        try {
            $itemsDetail = null;
            $shopSetting = MerchantSetting::where('shop_id', $referralOriginal->shop_id)->first();
            $refund = DB::table('referrals')->where('refund_id', $input['id'])->first();

            if (!$refund && !empty($input['transactions'])) {
                $totalTransactionAmount = $input['transactions'][0]['amount'];
                if ($detail) {
                    $referralOriginal->order_detail = $detail->order_detail ? json_encode($detail->order_detail, true) : null;
                    $referralOriginal->item_detail = $detail->item_detail ? json_encode($detail->item_detail, true) : null;
                }

                // Handle exclude product/ collection
                $excludeProductCollection = false;
                if ($referralOriginal->exclude_product_collection && !empty($detail->order_detail['line_items'])) {
                    /** @var OrderReferral $referralOriginalReferral */
                    $referralOriginalReferral = app(OrderReferral::class);
                    $excludeProductDataRefund = $referralOriginalReferral->handleExcludeProductCollectionRefund($input, $detail->order_detail['line_items']);
                    if ($excludeProductDataRefund['status'] == 'not-record') {
                        return; // cancel record order refund
                    } else {
                        $excludeProductCollection = true;
                        $input['refund_line_items'] = $excludeProductDataRefund['refund_line_items']; // replace refund_line_items add key exclude_product_collection
                        $input['transactions'][0]['amount'] = $excludeProductDataRefund['total_sale_exclude']; // replace total amount refund exclude product/ collection
                    }
                }

                $totalItem = countItemsInOrder($input['refund_line_items']);
                if ($totalTransactionAmount == json_decode($referralOriginal->item_detail, true)['total_price']
                    || $input['transactions'][0]['kind'] == 'void'
                ) {
                    $commission = (-1) * $referralOriginal->commission;
                    $itemsDetail = $referralOriginal->item_detail;
                    $totalPriceRefund = -$referralOriginal->total;
                } else {
                    $totalPriceRefund = -$input['transactions'][0]['amount'];

                    if ($referralOriginal->exclude_shipping && !empty($input['order_adjustments'])) {
                        foreach ($input['order_adjustments'] as $referralOriginalAdjustment) {
                            if (!empty($referralOriginalAdjustment['kind']) && $referralOriginalAdjustment['kind'] == 'shipping_refund') {
                                if ($referralOriginal->exclude_tax == 1 && $referralOriginalAdjustment['tax_amount'] > 0) {
                                    $totalPriceRefund += (-1) * ($referralOriginalAdjustment['amount'] + $referralOriginalAdjustment['tax_amount']);
                                } else {
                                    $totalPriceRefund += (-1) * $referralOriginalAdjustment['amount'];
                                }
                            }
                        }
                    }

                    $variantIdExclude = [];
                    if ($excludeProductCollection && !empty($referralOriginal->item_detail)) {
                        $lineItemsOrders = json_decode($referralOriginal->item_detail, true);
                        $lineItemsOrders = $lineItemsOrders['line_items'] ?? null;
                        if ($lineItemsOrders) {
                            foreach ($lineItemsOrders as $lineItemsOrder) {
                                if (isset($lineItemsOrder['exclude_product_collection'])) {
                                    $variantIdExclude[] = $lineItemsOrder['variant_id'];
                                }
                            }
                        }
                    }

                    if ($referralOriginal->exclude_tax == 1) {
                        foreach ($input['refund_line_items'] as $refundLineItem) {
                            if (isset($refundLineItem['total_tax']) && $refundLineItem['total_tax'] > 0) {
                                if (isset($refundLineItem['line_item']['variant_id'])
                                    && in_array($refundLineItem['line_item']['variant_id'], $variantIdExclude)
                                    && in_array($referralOriginal->include_eu_vat, [1, 2])
                                )
                                {
                                    $refundLineItem['total_tax'] = 0;
                                }

                                $totalPriceRefund += $refundLineItem['total_tax'];
                            }
                        }
                    } else { // include tax
                        if (in_array($referralOriginal->include_eu_vat, [0, -1])) {
                            foreach ($input['refund_line_items'] as $refundLineItem) {
                                if (isset($refundLineItem['total_tax']) && $refundLineItem['total_tax'] > 0) {
                                    if (isset($refundLineItem['line_item']['variant_id'])
                                        && in_array($refundLineItem['line_item']['variant_id'], $variantIdExclude)
                                    )
                                    {
                                        $totalPriceRefund += $refundLineItem['total_tax'];
                                    }
                                }
                            }
                        }
                    }

                    $commission = $this->calRefundCommission($referralOriginal, $input['refund_line_items'], $itemsDetail, $totalPriceRefund);
                }

                // Check order refund gift_card
                $gift_card = false;
                if (isset($input['transactions'][0]['gateway']) && $input['transactions'][0]['gateway'] === 'gift_card') {
                    $gift_card = true;
                }

                $shop = Shop::find($referralOriginal->shop_id);
                $status = Referral::PENDING;
                $isActive = $referralOriginal->is_active;
                if (helperPlan()->planProfessionalUp($shop)) {
                    if ($isActive && $shopSetting->auto_approve_order && ($shopSetting->delay_auto_approve_order == 0)) {
                        $status = Referral::APPROVED;
                    }
                } else {
                    $status = $isActive ? $shopSetting->auto_approve_order : Referral::PENDING;
                }

                $currentTime = Carbon::now();
                $dataRefund = [
                    'refund_id' => $input['id'],
                    'shop_id' => $referralOriginal->shop_id,
                    'order_id' => $input['order_id'],
                    'order_number' => $referralOriginal->order_number,
                    'affiliate_id' => $referralOriginal->affiliate_id,
                    'customer_id' => $referralOriginal->customer_id,
                    'quantity' => $totalItem,
                    'total' => $totalPriceRefund,
                    'commission' => $commission,
                    'program_id' => $referralOriginal->program_id,
                    'commission_type' => $referralOriginal->commission_type,
                    'commission_amount' => $referralOriginal->commission_amount,
                    'status' => $status,
                    'is_active' => $isActive,
                    'tracking_type' => $referralOriginal->tracking_type,
                    'exclude_shipping' => $referralOriginal->exclude_shipping,
                    'exclude_tax' => $referralOriginal->exclude_tax,
                    'include_eu_vat' => $referralOriginal->include_eu_vat,
                    'exclude_product_collection' => $referralOriginal->exclude_product_collection,
                    'created_at' => $currentTime,
                    'updated_at' => $currentTime,
                    'created_at_year' => $currentTime->year,
                    'created_at_month' => $currentTime->month,
                    'currency' => $referralOriginal->currency,
                ];

                if (!$gift_card) {
                    $referral = new Referral;
                    $referral->fill($dataRefund);
                    $referral->save();
                    $this->orderReferral->createDetailReferral($referralOriginal->shop_id, $referral->id, $itemsDetail, null);
                    dispatch(new ReferralRefundCreateListenerJob($referral, $shopSetting, $isActive));
                }
            }

            return response()->json([
                'status' => 'ok',
                'message' => 'success'
            ]);
        } catch (Exception $e) {
            report($e);
            return response()->json([
                'status' => 'ok',
                'message' => 'success'
            ]);
        }
    }

    public function updateHook($shop)
    {

        $clientApi = session('client_api');
        $clientApi->setAccessToken($shop->access_token);
        $clientApi->setShopName(shopNameFromDomain($shop->shop));
        $webhook = new Webhook($clientApi);
        $hooks = $webhook->all();
        foreach ($hooks as $h) {
            if ($h->topic == 'orders/paid') {
                $webhook->update($h->id, route('webhook.order_paid', ['shopId' => $shop->id]));
            }
            if ($h->topic == 'refunds/create') {
                $webhook->update($h->id, route('webhook.order_refunds', ['shopId' => $shop->id]));
            }
        }
        return $webhook->all();
    }


    private function storeBoldTrackingv2($affId, $input)
    {
        $expired = Carbon::now()->addDays(365);
        foreach ($input['line_items'] as $l) {
            $browserFingerprints = md5($input['customer']['email'] . $l['variant_id'] . $l['product_id']);
            $newOrderTracking = new OrderTracking;
            $newOrderTracking->affiliate_id = $affId;
            $newOrderTracking->created_at = Carbon::now();
            $newOrderTracking->expired_at = $expired;
            $newOrderTracking->browser_fingerprints = $browserFingerprints;
            $newOrderTracking->cart_token = $input['cart_token'];
            $newOrderTracking->save();
        }
    }

    /**
     * @param $affiliate
     * @param $input
     * @param $shopSetting
     * @param $trackingType
     * @param $source
     * @param bool $isLinkBefore
     * @param bool $isConnectedCustomer
     * @return Referral|void|null
     */
    private function storeReferral($affiliate, $input, $shopSetting, $trackingType, $source = null, bool $isLinkBefore = false, bool $isConnectedCustomer = false)
    {
        $idInput = $input['id'];
        $orderId = Referral::select('id')->where('order_id', $idInput)->where('shop_id', $affiliate->shop_id)->first();
        $detectRefer = 0;
        if (!$orderId && !$isLinkBefore) {
            $detectRefer = $this->orderReferral->storeDetectReferral($affiliate, $input);
            if ($detectRefer == 2) {
                return;
            }
        }

        /** @var OrderReferral $orderReferral */
        $orderReferral = app(OrderReferral::class);
        $this->lineItems = $input['line_items'];
        $shopId = $affiliate->shop_id;
        $shop = Shop::find($shopId);
        $shopInfoCurrency = $shop->info->currency;

        $isActive = checkIsActiveReferrals($shopId);
        $program = $affiliate->program;
        if (!$this->orderReferral->isAvailableProgramPlan($program, $affiliate, $shop)) return null; // check record || not record order shop plan = Free || Feature add-on
        if ($affiliate->register_method == Affiliate::REG_BY_CUSTOMER_REFERRAL) {
            if (helperPlan()->planGrowthUp($shop)) {
                $program = Program::where('id', $affiliate->program_id)
                    ->whereNotNull('refer_customer_incentive')
                    ->whereNotNull('deleted_at')
                    ->onlyTrashed()
                    ->first();
                $trackingType = Referral::TRACKING_TYPE_REFER_CUSTOMER;
            } else {
                return null;
            }
        }
        $commissionType = $program->commission_type;
        $commissionAmount = $program->commission_amount;
        $isFirstCommission = false;
        $referralRule = NULL;

        // Check enable first commission or not
        if (helperPlan()->planProfessionalUp($shop)
            && $program->lifetime_commission
            && $program->enable_first_commission
            && $program->first_commission_amount != null
        ) {
            if (isset($input['customer']['id']) && $input['customer']['id'] != null
            ) {
                $customerExists = Referral::where('shop_id', $shopId)
                    ->where('customer_id', $input['customer']['id'])
                    ->first();

                if (!$customerExists) {
                    $commissionType = $program->first_commission_type;
                    $commissionAmount = $program->first_commission_amount;
                    $isFirstCommission = true;
                }
            }
        }

        // Calculator subtotal
        $subtotal = $input['subtotal_price'];
        $totalTaxOrder = $input['total_tax'];
        $excludeProductCollectionData = $orderReferral->handleExcludeProductCollection($shop, $affiliate, $input['line_items']);
        if ($excludeProductCollectionData['status'] == 'not-record') {
            return;
        } else {
            $input['line_items'] = $excludeProductCollectionData['line_item'];
            $this->lineItems = $excludeProductCollectionData['line_item'];
            $subtotal -= $excludeProductCollectionData['total_sale_exclude'];
            $totalTaxExcludeProduct = $orderReferral->totalTaxExcludeProductCollection($input['line_items']);
            $totalTaxOrder -= $totalTaxExcludeProduct;
        }

        $totalItem = countItemsInOrder($input['line_items']);
        $currentTime = Carbon::now();
        if (!$program->exclude_shipping) { // include shipping
            $subtotal = $subtotal + $input['total_shipping_price_set']['shop_money']['amount'];
        }

        if ($program->exclude_tax) {
            if ($program->include_eu_vat == 1) {
                $subtotal = $subtotal - $input['total_tax'] + $totalTaxExcludeProduct;
            }

            if ($program->include_eu_vat == 2) {
                // Customize for shop nuchido-uk - 34401
                if (!$this->isNuchidoNonUk($shopId, $input['customer'])) {
                    // Exclude shipping tax (EU shops)
                    $shippingTax = 0;
                    if (isset($input['shipping_lines'])) {
                        $hasShippingTaxValueInOrder = false;
                        foreach ($input['shipping_lines'] as $shippingLine) {
                            if (isset($shippingLine['tax_lines'])) {
                                foreach ($shippingLine['tax_lines'] as $taxLine) {
                                    if (isset($taxLine['price'])) {
                                        $shippingTax = $taxLine['price'];
                                        $hasShippingTaxValueInOrder = true;
                                    }
                                }
                            }
                        }

                        if (!$hasShippingTaxValueInOrder) {
                            $shippingExcludeVAT = round($input['total_shipping_price_set']['shop_money']['amount'] * 100 / 120, 2);
                            $shippingTax = round($shippingExcludeVAT * 20 / 100, 2);
                        }
                    }

                    $totalTaxOrder -= $shippingTax;
                    $subtotal += $totalTaxExcludeProduct;
                    if (!$program->exclude_shipping) { // include shipping
                        $subtotal = $subtotal - $input['total_tax'];
                    } else {
                        $subtotal = $subtotal - $input['total_tax'] + $shippingTax;
                    }
                }
            }
        } else {
            if ($program->include_eu_vat == 0 || $program->include_eu_vat == -1) {
                $subtotal = $subtotal + $input['total_tax'] - $totalTaxExcludeProduct;
            }
        }

        $ruleCommission = $program->rule;
        if (!$isFirstCommission) {
            // modify commission type and commission amount from subscription app
            $commissionSubscriptionSetting = $this->commissionSubscriptionsService->findSetting($shop->id);
            if (helperPlan()->planProfessionalUp($shop) &&
                $trackingType === Referral::TRACKING_TYPE_APP_RECURRING &&
                $commissionSubscriptionSetting &&
                $commissionSubscriptionSetting->active)
            {
                    $commissionType = $commissionSubscriptionSetting->commission_type;
                    $commissionAmount = $commissionSubscriptionSetting->commission_amount;
                    $referralRule = Referral::REFERRAL_RULE_SUBSCRIPTION;
            } else {
                $checkCommissioning = $this->orderReferral->checkCommissioning($input, $affiliate, $shop, $ruleCommission, $program, $shopSetting); // Check advance program commissioning, simple rule program
                if ($checkCommissioning['status']) {
                    $commissionAmount = $checkCommissioning['amount'];
                    $referralRule = 1; // Referral use commissioning
                }

                if ($ruleCommission == Program::ADVANCE_RULE) { // Check advance Commission Structure
                    $commissionAmount = $this->calAdvanceCommission($subtotal, $program->id);
                }
            }
        }

        //check for New Customers
        $checkFirstOrderCustomer = $orderReferral->isFirstOrderCustomer($input, $shop, $affiliate->program->setting->commission_for_new_customer);
        if (!$isFirstCommission && !$isConnectedCustomer && $checkFirstOrderCustomer) {
            $commissionType = $checkFirstOrderCustomer['commission_type'];
            $commissionAmount = $checkFirstOrderCustomer['commission_amount'];
            $referralRule = Referral::FOR_NEW_CUSTOMERS;
        }

        $shopIgnoreGiftCard = array_map('strtolower', config('myconfig.customize.gift_card_commission_zero'));
        $amountGiftCard = 0;
        $isGiftCardOnly = false;
        if ($this->checkGiftCard($input)) {
            $isGiftCardOnly = true;
            $amountGiftCard = $this->orderReferral->getGiftCardAmount($input, $shop);
            if (!in_array(strtolower($shop->shop), $shopIgnoreGiftCard)) {
                $subtotal -= $amountGiftCard;
            }
        }
        $commission = $this->calCommission($commissionType, $commissionAmount, $totalItem, $subtotal, $input, $affiliate->program_id, $program, $input['taxes_included'], $shop);
        if ($this->checkGiftCard($input) && in_array(strtolower($shop->shop), $shopIgnoreGiftCard) || ($subtotal <= 0 && $commissionType == Program::PERCENT_OF_SALE)) {
            $commission = 0;
        }
        $input['line_items'] = $this->lineItems;

        $status = 0;
        if (helperPlan()->planProfessionalUp($shop)) {
            if ($isActive && $shopSetting->auto_approve_order && ($shopSetting->delay_auto_approve_order == 0)) {
                $status = 1;
            }
        } else {
            $status = $isActive ? $shopSetting->auto_approve_order : 0;
        }

        try {
            $referral = new Referral;
            $orderDetail = $orderReferral->convertInputDataOrder($input, $shopInfoCurrency);
            $orderDetail = json_encode($orderDetail);
            $input['total_tax'] = $totalTaxOrder;
            $itemsDetail = getItemsDetailTest($input, $isGiftCardOnly, $amountGiftCard);

            $dataReferral = [
                'shop_id' => $shopId,
                'order_id' => $input['id'],
                'order_number' => $input['order_number'],
                'affiliate_id' => $affiliate->id,
                'customer_id' => isset($input['customer']) ? $input['customer']['id'] : null,
                'quantity' => $totalItem,
                'total' => ($subtotal < 0) ? 0 : $subtotal,
                'commission' => $commission,
                'status' => $status,
                'is_active' => $isActive,
                'program_id' => $affiliate->program_id,
                'commission_type' => $commissionType,
                'commission_amount' => $commissionAmount,
                'tracking_type' => $trackingType,
                'exclude_shipping' => $program->exclude_shipping,
                'exclude_tax' => $program->exclude_tax,
                'include_eu_vat' => $program->include_eu_vat,
                'sca_source' => $source,
                'is_first_commission' => $isFirstCommission,
                'referral_rule' => $referralRule,
                'exclude_product_collection' => (helperPlan()->planProfessionalUp($shop) && $program->exclude_product_collection_status) ? $program->exclude_product_collection_type : Program::EXCLUDE_PRODUCT_COLLECTION_DEACTIVATE,
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
                'created_at_year' => $currentTime->year,
                'created_at_month' => $currentTime->month,
                'is_flag' => ($detectRefer == 3) ? 1 : 0,
                'currency' => $shopInfoCurrency,
            ];

            $referral->fill($dataReferral);

            // Check duplicate referral
            $trackingTypes = [
                Referral::TRACKING_TYPE_CONNECT_CUSTOMER,
                Referral::TRACKING_TYPE_CONNECT_PRODUCT,
                Referral::TRACKING_TYPE_COUPON,
                Referral::TRACKING_TYPE_LINK,
                Referral::TRACKING_TYPE_REFER_CUSTOMER
            ];
            if (in_array($trackingType, $trackingTypes)) {
                // Random timeout for duplicate referral
                usleep(rand(0, 1000000));
                // Check duplicate
                $isDuplicated = Referral::where('order_id', $input['id'])
                    ->where('shop_id', $shopId)
                    ->where('affiliate_id', $affiliate->id)
                    ->where('tracking_type', $trackingType)
                    ->where('level', 0)
                    ->first();
                if ($isDuplicated) {
                    $this->isDuplicate = true;
                    return null;
                }
            }
            // Save referral
            $referral->save();
            $this->orderReferral->createDetailReferral($shopId, $referral->id, $itemsDetail, $orderDetail);

            /** @var SellingPromoteProductService $sellingPromoteProductService */
            $sellingPromoteProductService = app(SellingPromoteProductService::class);
            $sellingPromoteProductService->updateProductSales($shopSetting->shop_id, $input, true);
        } catch (Exception $e) {
            logger('Save referral error: ' . $e->getMessage());
        }

        if (isset($referral)) {
            dispatch(new ReferralCreateListenerJob($referral, $affiliate, $input));
            return $referral;
        }

        return null;
    }

    private function customerIO($affiliate)
    {
        $merchant = User::where('shop_id', $affiliate->shop_id)->first();

        if ($merchant) {
            $hasFirstOrder = shopSetting($affiliate->shop_id, 'aff_first_order');

            if (!$hasFirstOrder) {
                $testAffiliateAccount = Affiliate::select('id')
                    ->where('email', $merchant->email)
                    ->where('shop_id', $affiliate->shop_id)
                    ->first();

                if ($testAffiliateAccount) {
                    $hasReferral = Referral::where('shop_id', $affiliate->shop_id)
                        ->where('affiliate_id', '<>', $testAffiliateAccount->id)
                        ->first();
                } else {
                    $hasReferral = Referral::where('shop_id', $affiliate->shop_id)->first();
                }

                if ($hasReferral) {
                    $shopInfo = DB::table('shop_infos')->where('shop_id', $affiliate->shop_id)->first();
                    $customerIO = new CustomerIO();
                    $customer = [
                        'id' => $shopInfo->shopify_id,
                        'email' => $shopInfo->email,
                        'fullname' => $shopInfo->shop_owner,
                        'aff_first_order' => 1,
                        'country_code' => $shopInfo->country_code
                    ];
                    $customerIO->updateCustomer($customer);
                    shopSetting($affiliate->shop_id, ['aff_first_order' => 1]);
                }
            }
        }
    }

    public function paidByRecharge(Request $request)
    {
        $inputs = $request->all();
        $order = $inputs['order'];
        $shop = $inputs['shop'];
        $cartToken = $inputs['shopify_cart_token'];
        $shop = Shop::where('shop', $shop)->first();
        $shopId = $shop->id;
        $shopSetting = MerchantSetting::where('shop_id', $shopId)->first();

        if ($order['type'] == "CHECKOUT") {
            $orderTracking = OrderTracking::where('cart_token', $cartToken)->whereNotNull('cart_token')->first();
            if ($orderTracking && $shopSetting) {
                $affiliate = Affiliate::where('id', $orderTracking->affiliate_id)->where('status', 1)->with('program')->first();
                if ($affiliate) {
                    $ref = Referral::select('id')->where('order_id', $inputs['id'])->first();
                    if (!$ref) {
                        $newRef = $this->storeReferral($affiliate, $order, $shopSetting, Referral::TRACKING_TYPE_APP_RECURRING);
                        DB::table('order_trackings')->where('id', $orderTracking->id)->update(['expired_at' => Carbon::now()]);
                    }
                }
            }
        }
    }

    public function orderProcessedByRecharge(Request $request)
    {
        $inputs = $request->all();
        DB::table('recharge_log')->insert(['type' => 'processed', 'data' => json_encode($inputs)]);
    }

    public function subscriptionCreated(Request $request)
    {
        $inputs = $request->all();
        DB::table('recharge_log')->insert(['type' => 'subscriptionCreated', 'data' => json_encode($inputs)]);
    }

    public function subscriptionUpdated(Request $request)
    {
        $inputs = $request->all();
        DB::table('recharge_log')->insert(['type' => 'subscriptionUpdated', 'data' => json_encode($inputs)]);
    }

    public function subscriptionActivated(Request $request)
    {
        $inputs = $request->all();
        DB::table('recharge_log')->insert(['type' => 'subscriptionActivated', 'data' => json_encode($inputs)]);
    }

    public function subscriptionCancelled(Request $request)
    {
        $inputs = $request->all();
        DB::table('recharge_log')->insert(['type' => 'subscriptionCancelled', 'data' => json_encode($inputs)]);
    }

    public function subscriptionDeleted(Request $request)
    {
        $inputs = $request->all();
        DB::table('recharge_log')->insert(['type' => 'subscriptionDeleted', 'data' => json_encode($inputs)]);
    }

    private function assignDownLineForCustomer($customerEmail, $shopId, $parentId, $orderCreatedAt)
    {
        $orderCreatedAt = strtotime($orderCreatedAt);
        $orderDate = date('Y-m-d', $orderCreatedAt);
        $assignPending = AssignDownLinePending::where([
            'shop_id' => $shopId,
            'is_assigned' => 0,
        ])->whereIn('customer_email', [
            strtolower($customerEmail),
            EncryptableBaseModel::encrypt(strtolower($customerEmail))
        ])->whereRaw('DATE(created_at) = "' . $orderDate . '"')->first();

        if ($assignPending) {
            $childAffiliate = Affiliate::where(['id' => $assignPending->affiliate_id, 'shop_id' => $shopId])->first();
            if ($childAffiliate) {
                $childAffiliate->parent_id = $parentId;
                $childAffiliate->save();
                $assignPending->is_assigned = 1;
                $assignPending->save();

                dispatchSyncDataSignal(
                    SynchronousModelType::AFFILIATE_RELATED,
                    SynchronousModelType::AFFILIATE_RELATED_ACTION['ON_SET']
                )
                    ->setArguments($childAffiliate->id, $parentId, 'set')
                    ->setIdentity($childAffiliate->shop_id)
                    ->dispatch();
            }
        }
    }

    /**
     * Customize for shop nuchido-uk - 34401
     * Ignore tax for customer not in UK
     *
     * @param $shopId
     * @param $customer
     * @return bool
     */
    public function isNuchidoNonUk($shopId, $customer)
    {
        try {
            if ($shopId != 34401) return false;

            if (!empty($customer['shipping_address']['country'])) {
                $country = $customer['shipping_address']['country'];
            } else if (!empty($customer['default_address']['country'])) {
                $country = $customer['default_address']['country'];
            } else {
                $country = 'United Kingdom';
            }

            if ($country != 'United Kingdom') {
                return true;
            }
        } catch (Exception $exception) {
            logger('Error nuchido-uk: ' . json_encode($customer));
        }

        return false;
    }

    /**
     * @param $input
     * @param $browserFingerprints
     * @return mixed
     */
    public function getOrderTrackingSubscription($input, $browserFingerprints)
    {
        $cartToken = null;
        if (!empty($input['note_attributes'])) {
            foreach ($input['note_attributes'] as $note) {
                if ($note['name'] == 'cart_token' || $note['name'] == '_cart_token') {
                    $cartToken = trim($note['value']);
                }
            }
        }

        $orderTracking = OrderTracking::where('browser_fingerprints', $browserFingerprints)
            ->whereNotNull('browser_fingerprints');

        if ($cartToken) {
            $orderTracking = $orderTracking->orwhere(function ($query) use ($cartToken) {
                $query->where('cart_token', $cartToken);
                $query->whereNotNull('cart_token');
            });
        }

        return $orderTracking->orderBy('id', 'desc')->first();
    }
}

<?php

namespace App\Http\Controllers\Merchant;

use App\Constant\Feature;
use App\Constant\Tracking\AffiliateCoupon as AffiliateCouponCaching;
use App\Constant\Tracking\ClickTracking;
use App\Constant\Tracking\FacebookPixel;
use App\Events\Merchant\DeleteAffiliate;
use App\Http\Requests\Affiliate\SaveCustomLinkRequest;
use App\Jobs\MixpanelAnalytics\MixpanelAnalyticsJob;
use App\Jobs\ReSyncAffiliateMarketplaceRankJob;
use App\Jobs\SyncMailchimpJob;
use App\Jobs\SyncOmnisendJob;
use App\Models\BlockEmails;
use App\Models\BlockRegions;
use App\Models\ConnectCustomer;
use App\Models\EmailTemplate;
use App\Models\FraudProtectSettings;
use App\Models\Program;
use App\Models\ProgramsSetting;
use App\Models\Setting\SettingModel;
use App\Models\ShopifyCustomerConvert;
use App\Models\PurchasePopup;
use App\Models\StatisticalSpams;
use App\Models\User;
use App\Repositories\ProgramRepository\ProgramRepository;
use App\Rules\ValidFirstName;
use App\Rules\ValidInternationalEmail;
use App\Rules\ValidLastName;
use App\Services\AffiliateService\AffiliateService;
use App\Services\CustomBlockLinkService\CustomBlockLinkService;
use App\Services\KlaviyoApi;
use App\Services\KlaviyoApiNew;
use App\Services\MerchantSendMail;
use App\Services\MultipleShopService\MultipleShopService;
use App\Services\ProgramService\ProgramService;
use App\Services\ReferralService\ReferralService;
use App\Shopify\Resources\DiscountCode;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use App\Models\Affiliate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;
use App\Events\Merchant\ActiveAffiliate;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Secomapp\Resources\PriceRule;
use Yajra\DataTables\Facades\DataTables;
use App\Models\MerchantSetting;
use App\Models\AffiliateSetting;
use App\Models\AffiliateCoupon;
use App\Events\AffiliateRegisteredByPurchase;
use App\Events\Merchant\AffiliateImported;
use Secomapp\Models\ShopInfo;
use App\Models\ProgramMlm;
use App\Models\Referral;
use App\Imports\AffiliateImport;
use Secomapp\Models\Shop;
use Carbon\Carbon;
use Rap2hpoutre\FastExcel\FastExcel;
use App\Models\Payment;
use Secomapp\Contracts\ClientApiContract;
use App\Jobs\SendAffiliatesQueue;
use App\Models\Chat;
use Secomapp\Traits\InstalledShop;
use Throwable;

class AffiliateController extends Controller
{
    use InstalledShop;

    private $clientApi;
    private $affiliateService;
    private $customBlockLinkService;
    private $referralService;

    /**
     * @param ClientApiContract $clientApi
     * @param AffiliateService $affiliateService
     * @param CustomBlockLinkService $customBlockLinkService
     * @param ReferralService $referralService
     */
    public function __construct(
        ClientApiContract      $clientApi,
        AffiliateService       $affiliateService,
        CustomBlockLinkService $customBlockLinkService,
        ReferralService        $referralService
    )
    {
        $this->middleware(['auth', 'can:affiliate and coupon'])->except([
            'createAffiliateByPurchasePopup',
            'getCouponApi', 'getFacebookPixel', 'loginAsBySmall', 'search',
            'bulkMoveAffiliateToProgram'
        ]);
        $this->clientApi = $clientApi;
        $this->affiliateService = $affiliateService;
        $this->customBlockLinkService = $customBlockLinkService;
        $this->referralService = $referralService;
    }

    public function index(Request $request)
    {
        $user = Auth::user();
        $shopId = $user->shop_id;
        $countAffiliate = Affiliate::where('shop_id', $user->shop_id)->count();
        $settings = MerchantSetting::where('shop_id', $user->shop_id)->first();
        $shopInfo = ShopInfo::where('shop_id', $user->shop_id)->select('plan_name')->first();
        $canUseConnectCustomer = $user->can('connect-customer');
        $canUseConnectProduct = $user->can('connect-product');
        $checkScopeCustomer = shopSetting($user->shop_id, 'convert_shopify_customer') ? 1 : 0;
        $programs = Program::where('shop_id', $user->shop_id)->select('id', 'name', 'is_default')->get();

        $programWithoutCustomerReferralProgram = clone $programs;

        /** @var ProgramService $programService */
        $programService = app(ProgramService::class);
        $customerReferralProgram = $programService->programRepository->getReferCustomerProgram($shopId);
        if ($customerReferralProgram) {
            $programs->push($customerReferralProgram);
        }
        // Check merchant uses convert customer feature or not
        $checkUseConvertCustomer = false;
        $purchasePopup = PurchasePopup::where('shop_id', $user->shop_id)
            ->where('status', 1)
            ->first();
        if ($purchasePopup) {
            $checkUseConvertCustomer = true;
        }

        $shopifyCustomerConvert = ShopifyCustomerConvert::where('shop_id', $user->shop_id)
            ->where('status', 1)
            ->first();
        if ($shopifyCustomerConvert) {
            $checkUseConvertCustomer = true;
        }

        $remindPaymentEmail = EmailTemplate::where('shop_id', $user->shop_id)
            ->where('slug', 'enter_payment_detail_alert')
            ->first();

        if ($request->has('active')) {
            $active = $request->active;
        } else {
            $active = '';
        }
        // Test store id: 23941 - original-lemonhaze.myshopify.com
        $customizeShopIds = [41249, 43923, 44232, 44233, 44234, 43368, 44066, 46574];

        // custom colums affiliate
        $affiliateData = getCustomColumnsData('affiliate_index', $user->shop_id, 'affiliate_datatable', 'affiliate_index', 'affiliate');
        $customData = [
            'affiliate_index' => $affiliateData
        ];

        return view(in_array($user->shop_id, $customizeShopIds) ?
            "app.pages.customize.affiliate.index.$user->shop_id" :
            'app.pages.affiliate.index', [
            'merchant' => $user,
            'settings' => $settings,
            'shopInfo' => $shopInfo,
            'countAffiliate' => $countAffiliate,
            'canUseConnectCustomer' => $canUseConnectCustomer,
            'canUseConnectProduct' => $canUseConnectProduct,
            'checkScopeCustomer' => $checkScopeCustomer,
            'programs' => $programs,
            'checkUseConvertCustomer' => $checkUseConvertCustomer,
            'remindPaymentEmail' => $remindPaymentEmail,
            'active' => $active,
            'customData' => $customData,
            'programWithoutCustomerReferralProgram' => $programWithoutCustomerReferralProgram
        ]);
    }

    public function getDatatables(Request $request)
    {
        $input = $request->all();
        $shopId = Auth::user()->shop_id;

        if (!empty($input['from'])) {
            $from = Carbon::createFromTimestamp(intval($input['from']))->toDateTimeString();
            $to = Carbon::createFromTimestamp(intval($input['to']))->toDateTimeString();
        }
        $settings = MerchantSetting::where('shop_id', $shopId)->select('show_unverified_aff_email')->first();
        $condition = [['shop_id', '=', $shopId]];
        if (isset($input['status'])) {
            if ($input['status'] >= 0) {
                if ($input['status'] == 2) {
                    $condition[] = ['is_pending', '=', 1];
                } else {
                    $condition[] = ['status', '=', $input['status']];
                }
            }
        }
        if ($request->get('program')) {
            $condition[] = ['program_id', $request->get('program')];
        }
        if (!$settings->show_unverified_aff_email) {
            $condition[] = ['verified', 1];
        }
        $aff = Affiliate::where($condition);
        if (isset($from) && isset($to)) {
            $aff = $aff->whereBetween('created_at', [$from, $to]);
        }

        $select = [
            'id',
            'first_name',
            'last_name',
            'email',
            'coupon',
            'status',
            'created_at',
            'program_id',
            'is_pending',
            'register_method',
            'company',
            'address',
            'city',
            'state',
            'country',
            'zipcode',
            'phone',
            'personal_detail',
            'website',
            'vat_number',
            'login_count',
            'updated_at',
            'marketplace_user_id',
            'marketplace_affiliate_profile',
            'last_login',
            'id as merchant_can_rate',
            'id as full_name',
            'flag_fraud_protection',
            'instagram'
        ];
        if ($shopId == 41249 || $shopId == 44066) {
            $select[] = 'personal_detail';
        }

        $customizeCountryShopIds = [43923, 44232, 44233, 44234, 46574];
        if (in_array($shopId, $customizeCountryShopIds)) {
            $select[] = 'country';
        }
        $aff->select($select)->with(['program' => function ($query) {
            $query->withTrashed();
        }]);

        return DataTables::of($aff)->addColumn('merchant_rated', function ($affElement) use ($shopId) {
            return $affElement->isMerchantRated($shopId);
        })->make();
    }

    /**
     * Create affiliate manually
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function create(Request $request)
    {
        $merchant = Auth::user();
        $data = $request->only([
            'send_notification',
            'program',
            'first_name',
            'last_name',
            'email',
            'password_type',
            'password'
        ]);

        // Make validation
        $rules = [
            'program' => ['required'],
            'first_name' => ['required', 'string', 'max:15', new ValidFirstName],
            'last_name' => ['required', 'string', 'max:30', new ValidLastName],
            'email' => [
                'required',
                'string',
                new ValidInternationalEmail(),
                'max:255',
                function ($attribute, $value, $fail) use ($merchant) {
                    $checkAffiliateExist = DB::table('affiliates')->where([
                        'shop_id' => $merchant->shop_id,
                        'email' => trim($value)
                    ])->get();

                    if ($checkAffiliateExist->count() != 0) {
                        $fail('The email has already been taken.');
                    }
                }
            ],
        ];

        // Check set default password or not
        if ($data['password_type']) {
            $rules['password'] = 'required|string|min:6|max:50';
        }

        // Process validation
        $validator = Validator::make($data, $rules);
        if ($validator->fails()) {
            // Return message if fail
            return response()->json([
                'status' => 'error',
                'message' => $validator->errors()
            ]);
        }

        // Check set default password or not
        if ($data['password_type']) {
            $password = $data['password'];
        } else {
            $password = generateRandomString();
        }

        $totalAffiliate = Affiliate::where('shop_id', $merchant->shop_id)
            ->where('register_method', Affiliate::REG_BY_MANUAL)
            ->whereDate('created_at', Carbon::today())
            ->count();
        if ($totalAffiliate > 50) {
            return response()->json([
                'status' => 'ok',
                'message' => 'You can create only 50 affiliates per day'
            ]);
        }

        // Create affiliate
        $affiliate = Affiliate::firstOrNew([
            'email' => trim($data['email']),
            'shop_id' => $merchant->shop_id
        ], [
            'password' => Hash::make($password),
            'first_name' => trim($data['first_name']),
            'last_name' => trim($data['last_name']),
            'hash_code' => generateRandomString(),
            'program_id' => $data['program'],
            'shop_id' => $merchant->shop_id,
            'status' => 1,
            'register_method' => Affiliate::REG_BY_MANUAL
        ]);

        if (!$affiliate->id) {
            $affiliate->save();
            $affiliate->temp_password = $password;
            $affiliate->notification = $data['send_notification'];
            event(new AffiliateImported($affiliate));
        }

        try {
            $this->syncKlaviyo($affiliate, $merchant);
        } catch (Exception $e) {
            info($e->getMessage());
        }

        if ($merchant->setting->klaviyo) {
            if ($merchant->setting->klaviyo['status']) {
                $apiKey = $merchant->setting->klaviyo['api_key'];
                $klaviyoApi = new KlaviyoApiNew($apiKey);
                $shopInfo = DB::table('shop_infos')->where('shop_id', $affiliate->shop_id)->select('money_format')->first();
                if ($merchant->setting->custom_domain) {
                    $aff_login_url = 'https://' . $merchant->setting->custom_domain . '/login';
                } else {
                    $aff_login_url = env('APP_URL') . '/' . $merchant->subdomain . '/login';
                }
                $program = Program::find($affiliate->program_id);
                if ($program->commission_type == 0) {
                    $commissionStructure = 'flat rate per order';
                } else if ($program->commission_type == 1) {
                    $commissionStructure = 'flat rate per item';
                } else {
                    $commissionStructure = 'percent of sale';
                }
                if ($program->commission_type != 2) {
                    $commission_amount = currnency_format(strip_tags($shopInfo->money_format), $program->commission_amount);
                } else {
                    $commission_amount = ($program->commission_amount) . '%';
                }
                $affiliateCoupon = AffiliateCoupon::where('affiliate_id', $affiliate->id)->where('shop_id', $affiliate->shop_id)->get();
                $discountCode = '';
                if ($affiliateCoupon) {
                    foreach ($affiliateCoupon as $c) {
                        $coupons[] = $c->coupon;
                    }
                    if (!empty($coupons)) {
                        $discountCode = implode(", ", $coupons);
                    }
                }

                $data = [
                    "data" => [
                        "type" => "event",
                        "attributes" => [
                            "profile" => [
                                "data" => [
                                    "type" => "profile",
                                    "attributes" => [
                                        "email" => "$affiliate->email",
                                        "first_name" => $affiliate->first_name,
                                        "last_name" => $affiliate->last_name,
                                        "properties" => [
                                            "Name - UpPromote" => $affiliate->first_name . ' ' . $affiliate->last_name,
                                            "Referral Code - UpPromote" => $affiliate->id . '.' . $affiliate->hash_code,
                                            "Program - UpPromote" => $program->name,
                                            "Sign Up Source - UpPromote" => $affiliate->registerMethodText($affiliate->register_method),
                                            "Affiliate Link - UpPromote" => genReferralLink($affiliate, $merchant->setting->referral_link),
                                            "Affiliate Login Link - UpPromote" => $aff_login_url,
                                            "Commission Structure - UpPromote" => $commissionStructure,
                                            "Commission Amount - UpPromote" => $commission_amount,
                                            "First Name - UpPromote" => $affiliate->first_name,
                                            "Last Name - UpPromote" => $affiliate->last_name,
                                            "Coupon - Uppromote" => $discountCode
                                        ]
                                    ]
                                ]
                            ],
                            "metric" => [
                                "data" => [
                                    "type" => "metric",
                                    "attributes" => [
                                        "name" => "UpPromote - Added Affiliate - " . $merchant->setting->brand_name
                                    ]
                                ]
                            ],
                            "properties" => [
                                "first_name" => $affiliate->first_name,
                                "last_name" => $affiliate->last_name,
                                "affiliate_link" => genReferralLink($affiliate, $merchant->setting->referral_link),
                                "affiliate_login_url" => $aff_login_url,
                                "commission_structure" => $commissionStructure,
                                "commission_amount" => $commission_amount,
                                "temporary_password" => $affiliate->temp_password,
                                "coupon" => $discountCode
                            ]
                        ]
                    ]
                ];
                $klaviyoApi->sendApiKlaviyo($data);
            }
        }
        if (helperPlan()->planProfessionalUp()) {
            $shopId = $merchant->shop_id;
            $data = SettingModel::getApiMailchimp($shopId);
            $listId = $data['listId'] ?? null;
            $apikey = $data['apikey'] ?? null;
            $shop = Shop::find($shopId);
            $apiOmnisend = json_decode($shop->api_omnisend, true);
            $apiMailchimp = json_decode($shop->api_mailchimp, true);
            if ($apiMailchimp && $apiMailchimp['status'] == 1) {
                dispatch((new SyncMailchimpJob(collect([$affiliate]), $listId, $apikey))->onQueue('integration-mailchimp')); // queue job
            }
            if ($apiOmnisend && $apiOmnisend['status'] == 1) {
                $apikeyOmni = $apiOmnisend['apikey'] ?? null;
                dispatch((new SyncOmnisendJob(collect([$affiliate]), $apikeyOmni))->onQueue('integration-omnisend')); // queue job
            }
        }
        activity('affiliate')
            ->causedBy($merchant)
            ->performedOn($affiliate)
            ->log('added affiliate <b>' . $affiliate->first_name . ' ' . $affiliate->last_name . '</b> manually');

        /******** Start Mixpanel ********/
        try {
            $shop = Shop::findOrFail($affiliate->shop_id);
            $shopInfo = DB::table('shop_infos')->where('shop_id', $affiliate->shop_id)->first();
            $inputs = [
                'shop' => $shop,
                'shop_infos' => $shopInfo,
                'settings' => $merchant->setting,
                'users' => $merchant
            ];

            dispatch(new MixpanelAnalyticsJob('user-profile', $affiliate->shop_id, $inputs, null, session()->has('sudo')));
        } catch (Exception $exception) {
            logger($exception->getMessage() . ' - ' . $exception->getMessage());
        }
        /******** End Mixpanel ********/

        return response()->json([
            'status' => 'ok',
            'message' => 'Created new affiliate'
        ]);
    }

    protected function syncKlaviyo($affiliate, $merchant)
    {
        if ($merchant->setting->klaviyo && helperPlan()->planProfessionalUp()) {
            if ($merchant->setting->klaviyo['status']) {
                $apiKey = $merchant->setting->klaviyo['api_key'];
                $listId = $merchant->setting->klaviyo['list_id'];
                $klaviyoApi = new KlaviyoApi($apiKey);
                if ($affiliate->status) {
                    $idSearch = $klaviyoApi->searchProfile($affiliate->email);
                    if ($idSearch) {
                        $klaviyoApi->addMembersFromIdSearch($listId, $idSearch);
                    }
                } else {
                    if ($merchant->setting->sync_klaviyo_approved_affiliate) {
                        $idSearch = $klaviyoApi->searchProfile($affiliate->email);
                        if ($idSearch) {
                            $klaviyoApi->deleteMembers($listId, $idSearch);

                        }
                    }
                }
            }
        }
    }

    private function checkExistAndGetRandomHashcode()
    {
        while (1) {
            $hashcode = generateRandomString();
            $check = Affiliate::where('hash_code', $hashcode)->first();
            if (!$check) {
                break;
            }
        }
        return $hashcode;
    }

    public function edit($id)
    {
        $user = Auth::user();
        $programs = Program::where('shop_id', $user->shop_id)->get();
        $affiliate = Affiliate::findOrFail($id);
        if ($affiliate->register_method == Affiliate::REG_BY_CUSTOMER_REFERRAL) {
            abort(404);
        }
        $affiliate = $affiliate->where('id', $id)->with(['setting', 'parent', 'program', 'coupons'])->first();

        $affiliate->registerMethodToText();
        $this->authorize('show-affiliate', $affiliate);
        $commissions = DB::table('referrals')->where('affiliate_id', $id)
            ->select(DB::Raw('sum(case when status=0 then commission else 0 end) as commission_pending'),
                DB::Raw('sum(case when status=1 then commission else 0 end) as commission_approve'))
            ->first();
        $merchantSettings = MerchantSetting::where('shop_id', $user->shop_id)->first();
        $referralLink = genReferralLink($affiliate, $merchantSettings->referral_link);
        $defaultAffiliateLink = parse_url($merchantSettings->referral_link);
        $defaultAffiliateLink = optional($defaultAffiliateLink)['scheme'] . '://' . optional($defaultAffiliateLink)['host'] . '/';
        $commissionPaid = Payment::where('affiliate_id', $id)->where('status', 1)->sum('amount');
        //get network data
        $multiLevel = ProgramMlm::where('shop_id', $affiliate->shop_id)->where('program_id', $affiliate->program_id)->first();
        $levels = [];
        $dataLevel = [];
        $earnings = 0;
        $totalSignups = 0;
        if ($multiLevel) {
            for ($i = 1; $i <= $multiLevel->number_level; $i++) {
                $levels[] = $i;
            }
            $infos = Referral::where('affiliate_id', $affiliate->id)
                ->whereIn('level', $levels)
                ->select('level', DB::raw('SUM(total) as total_revenue'), DB::raw('COUNT(*) as sales'), DB::raw('SUM(commission) as total_commission'))
                ->whereNotIn('status', [3])
                ->groupBy('level')
                ->orderBy('level')
                ->get();
            $ids = [$affiliate->id];

            for ($i = 0; $i < $multiLevel->number_level; $i++) {
                $name = 'Level ' . ($i + 1);
                $ids = Affiliate::whereIn('parent_id', $ids)->pluck('id')->toArray();
                $dataLevel[] = ['name' => $name, 'number_affiliate' => count($ids), 'infos' => $this->searchLevel($infos, $i + 1)];
            }
            if (!helperPlan()->planProfessionalUp())
                $multiLevel->is_enable = 0;
        }
        foreach ($dataLevel as $v) {
            if ($v['infos']) {
                $earnings = $earnings + $v['infos']->total_commission;
            }
            $totalSignups = $totalSignups + $v['number_affiliate'];
        }
        $canUseCustomReferralLink = $user->can('custom-referral-link');
        $shopInfos = DB::table('shop_infos')->where('shop_id', $user->shop_id)
            ->select(['domain', 'myshopify_domain', 'plan_name'])
            ->first();
        $customAffiliateLink = $affiliate->custom_affiliate_link ?
            $this->affiliateService->generateCustomLinkPath($affiliate->custom_affiliate_link) : '';
        if ($user->shop_id == 12737) {
            return view('app.pages.affiliate.custom.12737_edit', [
                'aff' => $affiliate,
                'programs' => $programs,
                'commissions' => $commissions,
                'commissionPaid' => $commissionPaid,
                'paymentText' => config('myconfig.app.payment_text'),
                'merchantSettings' => $merchantSettings,
                'referralLink' => $referralLink,
                'earnings' => $earnings,
                'totalSignups' => $totalSignups,
                'dataLevel' => $dataLevel,
                'domain' => $user->subdomain,
                'multiLevel' => $multiLevel,
                'canUseCustomReferralLink' => $canUseCustomReferralLink,
                'shopInfos' => $shopInfos,
                'activeCommissionTab' => (isset($_GET['tab']) && $_GET['tab'] == 'commission') ? 1 : 0
            ]);
        }

        $customBlockLink = null;
        $standardLink = null;
        $customLink = null;
        if ($user->shop_id == 34620) {
            $blockLink = $this->customBlockLinkService->getCustomBlockLink($user->shop_id, $affiliate->id);
            if ($blockLink) {
                $customBlockLink = $blockLink->link;
                $standardLink = $blockLink->standard_link;
                $customLink = $blockLink->custom_link;
            }
        }

        $customizeShopId = [34620];

        $utmUrl = genUtmSource($merchantSettings, $affiliate, $affiliate->program->name);
        $referralLink .= $utmUrl;

        $signupFieldsArr = [];
        try {
            $signupFieldsArr = $affiliate->program->setting->signup_fields_arr;
            $signupFieldsArr = array_reduce($signupFieldsArr, function ($pre, $cur) {
                $pre[$cur->id] = $cur;
                return $pre;
            }, []);
        } catch (Exception $e) {

        }

        return view(in_array($user->shop_id, $customizeShopId) ?
            "app.pages.customize.affiliate.edit.$user->shop_id" :
            'app.pages.affiliate.edit', [
            'aff' => $affiliate,
            'programs' => $programs,
            'commissions' => $commissions,
            'commissionPaid' => $commissionPaid,
            'paymentText' => config('myconfig.app.payment_text'),
            'merchantSettings' => $merchantSettings,
            'referralLink' => $referralLink,
            'earnings' => $earnings,
            'totalSignups' => $totalSignups,
            'dataLevel' => $dataLevel,
            'domain' => $user->subdomain,
            'multiLevel' => $multiLevel,
            'canUseCustomReferralLink' => $canUseCustomReferralLink,
            'shopInfos' => $shopInfos,
            'activeCommissionTab' => (isset($_GET['tab']) && $_GET['tab'] == 'commission') ? 1 : 0,
            'customAffiliateLink' => $customAffiliateLink,
            'customBlockLink' => $customBlockLink,
            'standardLink' => $standardLink,
            'customLink' => $customLink,
            'defaultAffiliateLink' => $defaultAffiliateLink,
            'signupFieldsArr' => $signupFieldsArr
        ]);
    }

    public function getDetailDatatable($id, $level)
    {
        $affiliate = Affiliate::find($id);
        $ids = [$affiliate->id];

        for ($i = 1; $i <= $level; $i++) {
            if ($i == $level) {
                $data = Affiliate::whereIn('parent_id', $ids)
                    ->leftJoin('referrals', function ($join) use ($level, $id) {
                        $join->on('affiliates.id', '=', 'referrals.child_affiliate')
                            ->where('referrals.affiliate_id', $id)
                            ->where('referrals.level', $level)
                            ->whereNotIn('referrals.status', [3]);
                    })
                    ->select('affiliates.id', 'affiliates.first_name', 'affiliates.last_name', DB::raw('SUM(referrals.total) as total_revenue'), DB::raw('SUM(referrals.commission) as total_commission'), DB::raw('COUNT(referrals.id) as sales'))
                    ->groupBy(['affiliates.id', 'affiliates.first_name', 'affiliates.last_name']);


            } else {
                $ids = Affiliate::whereIn('parent_id', $ids)->pluck('id')->toArray();
            }
        }

        return DataTables::of($data)->make();
    }

    /**
     * @throws AuthorizationException
     */
    public function show($id): JsonResponse
    {
        $user = Auth::user();
        $aff = Affiliate::where('id', $id)->with('setting')->first();
        $aff->registerMethodToText();
        $this->authorize('show-affiliate', $aff);
        $coupons = AffiliateCoupon::select('coupon')
            ->where('shop_id', $user->shop_id)
            ->where('affiliate_id', $id)
            ->take(10)
            ->get();

        $merchantSetting = MerchantSetting::where('shop_id', $user->shop_id)->select('referral_link', 'utm_settings', 'utm_parameters')->first();
        $program = Program::select('id', 'name')->where('id', $aff->program_id);
        if ($aff->register_method == 'Customer referral') {
            $program = $program->whereNotNull('refer_customer_incentive')
                ->whereNotNull('deleted_at')
                ->onlyTrashed();
        }
        $program = $program->first();
        $utmUrl = genUtmSource($merchantSetting, $aff, $program->name);

        $referralLink = genReferralLink($aff, $merchantSetting->referral_link);
        $isConvertedCustomer = DB::table('registered_purchase_popup')->where('affiliate_id', $aff->id);
        $canUseCustomReferralLink = $user->can('custom-referral-link');
        if ($aff->custom_affiliate_link && helperPlan()->planProfessionalUp()) {
            $referralLink = "https://{$user->shop->info->domain}/{$this->affiliateService->generateCustomLinkPath($aff->custom_affiliate_link)}";
        }
        $referralLink .= $utmUrl;
        $referralLink = ($aff->register_method == 'Customer referral') ? genReferralLink($aff, $merchantSetting->referral_link) . '&sca_crp=' . base64_encode($program->id) : $referralLink;

        $contentEmail = EmailTemplate::where('program_id', $aff->program_id)
            ->where('slug', 'enter_payment_detail_alert')
            ->where('shop_id', $user->shop_id)
            ->first();
        if (!isset($contentEmail)) {
            $contentEmail = EmailTemplate::where('program_id', 0)
                ->where('slug', 'enter_payment_detail_alert')
                ->where('shop_id', $user->shop_id)
                ->first();
        }

        return response()->json([
            'data' => $aff,
            'coupons' => $coupons,
            'referral_link' => $referralLink,
            'is_converted_customer' => $isConvertedCustomer->count(),
            'can_use_custom_referral_link' => $canUseCustomReferralLink,
            'contentEmail' => $contentEmail,
            'status' => 'ok',
            'message' => 'success'
        ]);
    }

    public function update(Request $request, $id)
    {
        $input = $request->only(['status', 'paypal_email', 'program_id']);
        Validator::make($input, [
            'paypal_email' => 'nullable|email',
        ])->validate();
        $aff = Affiliate::find($id);

        $isChangeStatus = $aff->status != (isset($input['status']) ? $input['status'] : false);
        $firstApprove = $aff->is_first_approve_recruitment_bonus;
        $this->authorize('update-affiliate', $aff);
        $aff->is_pending = 0;
        $aff->fill($input);
        $aff->save();

        if ($aff->shop_id == 34620) {
            $this->customBlockLinkService->storeCustomBlockLink(
                $aff->shop_id,
                $aff->id,
                $request->get('custom_block_link') ?? '',
                $request->get('custom_standard_link') ?? '',
                $request->get('custom_custom_link') ?? ''
            );
        }
        $request->session()->flash('saved', 1);

        if ($isChangeStatus) {
            event(new ActiveAffiliate($aff, $firstApprove));
        }

        return redirect()->back()->with('ok', 'Affiliate updated');
    }

    public function active($id, Request $request)
    {
        $aff = Affiliate::find($id);
        $user = Auth::user();
        if ($aff) {
            $firstApprove = $aff->is_first_approve_recruitment_bonus;
            $this->authorize('update-affiliate', $aff);
            if ($request->has('t')) {
                if ($request->get('t') == 'approve') {
                    $aff->status = 1;
                    $aff->is_pending = 0;
                } else {
                    $aff->status = 0;
                    $aff->is_pending = 0;

                    /******** Start Mixpanel Deactivate Aff ********/
                    $this->manualDeActiveAff($aff);
                }
            } else {
                $aff->status = !$aff->status;

            }
            $aff->save();

            if (!$aff->status && $aff->is_pending == 0) $this->manualDeActiveAff($aff);

            event(new ActiveAffiliate($aff, $firstApprove));
        }

        return response()->json([
            'status' => 'ok',
            'message' => 'success'
        ]);
    }

    /**
     * Merchant verify affiliate email manually
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function manualVerify(Request $request)
    {
        $affiliateId = $request->get('id');

        if ($affiliateId) {
            $affiliate = Affiliate::find($affiliateId);

            if ($affiliate) {
                $affiliate->verified = true;
                $affiliate->save();

                //send mail approve affiliate
                $merchant = Auth::user();
                $settings = $merchant->setting;
                if ($affiliate->status == 1) {
                    $type = 'approve_affiliate';
                    $program = Program::find($affiliate->program_id);
                    if ($merchant->setting->custom_domain) {
                        $urlLogin = 'https://' . $merchant->setting->custom_domain . '/login';
                    } else {
                        $urlLogin = route('aff.login', ['domain' => $merchant->subdomain]);
                    }
                    $commissionStructureText = get_commission_type_text($program->commission_type);
                    $commissionAmountText = get_commission_amount_text($program->commission_type, $program->commission_amount, session('money_format'));
                    $affiliateCoupon = AffiliateCoupon::where('affiliate_id', $affiliate->id)->where('shop_id', $affiliate->shop_id)->get();
                    $discountCode = '';
                    if ($affiliateCoupon) {
                        foreach ($affiliateCoupon as $c) {
                            $coupons[] = $c->coupon;
                        }
                        if (!empty($coupons)) {
                            $discountCode = implode(", ", $coupons);
                        }
                    }
                    $referralLink = $settings->referral_link . '?sca_ref=' . $affiliate->id . '.' . $affiliate->hash_code;
                    if ($affiliate->custom_affiliate_link && helperPlan()->planProfessionalUp()) {
                        $referralLink = $settings->referral_link . '/' . $affiliate->custom_affiliate_link;
                    }
                    $replaces = [$affiliate->first_name, $urlLogin, $discountCode, $referralLink, $program->name, $commissionStructureText, $commissionAmountText];
                    if ($settings->klaviyo && helperPlan()->planProfessionalUp()) {
                        if ($settings->klaviyo['status']) {
                            $apiKey = $settings->klaviyo['api_key'];
                            $klaviyoApi = new KlaviyoApiNew($apiKey);

                            $data = [
                                "data" => [
                                    "type" => "event",
                                    "attributes" => [
                                        "profile" => [
                                            "data" => [
                                                "type" => "profile",
                                                "attributes" => [
                                                    "email" => "$affiliate->email"
                                                ]
                                            ]
                                        ],
                                        "metric" => [
                                            "data" => [
                                                "type" => "metric",
                                                "attributes" => [
                                                    "name" => "UpPromote - Approved Affiliate - $settings->brand_name"
                                                ]
                                            ]
                                        ],
                                        "properties" => [
                                            "first_name" => $affiliate->first_name,
                                            "last_name" => $affiliate->last_name,
                                            "affiliate_link" => $referralLink,
                                            "affiliate_login_url" => $urlLogin,
                                            "commission_structure" => $commissionStructureText,
                                            "commission_amount" => $commissionAmountText,
                                            "program_name" => $program->name,
                                            "coupon" => $discountCode
                                        ]
                                    ]
                                ]
                            ];
                            $klaviyoApi->sendApiKlaviyo($data);
                        }
                    }
                    MerchantSendMail::send($merchant, $affiliate, $type, $replaces);
                } else if ($affiliate->status == 0 && $affiliate->is_pending == 0) {
                    if ($settings->klaviyo && helperPlan()->planProfessionalUp()) {
                        if ($settings->klaviyo['status']) {
                            $apiKey = $settings->klaviyo['api_key'];
                            $klaviyoApi = new KlaviyoApiNew($apiKey);
                            $data = [
                                "data" => [
                                    "type" => "event",
                                    "attributes" => [
                                        "profile" => [
                                            "data" => [
                                                "type" => "profile",
                                                "attributes" => [
                                                    "email" => "$affiliate->email"
                                                ]
                                            ]
                                        ],
                                        "metric" => [
                                            "data" => [
                                                "type" => "metric",
                                                "attributes" => [
                                                    "name" => "UpPromote - Denied Affiliate - $settings->brand_name"
                                                ]
                                            ]
                                        ],
                                        "properties" => [
                                            "first_name" => $affiliate->first_name,
                                            "last_name" => $affiliate->last_name
                                        ]
                                    ]
                                ]
                            ];
                            $klaviyoApi->sendApiKlaviyo($data);
                        }
                    }
                    $type = 'denied_affiliate';
                    $replaces = [$affiliate->first_name];
                    MerchantSendMail::send($merchant, $affiliate, $type, $replaces);
                }
                return response()->json([
                    'status' => 'ok'
                ], 200);
            }
        }

        return response()->json([
            'status' => 'error'
        ], 200);
    }

    private function destroyaffiliateMultiShop($affiliate)
    {
        $shopId = $affiliate->shop_id;
        $email = $affiliate->email;
        /** @var MultipleShopService $multipleShopService */
        $multipleShopService = app(MultipleShopService::class);
        $canUseMultipleShop = $multipleShopService->canUseMultipleShop($affiliate->shop_id);
        if ($canUseMultipleShop) {
            $shopRelationship = $multipleShopService->getShopRelationship($affiliate->shop_id, $affiliate->shop_id);
            foreach ($shopRelationship as $s) {
                if ($shopId) {
                    $affiliate = Affiliate::where([
                        'email' => $email,
                        'shop_id' => $s['shop_id']
                    ])->first();
                    if ($affiliate) {
                        // Remove detail referral
                        $referralIds = $affiliate->referrals()->pluck('id', 'id')->all();
                        if (count($referralIds) > 0) {
                            $this->referralService->referralRepository->removeDetailReferral($referralIds, $s['shop_id']);
                        }

                        $affMpUserId = $affiliate->marketplace_user_id ?? null;

                        $affiliate->coupons()->delete();
                        $affiliate->referrals()->delete();
                        $affiliate->clicks()->delete();
                        $affiliate->setting()->delete();
                        $affiliate->rates()->delete();

                        ConnectCustomer::whereAffiliateId($affiliate->id)->whereShopId($affiliate->shop_id)->delete();

                        $affiliate->delete();
                        event(new DeleteAffiliate($affiliate));
                        if ($affMpUserId) dispatch(new ReSyncAffiliateMarketplaceRankJob(['mkpUserId' => $affMpUserId]));
                    }
                }
            }
        }
    }

    /**
     * @param $id
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function destroy($id)
    {
        $aff = Affiliate::find($id);
        if ($aff) {
            $fakerAffiliate = $aff;
            $this->authorize('delete-affiliate', $aff);

            // Remove detail referral
            $referralIds = $aff->referrals()->pluck('id', 'id')->all();
            if (count($referralIds) > 0) {
                $this->referralService->referralRepository->removeDetailReferral($referralIds, $aff->shop_id);
            }

            $affMpUserId = $aff->marketplace_user_id ?? null;

            $aff->coupons()->delete();

            // remove network
            $this->deleteNetWorkCommission($aff->id, $aff->referrals()->pluck('id')->all());

            $aff->referrals()->get()->each(function ($ref){ $ref->delete(); });
            $this->referralService->handleReferralTotal($aff->shop_id); // update total ref
            $aff->clicks()->delete();
            $aff->setting()->delete();
            $aff->rates()->delete();

            // delete chat
            Chat::where(function ($query) use ($aff) {
                $query->where('from_id', '=', $aff->id)
                    ->where('acc_send', '=', 'affiliate');
            })
                ->orWhere(function ($query) use ($aff) {
                    $query->where('to_id', '=', $aff->id)
                        ->where('acc_send', '=', 'merchant');
                })
                ->delete();

            ConnectCustomer::whereAffiliateId($aff->id)->whereShopId($aff->shop_id)->delete();

            $affProfile = DB::connection('mysql2')->table('affiliate_profile_activity')->select('*')->where(['shop_id' => $aff->shop_id, 'to_email' => $aff->email]);
            if ($affProfile->count() > 0) {
                $affProfile->delete();
            }
            $aff->delete();

            if ($fakerAffiliate instanceof Affiliate) {
                $this->destroyaffiliateMultiShop($aff);
            }
            event(new DeleteAffiliate($aff));

            if ($affMpUserId) dispatch(new ReSyncAffiliateMarketplaceRankJob(['mkpUserId' => $affMpUserId]));

            return response()->json([
                'status' => 'ok',
                'message' => 'success'
            ], 200);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'not found'
            ], 404);
        }
    }

    public function deleteNetWorkCommission($affId, $refIds)
    {
        $user = Auth::user();
        $records = Referral::where('shop_id', $user->shop_id)
            ->where('level', '<>', 0)
            ->where('status', '<>', Referral::PAID)
            ->where('child_affiliate', $affId)
            ->whereIn('referral_id', $refIds)
            ->get();

        return $records->each(function ($record){
            $record->delete();
        });
    }

    public function search(Request $request): JsonResponse
    {
        $user = Auth::user();
        if (!$user) return response()->json([]);

        $shopId = $user->shop_id;
        if (!$shopId) return response()->json([]);

        $query = addslashes($request->get("query"));
        $affiliate = Affiliate::where('shop_id', $shopId)->where(function ($q) use ($query, $shopId) {
            $q->where('first_name', 'like', '%' . $query . '%')
                ->orWhere('last_name', 'like', '%' . $query . '%')
                ->orWhere(DB::raw("concat(first_name, ' ', last_name)"), 'like', '%' . $query . '%')
                ->orWhere('email', 'like', '%' . $query . '%');
            if ($shopId == 41249 || $shopId == 44066) {
                $q->orWhere('personal_detail', 'like', '%' . $query . '%');
            }
        });
        if ($request->has('only_customer_refer')) {
            $affiliate->where('register_method', '=', Affiliate::REG_BY_CUSTOMER_REFERRAL);
        } elseif ($request->has('affiliate_only')) {
            $affiliate->where('register_method', '!=', Affiliate::REG_BY_CUSTOMER_REFERRAL);
        }
        if ($request->has('aff_id')) {
            $affiliate = $affiliate->where('id', '!=', $request->get('aff_id'));
        }
        if ($request->has('status')) {
            $affiliate = $affiliate->where('status', Affiliate::ACTIVE_AFFILIATE);
        }
        $affiliate = $affiliate->select('id', 'email', 'first_name', 'last_name', 'personal_detail', 'register_method')->get();
        return response()->json($affiliate);
    }

    public function getCommissionDatatables($id)
    {
        if (Auth::user()->shop_id == 12737) {
            $refs = Referral::where('affiliate_id', $id)->with('customer');
        } else {
            $refs = Referral::where('affiliate_id', $id);
        }

        return DataTables::of($refs)
            ->make();
    }

    /**
     * Check coupon total and pricing plan to
     * enable/disable create multi coupons for an affiliate
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function checkPlan(Request $request)
    {
        $user = Auth::user();

        $affiliateId = $request->get('affiliate_id');
        // Get total of coupon of this affiliate
        $coupon = AffiliateCoupon::where('shop_id', $user->shop_id)
            ->where('affiliate_id', $affiliateId)
            ->count();

        // Check pricing plan to display add coupon form
        $canCreate = $user->can('create-coupon');
        if (!$canCreate && $coupon > 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'You need to Upgrade to create more coupon code'
            ]);
        } else {
            return response()->json([
                'status' => 'ok',
                'message' => view('app.includes.create_coupon')->render()
            ]);
        }
    }

    /**
     * Get coupon codes of specific affiliate (Datatable)
     * @param $id
     * @return mixed
     * @throws Exception
     */
    public function getCoupon($id)
    {
        if (!$id) return DataTables::of([])->make();
        $affiliate = Affiliate::findOrFail($id);
        return DataTables::of($affiliate->coupons)->make();
    }

    /**
     * Create new coupon (Ajax)
     *
     * @param $id
     * @param Request $request
     * @return JsonResponse
     */
    public function createCoupon($id, Request $request)
    {
        $coupon = $request->get('coupon');

        if (empty($coupon)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Please enter a coupon code'
            ]);
        }

        $shopId = Auth::user()->shop_id;
        $isExist = AffiliateCoupon::where('shop_id', $shopId)
            ->where('coupon', $coupon)
            ->whereNotNull('coupon')
            ->first();

        if (!$isExist) {
            // Check this discount code is exist on Shopify or not
            $clientApi = session('client_api');

            if ($clientApi) {
                try {
                    // Check discount code exists or not
                    $couponCode = encodeURIComponent(trim($coupon));
                    $clientApi->get('discount_codes/lookup.json', null, ['code' => $couponCode]);
                    $codeIsExist = true;
                } catch (Exception $e) {
                    $codeIsExist = false;
                }

                if ($codeIsExist) {
                    // Save
                    AffiliateCoupon::create([
                        'affiliate_id' => $id,
                        'shop_id' => $shopId,
                        'coupon' => trim($coupon),
                        'coupon_description' => $request->get('coupon_description')
                    ]);

                    return response()->json([
                        'status' => 'ok',
                        'message' => 'Success!'
                    ], 200);
                }

                return response()->json([
                    'status' => 'error',
                    'message' => 'Coupon code does not exist on your store. Make sure you enter the exact code (pay attention on uppercase/lowercase letters)'
                ]);
            }
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Coupon code already belong to another affiliate'
        ]);
    }

    /**
     * @param $id
     * @param Request $request
     * @return JsonResponse
     * @throws GuzzleException
     */
    public function updateCoupon($id, Request $request): JsonResponse
    {
        $shopId = Auth::user()->shop_id;
        $shop = Shop::find($shopId);
        $coupon = $request->get('coupon');
        if (empty($coupon)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Please enter a coupon code'
            ]);
        }

        /** @var ProgramRepository $programRefer */
        $programRepository = app(ProgramRepository::class);
        $programRefer = $programRepository->getReferCustomerProgram($shopId);
        $discountReferCustomer = (!empty($programRefer->refer_customer_incentive['coupon'])) ? $programRefer->refer_customer_incentive['coupon'] : Program::COUPON_CODE_REFER_CUSTOMER;

        if ($coupon == $discountReferCustomer) {
            return response()->json([
                'status' => 'error',
                'message' => 'You cant update coupon with name: ' . $discountReferCustomer
            ]);
        }

        if (strlen($coupon) > 100) {
            return response()->json([
                'status' => 'error',
                'message' => 'The coupon name must not exceed 100 characters.'
            ]);
        }

        $isExist = AffiliateCoupon::where('shop_id', $shopId)
            ->where('id', '<>', $id)
            ->where('coupon', $coupon)
            ->whereNotNull('coupon')
            ->first();   // check coupon exist
        if (!$isExist) {
            // Check this discount code is existing on Shopify or not
            $clientApi = session('client_api');
            if ($clientApi) {
                $currentCoupon = AffiliateCoupon::find($id);
                $discountCodeApi = new DiscountCode($clientApi);
                $codeCurrentIsExist = $discountCodeApi->lookup(trim($currentCoupon->coupon), $shop); //check coupon current exist shopify
                $codeCurrentIsExist = $codeCurrentIsExist['discount_code'] ?? null;
                if (!$codeCurrentIsExist) {
                    return response()->json([
                        'status' => 'error',
                        'message' => "This coupon has been deleted on Shopify so it couldn't be renamed. Please delete the coupon on UpPromote and recreate it."
                    ]);
                }
                $codeIsExist = $discountCodeApi->lookup(trim($coupon), $shop); //check coupon new exist shopify
                $codeIsExist = $codeIsExist['discount_code'] ?? null;
                if ($codeIsExist) {
                    if ($coupon != $currentCoupon->coupon) {
                        return response()->json([
                            'status' => 'error',
                            'message' => "This coupon already exists on your Shopify store."
                        ]);
                    } else {
                        $currentCoupon->coupon_description = $request->get('coupon_description');
                        $currentCoupon->save();
                        return response()->json([
                            'status' => 'ok',
                            'message' => 'Success!'
                        ]);
                    }
                }
                try {
                    $client = new Client(['allow_redirects' => true]);
                    $requestDataPriceRule = new \GuzzleHttp\Psr7\Request('GET', "https://{$shop->shop}/admin/api/" . config('shopify.api_version') . "/price_rules/{$codeCurrentIsExist['price_rule_id']}/discount_codes.json", [
                        'X-Shopify-Access-Token' => $shop->access_token
                    ]);
                    $responseGetPriceRule = $client->send($requestDataPriceRule)->getBody()->getContents();
                    $res = json_decode($responseGetPriceRule, true);

                    if (count($res['discount_codes']) == 1) {
                        $priceRuleApi = new PriceRule(generateClientApi($shop));
                        $priceRuleApi->update($codeCurrentIsExist['price_rule_id'], [
                            'title' => $coupon
                        ]);
                    }
                    $couponUpdate = [
                        "discount_code" => array(
                            "id" => $codeCurrentIsExist['id'],
                            'code' => $coupon,
                        )
                    ];
                    $requestApi = new \GuzzleHttp\Psr7\Request('PUT', "https://{$shop->shop}/admin/api/" . config('shopify.api_version') . "/price_rules/{$codeCurrentIsExist['price_rule_id']}/discount_codes/{$codeCurrentIsExist['id']}.json", [
                        'X-Shopify-Access-Token' => $shop->access_token,
                        'Content-Type' => 'application/json'
                    ], json_encode(
                        $couponUpdate
                    ));


                    $client->send($requestApi);
                    $currentCoupon->coupon = trim($coupon);
                    $currentCoupon->coupon_description = $request->get('coupon_description');
                    $currentCoupon->save();
                } catch (Exception $e) {
                    info($e->getMessage());
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Update coupon error.'
                    ]);
                }
                return response()->json([
                    'status' => 'ok',
                    'message' => 'Success!'
                ]);
            }
        }
        return response()->json([
            'status' => 'error',
            'message' => 'This coupon already exists.'
        ]);
    }

    /**
     * Delete a coupon code (Ajax)
     *
     * @param $id
     * @return JsonResponse
     */
    public function deleteCoupon($id)
    {
        $coupon = AffiliateCoupon::find($id);
        if ($coupon) {
            $coupon->delete();

            return response()->json([
                'status' => 'ok',
                'message' => 'Success!'
            ], 200);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot delete this coupon code'
            ]);
        }
    }

    public function getSetting($id)
    {
        $settings = AffiliateSetting::where('affiliate_id', $id)->select('payment_method', 'payment_info')->first();
        $texts = config('myconfig.app.payment_text');
        return response()->json([
            'status' => 'ok',
            'message' => 'success',
            'data' => ['settings' => $settings, 'texts' => $texts]
        ], 200);
    }

    public function createAffiliateByPurchasePopup(Request $request)
    {
        $inputs = $request->all();
        $pascalCase = Str::studly(strtolower($inputs['first_name']));
        $detachWord = ltrim(preg_replace('/(?<! )[A-Z]/', ' $0', $pascalCase));
        if (countUpper($detachWord) > 3) {
            return response()->json([
                'status' => 'ok',
                'message' => 'success'
            ]);
        }
        $inputs['first_name'] = $inputs['first_name'] === 'null' ? null : $inputs['first_name'];
        $inputs['last_name'] = $inputs['last_name'] === 'null' ? null : $inputs['last_name'];

        if (!$inputs['first_name'] && !$inputs['last_name']) {
            return response()->json([
                'status' => 'error',
                'message' => 'First name or last name is required.'
            ]);
        }

        if (empty($inputs['email'])) {
            return response()->json([
                'status' => 'ok',
                'message' => 'success'
            ]);
        }

        $shopUrl = $request->get('shop');
        $shop = Shop::join('merchant_settings', 'shops.id', '=', 'merchant_settings.shop_id')
            ->join('users', 'shops.id', '=', 'users.shop_id')
            ->where('shops.shop', $shopUrl)
            ->whereNotNull('shops.access_token')
            ->select(
                'shops.id',
                'merchant_settings.referral_link',
                'merchant_settings.brand_name',
                'users.subdomain',
                'merchant_settings.auto_active_affiliate',
                'merchant_settings.auto_active_affiliate_program',
                'merchant_settings.default_affiliate_link_program'
            )->first();
        if (!$shop) {
            return response()->json([
                'status' => 'error',
                'message' => 'Your store has not been completed the UpPromote app installation'
            ]);
        }

        $freeFeature = DB::table('free_feature')->where('shop_id', $shop->id)
            ->where('feature', 'convert-customer')
            ->where('expired_at', '>=', Carbon::now())
            ->first();
        if (helperPlan()->isPlanFree($shop) && !$freeFeature) {
            return response()->json([]);
        }
        $checkAffiliateExist = DB::table('affiliates')->where(['shop_id' => $shop->id, 'email' => $inputs['email']])->first();
        if ($checkAffiliateExist) {
            return response()->json([
                'status' => 'ok',
                'message' => 'success',
                'referral_link' => $shop->referral_link . '?sca_ref=' . $checkAffiliateExist->id . '.' . $checkAffiliateExist->hash_code
            ]);
        } else {
            $pur = DB::table('purchase_popup')
                ->join('programs', 'purchase_popup.program_id', '=', 'programs.id')
                ->where('purchase_popup.shop_id', $shop->id)
                ->where('purchase_popup.status', 1)
                ->where('programs.status', 1)
                ->select(
                    'purchase_popup.email',
                    'purchase_popup.program_id',
                    'purchase_popup.subject',
                    'purchase_popup.auto_assign_down_line',
                    'purchase_popup.promotion_method',
                    'purchase_popup.email_coupon',
                    'purchase_popup.design'
                )->first();

            if ($pur) {
                $programMLM = ProgramMlm::where('program_id', $pur->program_id)->first();
                $canUseAutoAssignMLM = helperPlan()->isPlanEnterpriseOrFreeForever($shop) && $pur->auto_assign_down_line && ($programMLM->is_enable ?? false);
                $tempPassword = generateRandomString();
                $parentAffiliate = $inputs['affiliate_id'] ?? 0;

                if (isset($inputs['coupon'])) {
                    $affiliateCoupon = AffiliateCoupon::whereCoupon($inputs['coupon'])
                        ->whereShopId($shop->id)
                        ->first();

                    if ($affiliateCoupon) {
                        $parentAffiliate = $affiliateCoupon->affiliate_id;
                    }
                }

                $parentAffiliate = $canUseAutoAssignMLM ? $parentAffiliate : 0;
                $aff = new Affiliate;
                $aff->email = $inputs['email'];
                $aff->password = Hash::make($tempPassword);
                $aff->first_name = $inputs['first_name'];
                $aff->last_name = $inputs['last_name'];
                $aff->parent_id = $parentAffiliate;

                // Check program is deleted or not
                $isProgram = Program::where('id', $pur->program_id)
                    ->where('shop_id', $shop->id)
                    ->where('status', 1)
                    ->select('id')
                    ->first();
                if ($isProgram) {
                    $aff->program_id = $pur->program_id;
                } else {
                    // If deleted, use default program
                    $programID = Program::select('id')
                        ->where('shop_id', $shop->id)
                        ->where('status', 1)
                        ->where('is_default', 1)
                        ->first();
                    $aff->program_id = $programID->id;
                }

                $aff->hash_code = generateRandomString();

                $settings = (object)[
                    'auto_active_affiliate' => $shop->auto_active_affiliate,
                    'auto_active_affiliate_program' => $shop->auto_active_affiliate_program,
                ];

                /** @var AffiliateService $affiliateService */
                $affiliateService = app(AffiliateService::class);
                $dataAutoActive = $affiliateService->autoActiveAffiliate($shop, $settings, $pur->program_id);
                $aff->status = $dataAutoActive['status'];
                $aff->is_pending = $dataAutoActive['is_pending'];
                $aff->first_approve = ($dataAutoActive['status'] == 1) ? 1 : 0;
                $aff->shop_id = $shop->id;
                $aff->register_method = Affiliate::REG_BY_PURCHASE_POPUP;
                $settingFraudAff = FraudProtectSettings::where('shop_id', $shop->id)->first();
                $inputEmail = $request->email;
                $arr = strtolower($inputEmail);
                if (!is_null($settingFraudAff)) {
                    $country = getLocationInfoByIp($request->ip());
                    $listBlockEmail = BlockEmails::where('fraud_protect_setting_id', $settingFraudAff->id)->get()->pluck('email');
                    $listBlockRegional = BlockRegions::where('fraud_protect_setting_id', $settingFraudAff->id)->get()->pluck('region');

                    if ($settingFraudAff->is_detect_affiliate == 1) {
                        $optionSignUpAff = json_decode($settingFraudAff->detect_signup_by_ip);
                        $redis = Redis::connection('tracking');
                        $dataAff = json_decode($settingFraudAff->detect_signup_by_ip);
                        $timeDetect = (int)$dataAff->within_aff;
                        $clientIp = $request->ip();

                        if ($optionSignUpAff->select_signup_aff == 0) {
                            $dataPurchasePopupKey = "data_purchase_popup_" . $shop->id . "_" . $clientIp;

                            if (is_null($redis->get($dataPurchasePopupKey))) {
                                $redis->set($dataPurchasePopupKey, $this->setRedisDontRecord($shop->id, $clientIp, 1, time()));

                                // set expire time use time_detect
                                $redis->expire($dataPurchasePopupKey, $timeDetect);
                            }

                            $dataPurchasePopupIp = $redis->get($dataPurchasePopupKey);

                            $response = json_decode($dataPurchasePopupIp);
                            $timeNow = time() - $response->last_signup;

                            if ($timeNow > 0 && $timeNow < $timeDetect) {
                                StatisticalSpams::failOrderInBlockAff($shop->id, $arr, $settingFraudAff, $country, 1);

                                return response()->json([
                                    'status' => 'error',
                                    'message' => 'Multiple signups from the same IP address detected, your registration has been rejected.',
                                ]);
                            }

                        } else {
                            $dataPurchasePopupDeniedKey = "data_purchase_popup_denied_" . $shop->id . "_" . $clientIp;

                            if (is_null($redis->get($dataPurchasePopupDeniedKey))) {
                                $redis->set($dataPurchasePopupDeniedKey, $this->setRedisDenied($shop->id, $clientIp, time()));

                                // set expire time use time_detect
                                $redis->expire($dataPurchasePopupDeniedKey, $timeDetect);
                            }

                            $dataPurchasePopupDeniedIp = $redis->get($dataPurchasePopupDeniedKey);
                            $response = json_decode($dataPurchasePopupDeniedIp);
                            $timeNow = time() - $response->last_signup;

                            if ($timeNow > 0 && $timeNow < $timeDetect) {
                                $aff->status = 0;
                                $aff->is_pending = 0;
                                $aff->first_approve = 0;
                                $aff->flag_fraud_protection = 1;
                            }
                        }
                    }

                    if (in_array($inputEmail, $listBlockEmail->toArray())) {
                        StatisticalSpams::failOrderInBlockAff($shop->id, $arr, $settingFraudAff, $country, 1);

                        return response()->json([
                            'status' => 'error',
                            'message' => 'Your email is blocked from signing up as an affiliate in this store.',
                        ]);
                    }
                    if ($settingFraudAff->detect_signup_by_region == 1) {
                        if (!empty($country['country']) && $country['country'] != '') {
                            if (!in_array($country['country'], $listBlockRegional->toArray())) {
                                StatisticalSpams::failOrderInBlockAff($shop->id, $arr, $settingFraudAff, $country, 1);

                                return response()->json([
                                    'status' => 'error',
                                    'message' => 'Your country isn’t on the allowed country list to sign up as an affiliate on this store.',
                                ]);
                            }
                        }
                    }
                    $aff->save();
                } else {
                    $aff->save();
                }
                $aff->temp_password = $tempPassword;
                $aff->subdomain = $shop->subdomain;
                $aff->referral_link = $shop->referral_link . '?sca_ref=' . $aff->id . '.' . $aff->hash_code;
                $aff->purchase = $pur;
                $aff->brand_name = $shop->brand_name;
                event(new AffiliateRegisteredByPurchase($aff));
                $affiliateCoupon = '';
                if ($pur->promotion_method == 2) {
                    $couponData = AffiliateCoupon::where('affiliate_id', $aff->id)->latest('id')->first();
                    if ($couponData) {
                        $affiliateCoupon = $couponData->coupon;
                    }
                }

                $nonActiveAffiliate = ($aff->status) ? 0 : 1;
                if ($nonActiveAffiliate) {
                    if ($pur->promotion_method == PurchasePopup::PROMOTION_METHOD_COUPON) {
                        if (isset(json_decode($pur->design, true)['thank_you_content_coupon_non_active'])) {
                            $thankYou = json_decode($pur->design, true)['thank_you_content_coupon_non_active'];
                        } else {
                            $thankYou = 'Thanks for joining our Affiliate program. An email containing Affiliate coupon will be sent to you once your account is reviewed.';
                        }
                    } else {
                        if (isset(json_decode($pur->design, true)['thank_you_content_non_active'])) {
                            $thankYou = json_decode($pur->design, true)['thank_you_content_non_active'];
                        } else {
                            $thankYou = 'Thanks for joining our Affiliate program. An email containing Affiliate link will be sent to you once your account is reviewed.';
                        }
                    }
                } else {
                    if ($pur->promotion_method == PurchasePopup::PROMOTION_METHOD_COUPON) {
                        if (isset(json_decode($pur->design, true)['thank_you_content_coupon'])) {
                            $thankYou = json_decode($pur->design, true)['thank_you_content_coupon'];
                        } else {
                            $thankYou = 'Thanks for joining our Affiliate program. An email containing Affiliate coupon will be sent to you once your account is reviewed.';
                        }
                    } else {
                        if (isset(json_decode($pur->design, true)['thank_you_content'])) {
                            $thankYou = json_decode($pur->design, true)['thank_you_content'];
                        } else {
                            $thankYou = 'Thanks for joining our Affiliate program. An email containing Affiliate link will be sent to you once your account is reviewed.';
                        }
                    }
                }
                return response()->json([
                    'status' => 'ok',
                    'p' => json_decode($pur->design, true),
                    'thankYou' => $thankYou,
                    'message' => 'success',
                    'nonActiveAffiliate' => $aff->flag_fraud_protection ? 1 : $nonActiveAffiliate,
                    'promotion_method' => $pur->promotion_method,
                    'coupon_code' => $affiliateCoupon,
                    'referral_link' => ($isProgram->setting->affiliate_link_destination && $shop->default_affiliate_link_program)
                        ? ($isProgram->setting->affiliate_link_destination) . '?sca_ref=' . $aff->id . '.' . $aff->hash_code
                        : ($shop->referral_link) . '?sca_ref=' . $aff->id . '.' . $aff->hash_code
                ]);
            } else {
                return response()->json([
                    'status' => 'ok',
                    'message' => 'success'
                ]);
            }
        }
    }

    /**
     * @param $shopId
     * @param $clientIp
     * @param $tye
     * @param $times
     * @return false|string
     */
    public function setRedisDontRecord($shopId, $clientIp, $tye, $times)
    {
        return json_encode([
            'shop_id' => $shopId,
            'ip' => $clientIp,
            'type' => $tye,
            'last_signup' => $times,
        ]);
    }

    /**
     * @param $shopId
     * @param $clientIp
     * @param $times
     * @return false|string
     */
    public function setRedisDenied($shopId, $clientIp, $times)
    {
        return json_encode([
            'shop_id' => $shopId,
            'ip' => $clientIp,
            'last_signup' => $times,
        ]);
    }

    /**
     * Receive coupon code of affiliate
     * @param Request $request
     * @return JsonResponse
     */
    public function getCouponApi(Request $request): JsonResponse
    {
        $errorResponseRaw = ['status' => 'error', 'coupon' => ''];
        $errorResponse = responseSuccess($errorResponseRaw);
        $affiliateId = (int)$request->get('aid');
        $shopDomain = $request->get('shopify_domain');
        if (!$affiliateId || !$shopDomain) return $errorResponse;

        $cached = AffiliateCouponCaching::getACCacheData($affiliateId, shopNameFromDomain($shopDomain));
        if ($cached) {
            return response()->json(
                $cached->responseData,
                $cached->statusCode
            );
        }

        /** @var Shop $shop */
        $shop = Shop::query()
            ->where('shop', $shopDomain)
            ->whereNotNull('access_token')
            ->first();
        if (!$shop) return responseSuccess(['status' => 'Shop not available', 'coupon' => null]);

        $affiliate = Affiliate::query()
            ->where('id', $affiliateId)
            ->where('status', 1)
            ->where('shop_id', $shop->id)
            ->select([
                'id',
                'program_id',
                'register_method'
            ])->first();
        if (!$affiliate) {
            return responseSuccess(['status' => 'Affiliate not available', 'coupon' => null]);
        }

        if ($affiliate->register_method == Affiliate::REG_BY_CUSTOMER_REFERRAL) {
            if (helperPlan()->isPlanFree($shop)) {
                return responseSuccess(['status' => 'Feature not available', 'coupon' => null]);
            }
        } else {
            if (!helperPlan()->planProfessionalUp($shop)
                && !helperPlan()->canUseFeatureAddOn($shop, [Feature::FEATURE_AUTO_DISCOUNT]) // Feature add on
            ) {
                return responseSuccess(['status' => 'Feature not available', 'coupon' => null]);
            }
        }

        $program = Program::query()
            ->select([
                'programs.auto_apply_coupon',
                'programs.auto_apply_coupon_by',
                'programs.defined_coupon',
                'programs.refer_customer_incentive',
                'programs.deleted_at'
            ])
            ->join('merchant_settings', 'programs.shop_id', '=', 'merchant_settings.shop_id')
            ->where('programs.status', 1)
            ->where('programs.id', $affiliate->program_id)
            ->where('merchant_settings.enable_coupon', 1)
            ->withTrashed()
            ->first();
        if (!$program) return $errorResponse;

        if ($program->refer_customer_incentive && $program->deleted_at) {
            $discountReferCustomer = !empty($program->refer_customer_incentive['coupon'])
                ? $program->refer_customer_incentive['coupon']
                : Program::COUPON_CODE_REFER_CUSTOMER;
            return responseSuccess(['status' => 'ok', 'coupon' => $discountReferCustomer]);
        }

        if ($program->deleted_at) return $errorResponse;

        if ($program->auto_apply_coupon == 1) {
            $response = ['status' => 'ok', 'coupon' => null];
            if ($program->auto_apply_coupon_by == Program::BY_DEFINED_COUPON && $program->defined_coupon != '') {
                $response['coupon'] = $program->defined_coupon;
            }
            if ($program->auto_apply_coupon_by == Program::BY_AFFILIATE_COUPON) {
                $affiliateCoupon = AffiliateCoupon::where('affiliate_id', $affiliate->id)->latest()->first();
                $response['coupon'] = optional($affiliateCoupon)->coupon;
            }
            return responseSuccess($response);
        }

        return $errorResponse;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getFacebookPixel(Request $request): JsonResponse
    {
        if (!$request->has('shop') || !$request->has('aff_id')) {
            return responseSuccess(['message' => 'Identity not found']);
        }
        $shop = $request->get('shop');
        $affiliateId = (int)$request->get('aff_id');

        $cached = FacebookPixel::getFBPCacheData($affiliateId, shopNameFromDomain($shop));
        if ($cached) {
            return response()->json($cached->responseData, $cached->statusCode);
        }

        $result = Shop::where('shop', $shop)
            ->whereNotNull('access_token')
            ->join('merchant_settings', 'merchant_settings.shop_id', '=', 'shops.id')
            ->select('shops.id', 'merchant_settings.enable_fb_pixel')
            ->first();

        if (!$result || !$result->enable_fb_pixel) {
            return responseSuccess(['status' => 'Feature not available']);
        }

        $affiliate = Affiliate::where('id', $affiliateId)
            ->where('shop_id', $result->id)
            ->where('status', 1)
            ->select('fb_pixel')
            ->first();

        if (!$affiliate || !$affiliate->fb_pixel) {
            return responseSuccess(['status' => 'Client do not setup.']);
        }
        return responseSuccess([
            'status' => 'ok',
            'pixel' => $affiliate->fb_pixel
        ]);
    }

    private function searchLevel($data, $level)
    {
        foreach ($data as $d) {
            if ($d['level'] == $level) return $d;
        }
        return null;
    }

    public function setParent($id, Request $request)
    {
        $user = Auth::user();
        $affiliate = Affiliate::find($id);
        $parentId = $request->get('p_id');
        $parent = Affiliate::findOrFail($parentId);
        $granId = $parent->parent_id;
        $level = 1;
        while ($granId != 0) {
            $gran = Affiliate::find($granId);
            if ($gran->id == $id || $level > 20) {
                return response()->json([
                    'status' => 'exist',
                    'message' => 'Cannot choose an existing downline affiliate to be an upline affiliate',
                ]);
            }
            $granId = $gran->parent_id;
            $level++;
        }
        $affiliate->parent_id = $parentId;
        $affiliate->is_first_approve_recruitment_bonus = 1;
        $affiliate->save();
        /** @var MultipleShopService $multipleShopService */
        $multipleShopService = app(MultipleShopService::class);
        $canUseMultipleShop = $multipleShopService->canUseMultipleShop($affiliate->shop_id);
        if ($canUseMultipleShop) {
            $shopsConnected = $multipleShopService->getShopRelationship($affiliate->shop_id, $affiliate->shop_id);
            foreach ($shopsConnected as $shop) {
                $upLineAffiliate = Affiliate::where([
                    'shop_id' => $shop['shop_id'],
                    'email' => $parent->email
                ])->first();
                $assAffiliate = Affiliate::where([
                    'shop_id' => $shop['shop_id'],
                    'email' => $affiliate->email
                ])->first();
                if ($upLineAffiliate && $assAffiliate) {
                    if ($assAffiliate->parent_id === 0) {
                        $assAffiliate->parent_id = $upLineAffiliate->id;
                        $assAffiliate->save();
                    }
                }
            }
        }

        return response()->json([
            'status' => 'ok',
            'message' => 'success',
            'parent' => $parent
        ], 200);


    }

    public function removeParent($id): JsonResponse
    {
        $affiliate = Affiliate::findOrFail($id);
        $parent = Affiliate::findOrFail($affiliate->parent_id);
        /** @var MultipleShopService $multipleShopService */
        $multipleShopService = app(MultipleShopService::class);
        $canUseMultipleShop = $multipleShopService->canUseMultipleShop($affiliate->shop_id);
        if ($canUseMultipleShop) {
            $shopRelationship = $multipleShopService->getShopRelationship($affiliate->shop_id, $affiliate->shop_id);
            foreach ($shopRelationship as $shop) {
                $upLineAffiliate = Affiliate::where([
                    'shop_id' => $shop['shop_id'],
                    'email' => $parent->email
                ])->first();
                $assAffiliate = Affiliate::where([
                    'shop_id' => $shop['shop_id'],
                    'email' => $affiliate->email
                ])->first();
                if ($upLineAffiliate && $assAffiliate) {
                    if ($assAffiliate->parent_id === $upLineAffiliate->id) {
                        $assAffiliate->parent_id = 0;
                        $assAffiliate->save();
                    }
                }
            }
        }
        $affiliate->parent_id = 0;
        $affiliate->save();
        return response()->json([
            'status' => 'ok',
            'message' => 'success'
        ]);
    }

    public function import(Request $request): JsonResponse
    {
        $user = Auth::user();
        $shopId = $user->shop_id;
        $inputs = $request->all();
        $sendInvitation = (helperPlan()->isPlanTrialShopify($shopId)) ? 0 : $inputs['send_invitation'];
        $program = Program::where('shop_id', $shopId)->where('id', $inputs['program_id'])->first();
        if ($inputs['password_type']) {
            $validatorPassword = Validator::make($inputs, [
                'password' => 'required|max:255'
            ]);
            if ($validatorPassword->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => $validatorPassword->errors()->first('password')
                ]);
            }
        }

        $totalAffiliate = Affiliate::where('shop_id', $shopId)->whereDate('created_at', Carbon::today())->count();

        if ($request->hasFile('import_file')) {
            $extension = File::extension(request()->file('import_file')->getClientOriginalName());
            if ($extension == 'xlsx' || $extension == 'xls' || $extension == 'csv') {
                $dataImport = Excel::toArray(new AffiliateImport, request()->file('import_file'));
                $invalidRow = [];
                if ($shopId != 80193) {
                    //check number affiliate import
                    if (count($dataImport[0]) > 200) {
                        return response()->json([
                            'status' => 'error',
                            'message' => 'Each file uploaded can have maximum 200 affiliates'
                        ]);
                    }
                    //check 300 affiliate
                    if ($totalAffiliate > 300) {
                        return response()->json([
                            'status' => 'error',
                            'message' => 'You can import maximum 300 affiliates a day.'
                        ]);
                    }
                }

                /**
                 * Check missing required columns
                 */
                $missingColumn = collect(['first_name', 'last_name', 'email'])
                    ->diff(collect(collect($dataImport[0])->first())->keys()->toArray())
                    ->toArray();
                if (count($missingColumn)) {
                    $missingColumnMessage = implode(', ', $missingColumn);
                    return response()->json([
                        'status' => 'error',
                        'message' => "The $missingColumnMessage column is required in your file."
                    ]);
                }

                foreach ($dataImport[0] as $d) {
                    $validatorEmail = Validator::make($d, [
                        'email' => ['required', new ValidInternationalEmail()],
                        'first_name' => 'required|max:255',
                        'last_name' => 'required|max:255'
                    ]);
                    if ($validatorEmail->fails()) {
                        $invalidRow[] = $d['email'] ?? null;
                        continue;
                    }

                    if ($shopId == 80193) {
                        if (!empty($d['passw'])) {
                            $password = $d['passw'];
                        }
                    } else {
                        if ($inputs['password_type']) {
                            $password = $inputs['password'];
                        } else {
                            $password = generateRandomString();
                        }
                    }

                    $newAff = Affiliate::firstOrNew(
                        [
                            'email' => $d['email'],
                            'shop_id' => $shopId
                        ],
                        [
                            'password' => Hash::make($password),
                            'first_name' => $d['first_name'],
                            'last_name' => $d['last_name'],
                            'hash_code' => generateRandomString(),
                            'program_id' => !empty($d['program_id']) ? $d['program_id'] : $program->id,
                            'shop_id' => $shopId,
                            'status' => 1,
                            'register_method' => Affiliate::REG_BY_IMPORT,
                            'coupon' => $d['coupon'] ?? null,
                            'company' => $d['company'] ?? null,
                            'address' => $d['address'] ?? null,
                            'city' => $d['city'] ?? null,
                            'state' => $d['state'] ?? null,
                            'zipcode' => $d['zip_code'] ?? null,
                            'phone' => $d['phone'] ?? null,
                            'website' => $d['website'] ?? null,
                            'vat_number' => $d['vat_number'] ?? null,
                            'country' => $d['country'] ?? null
                        ]
                    );
                    if (!$newAff->id) {
                        //check coupon
                        $newAff->save();
                        $newAff->temp_password = $password;
                        $newAff->notification = $sendInvitation;
                        $couponImport = $d['coupon'] ?? null;
                        event(new AffiliateImported($newAff, $couponImport, true));
                    } else {
                        $invalidRow[] = $d['email'];
                    }
                    if (isset($d['coupon'])) {
                        $isExistCoupon = AffiliateCoupon::where('shop_id', $shopId)->where('coupon', $d['coupon'])->first();
                        if (!$isExistCoupon) {
                            $newCoupon = new AffiliateCoupon;
                            $newCoupon->shop_id = $shopId;
                            $newCoupon->affiliate_id = $newAff->id;
                            $newCoupon->coupon = $d['coupon'];
                            $newCoupon->save();
                        }
                    }
                    $merchant = User::where('shop_id', $shopId)->with('setting')->first();
                    try {
                        $this->syncKlaviyo($newAff, $merchant);
                    } catch (Exception $e) {
                        info($e->getMessage());
                    }
                    if (helperPlan()->planProfessionalUp()) {
                        $data = SettingModel::getApiMailchimp($shopId);
                        $listId = $data['listId'] ?? null;
                        $apikey = $data['apikey'] ?? null;
                        $shop = Shop::find($shopId);
                        $apiOmnisend = json_decode($shop->api_omnisend, true);
                        $apiMailchimp = json_decode($shop->api_mailchimp, true);
                        if ($apiMailchimp && $apiMailchimp['status'] == 1) {
                            dispatch((new SyncMailchimpJob(collect([$newAff]), $listId, $apikey))->onQueue('integration-mailchimp')); // queue job
                        }
                        if ($apiOmnisend && $apiOmnisend['status'] == 1) {
                            $apikeyOmni = $apiOmnisend['apikey'] ?? null;
                            dispatch((new SyncOmnisendJob(collect([$newAff]), $apikeyOmni))->onQueue('integration-omnisend')); // queue job
                        }
                    }
                }
                activity('affiliate')
                    ->causedBy($user)
                    ->log('imported an affiliate list');
                return response()->json([
                    'status' => 'ok',
                    'message' => 'success',
                    'invalid_row' => $invalidRow
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'File is a ' . $extension . ' file. Please upload a valid xlsx/xls/csv file.'
                ]);
            }
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'import file is require'
            ]);
        }
    }

    public static function affiliatesGenerator($shopId, $conditions, $createdAt)
    {
        foreach (Affiliate::select(
            'id',
            'program_id',
            'email',
            'first_name',
            'last_name',
            'address',
            'company',
            'country',
            'phone',
            'personal_detail',
            'city',
            'state',
            'website',
            'facebook',
            'youtube',
            'instagram',
            'tiktok',
            'verified',
            'parent_id',
            'additional_info',
            'status',
            'is_pending',
            'vat_number',
            'hash_code',
            'last_login',
            'login_count',
            'register_method',
            'created_at'
        )->where('shop_id', $shopId)
                     ->where($conditions)
                     ->whereBetween('created_at', $createdAt)
                     ->with(['coupons' => function ($query) {
                         $query->select('id', 'affiliate_id', 'coupon');
                     }, 'setting', 'programMLM', 'program'])->cursor() as $aff) {
            yield $aff;
        }
    }

    //custom export cho shopID = 12737, 26491
    private
    function customAffiliatesGenerator($shopId, $conditions)
    {
        foreach (Affiliate::where('shop_id', $shopId)->where($conditions)->with(['coupons' => function ($query) {
            $query->select('id', 'affiliate_id', 'coupon');
        }, 'setting'])->cursor() as $aff) {
            yield $aff;
        }
    }

    /**
     * @param $data
     * @return string
     */
    public
    static function formatAdditional($data)
    {
        $additional = '';
        if (!empty($data) && $data != '[]') {
            foreach ($data as $val) {
                $value = is_array($val['value']) ? join(', ', $val['value']) : $val['value'];
                $additional .= $val['label'] . ': ' . $value . "\n";
            }
        }
        return $additional;
    }

    /**
     * @param $data
     * @return string
     */
    public
    static function formatPaymentInfo($data)
    {
        $paymentInfo = '';
        if (!empty($data)) {
            foreach ($data as $key => $val) {
                $paymentInfo .= $key . ': ' . $val . "\n";
            }
        }

        return $paymentInfo;
    }

    public function export(Request $request)
    {
        $user = Auth::user();
        $setting = $user->setting;
        $nowDay = Carbon::now()->toDateString() . ' 23:59:59';

        // Where export
        $conditions = [];
        $createdAt = ['1970-01-01', "$nowDay"];

        if ($request->get('filtered-affiliates') == 1) {
            if ($request->status != -1) {
                if ($request->status == 2) {
                    $conditions[] = ['is_pending', 1];
                } else {
                    $conditions[] = ['status', "$request->status"]; // filterd date
                }
            }
            if ($request->get('program')) {
                $conditions[] = ['program_id', (int)$request->get('program', 0)];
            }
            if (!empty($request->start_date) && !empty($request->end_date)) {
                $createdAt = ["$request->start_date", "$request->end_date" . ' 23:59:59'];
            }
        }
        activity('affiliate')
            ->causedBy($user)
            ->log('exported all affiliates');
        // total affiliate export
        $countAffiliate = Affiliate::where('shop_id', $user->shop_id)
            ->where($conditions)
            ->whereBetween('created_at', $createdAt)
            ->count();

        $filename = 'uppromote_affiliates_' . Carbon::now()->toDateString() . '.xlsx';
        $timezone = getTimeZoneCurrent();

        // START custom export cho shop 12737
        if ($user->shop_id == 12737) {
            return (new FastExcel($this->customAffiliatesGenerator($user->shop_id, $conditions)))->download($filename, function ($affiliate) use ($setting, $user, $timezone) {
                $networkLink = route('aff.register', ['domain' => $user->subdomain, 'p' => $affiliate->id]);
                $account = '';
                $number = '';
                $swift = '';
                $createdAt = Carbon::createFromTimestamp(intval($affiliate->created_at))->toDateTimeString();
                $createdAt = Carbon::createFromFormat('Y-m-d H:i:s', $createdAt)->setTimezone($timezone)->format('Y-m-d g:i A');
                if (isset($affiliate->setting)) {
                    if ($affiliate->setting->payment_method == 'bank') {
                        $account = !empty($affiliate->setting->payment_info['account']) ? $affiliate->setting->payment_info['account'] : '';
                        $number = !empty($affiliate->setting->payment_info['number']) ? $affiliate->setting->payment_info['number'] : '';
                        $swift = !empty($affiliate->setting->payment_info['swift']) ? $affiliate->setting->payment_info['swift'] : '';
                    }
                }

                $UmsatzsteuerNummer = '';
                if ($affiliate->additional_info == '' || $affiliate->additional_info == '[]') {
                    $UmsatzsteuerNummer = '';
                } else {
                    foreach ($affiliate->additional_info as $item) {
                        if (strpos($item['label'], 'Umsatzsteuer Nummer') !== false) {
                            $UmsatzsteuerNummer = $item['value'];
                        }
                    }
                }

                return [
                    'ID' => $affiliate->id,
                    'email' => $affiliate->email,
                    'First Name' => $affiliate->first_name,
                    'Last Name' => $affiliate->last_name,
                    'Address' => $affiliate->address,
                    'Company' => $affiliate->company,
                    'Country' => $affiliate->country,
                    'Phone' => $affiliate->phone,
                    'City' => $affiliate->city,
                    'Zipcode' => $affiliate->zipcode,
                    'State' => $affiliate->state,
                    'Website' => $affiliate->website,
                    'Facebook' => $affiliate->facebook,
                    'Youtube' => $affiliate->youtube,
                    'Instagram' => $affiliate->instagram,
                    'Tiktok' => $affiliate->tiktok,
                    'Verified email' => $affiliate->verified == 1 ? 'yes' : 'no',
                    'Referral link' => genReferralLink($affiliate, $setting->referral_link),
                    'Network link' => $networkLink,
                    'Upline affiliate' => $affiliate->parent_id,
                    'Coupons' => implode(',', $affiliate->coupons->pluck('coupon')->toArray()),
                    'Date created' => $createdAt,
                    'Payment method' => isset($affiliate->setting) ? $affiliate->setting->payment_method : '',
                    'Account' => $account,
                    'Number' => $number,
                    'Swift' => $swift,
                    'Umsatzsteuer Nummer (UID zb. ATUXXXXXX)' => $UmsatzsteuerNummer
                ];
            });
        }
        // END custom export cho shop 12737

        // START export shopID = 26491
        if ($user->shop_id == 26491 && helperPlan()->isPlanEnterprise()) {

            return (new FastExcel($this->customAffiliatesGenerator($user->shop_id, $conditions)))->download($filename, function ($affiliate) use ($setting, $user, $timezone) {
                $networkLink = route('aff.register', ['domain' => $user->subdomain, 'p' => $affiliate->id]);
                $createdAt = Carbon::createFromTimestamp(intval($affiliate->created_at))->toDateTimeString();
                $createdAt = Carbon::createFromFormat('Y-m-d H:i:s', $createdAt)->setTimezone($timezone)->format('Y-m-d g:i A');

                $teacherCode = '';
                if ($affiliate->additional_info == '' || $affiliate->additional_info == '[]') {
                    $teacherCode = '';
                } else {
                    foreach ($affiliate->additional_info as $item) {
                        if ($item['label'] == 'Teacher Code') {
                            $teacherCode = $item['value'];
                        }
                    }
                }

                return [
                    'ID' => $affiliate->id,
                    'email' => $affiliate->email,
                    'First Name' => $affiliate->first_name,
                    'Last Name' => $affiliate->last_name,
                    'Address' => $affiliate->address,
                    'Company' => $affiliate->company,
                    'Country' => $affiliate->country,
                    'Phone' => $affiliate->phone,
                    'City' => $affiliate->city,
                    'Zipcode' => $affiliate->zipcode,
                    'State' => $affiliate->state,
                    'Website' => $affiliate->website,
                    'Facebook' => $affiliate->facebook,
                    'Youtube' => $affiliate->youtube,
                    'Instagram' => $affiliate->instagram,
                    'Tiktok' => $affiliate->tiktok,
                    'Verified email' => $affiliate->verified == 1 ? 'yes' : 'no',
                    'Referral link' => genReferralLink($affiliate, $setting->referral_link),
                    'Network link' => $networkLink,
                    'Upline affiliate' => $affiliate->parent_id,
                    'Coupons' => implode(',', $affiliate->coupons->pluck('coupon')->toArray()),
                    'Teacher Code' => $teacherCode,
                    'Date created' => $createdAt
                ];
            });
        }
        // END export shopID = 26491

        // total affiliate > 1000 => sent file email
        $limitForceExportAffiliate = config('app.env') == 'production' ? 1000 : 100;
        if ($countAffiliate > $limitForceExportAffiliate) {
            dispatch(new SendAffiliatesQueue($user->shop_id, $conditions, $createdAt, $user, $setting, $countAffiliate, [], false, $timezone))->delay(now()->addSeconds(10));
            $messages = 'Since the number of affiliates exceeds ' . $limitForceExportAffiliate . ', we will send the list to your email ' . $user->email . ' in few minutes. Depending on the length of your list, this could take some time.';
            session()->flash('export-aff', $messages);
            return redirect()->back();
        }

        return (new FastExcel($this->affiliatesGenerator($user->shop_id, $conditions, $createdAt)))->download($filename, function ($affiliate) use ($setting, $user, $timezone) {
            $affiliate->registerMethodToText();
            $createdAt = Carbon::createFromTimestamp(intval($affiliate->created_at))->toDateTimeString();
            $createdAt = Carbon::createFromFormat('Y-m-d H:i:s', $createdAt)->setTimezone($timezone)->format('Y-m-d g:i A');
            if($affiliate->last_login) {
                $lastLogin = Carbon::createFromTimestamp(intval($affiliate->last_login))->toDateTimeString();
                $lastLogin = Carbon::createFromFormat('Y-m-d H:i:s', $lastLogin)->setTimezone($timezone)->format('Y-m-d g:i A');
            }

            return [
                'ID' => $affiliate->id,
                'email' => $affiliate->email,
                'First Name' => $affiliate->first_name,
                'Last Name' => $affiliate->last_name,
                'Address' => $affiliate->address,
                'Company' => $affiliate->company,
                'Country' => $affiliate->country,
                'Phone' => $affiliate->phone,
                'Personal Detail' => $affiliate->personal_detail,
                'City' => $affiliate->city,
                'Zip code' => $affiliate->zipcode,
                'State' => $affiliate->state,
                'Website' => $affiliate->website,
                'Facebook' => $affiliate->facebook,
                'Youtube' => $affiliate->youtube,
                'Instagram' => $affiliate->instagram,
                'Tiktok' => $affiliate->tiktok,
                'Verified email' => $affiliate->verified == 1 ? 'yes' : 'no',
                'Program' => optional($affiliate->program)->name,
                'Referral link' => genReferralLink($affiliate, $setting->referral_link),
                'Network Link' => route('aff.register', ['domain' => $user->subdomain, 'ref' => $affiliate->hash_code, 'p' => !empty($affiliate->programMLM->invited_program_id) ? $affiliate->programMLM->invited_program_id : $affiliate->program_id]),
                'Upline affiliate' => $affiliate->parent_id,
                'Payment Method' => optional($affiliate->setting)->payment_method,
                'Payment Info' => $this->formatPaymentInfo(optional($affiliate->setting)->payment_info),
                'Coupons' => implode(', ', $affiliate->coupons->pluck('coupon')->toArray()),
                'Additional Info' => $this->formatAdditional($affiliate->additional_info),
                'Status' => ($affiliate->status == Affiliate::ACTIVE_AFFILIATE) ? 'Active' : (($affiliate->is_pending == Affiliate::IS_PENDING) ? 'Pending' : 'Inactive'),
                'VAT number' => $affiliate->vat_number,
                'Login count' => $affiliate->login_count,
                'Last login' => $lastLogin ?? null,
                'Date created' => $createdAt,
                'Signup source' => $affiliate->register_method
            ];
        });
    }

    /**
     * Update custom affiliate link
     *
     * @param SaveCustomLinkRequest $request
     * @param AffiliateService $affiliateService
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function saveCustomAffiliateLink(SaveCustomLinkRequest $request, AffiliateService $affiliateService): JsonResponse
    {
        $affiliate = Affiliate::find($request->get('id'));
        $this->authorize('update-affiliate', $affiliate);
        $shop = $this->shop();
        $resolveCode = $affiliateService->storeCustomLink($shop, $affiliate, (string)$request->get('custom_affiliate_link', ''));

        /******** Start Mixpanel ********/
        if (in_array($resolveCode, [1, 2])) {
            try {
                dispatch(new MixpanelAnalyticsJob('track-event', $shop->id, [
                    'Current page' => 'Affiliate profile',
                    'Screen size' => !empty($_COOKIE['screenSize']) ? $_COOKIE['screenSize'] : null,
                    'Feature on' => 'true',
                    'Affiliate email' => $affiliate->email ?? null,
                ], [
                    'shop' => $shop,
                    'event_name' => 'Custom affiliate link',
                    'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                    'params' => !empty(request()->all()) ? request()->all() : null,
                ], session()->has('sudo')));
            } catch (Exception $exception) {
                logger('AffiliateController saveCustomAffiliateLink' . $exception->getMessage());
            }
        }
        /******** End Mixpanel ********/

        return response()->json([
            'status' => in_array($resolveCode, [1, 2]) ? 'ok' : 'error',
            'message' => [
                'The custom link not available.',
                'The custom link is updated.',
                'The custom link is created.',
                'Custom link already existed.',
                'This custom link will not give your affiliate commission as it is already an existing link in your store. Please recreate it.',
                'The custom path cannot start with "/".',
                'Only special characters - _ /are allowed.'
                ][$resolveCode]
        ]);
    }

    /**
     * @param $id
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function loginAs($id): RedirectResponse
    {
        $user = Auth::user();
        $affiliate = Affiliate::findOrFail($id);
        $this->authorize('show-affiliate', $affiliate);
        $this->authorize('block-customer-refer-actions', $affiliate);
        Auth::guard('affiliate')->login($affiliate);

        /******** Start Mixpanel ********/
        try {
            $shop = Shop::find($affiliate->shop_id);
            dispatch(new MixpanelAnalyticsJob('track-event', $affiliate->shop_id, [
                'Screen size' => !empty($_COOKIE['screenSize']) ? $_COOKIE['screenSize'] : null,
                'Operation system' => !empty($_COOKIE['operationSystem']) ? $_COOKIE['operationSystem'] : null,
                'Affiliate email' => $affiliate->email ?? null,
                'Program id' => $affiliate->program->id ?? null,
            ], [
                'shop' => $shop,
                'event_name' => 'Login as affiliate',
                'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                'params' => !empty(request()->all()) ? request()->all() : null,
            ], session()->has('sudo')));
        } catch (Exception $exception) {
            logger('AffiliateController loginAs' . $exception->getMessage());
        }
        /******** End Mixpanel ********/

        session(['merchant_login_as_affiliate' => true]);
        return redirect()->route('aff.dashboard', ['domain' => $user->subdomain]);
    }

    public function bulkAction(Request $request)
    {
        $affiliateIds = $request->get('ids');
        if (empty($affiliateIds)) {
            return response()->json([
                'status' => 'ok',
                'message' => 'bulk success',
            ]);
        }
        if ($request->has('t')) {
            if ($request->get('t') == 'delete') {
                foreach ($affiliateIds as $affId) {
                    $this->destroy($affId);
                }
                return response()->json([
                    'status' => 'ok',
                    'message' => 'bulk delete success',
                ]);
            } else {
                foreach ($affiliateIds as $affId) {
                    $this->active($affId, $request);
                }

                /******** Start Mixpanel Deactivate Aff ********/
                $this->mixpanelDeactivateAff($request, $affiliateIds);
            }
        }

        return response()->json([
            'status' => 'ok',
            'message' => 'bulk success',
        ]);
    }

    public function mixpanelDeactivateAff($request, $affIds)
    {
        try {
            $user = auth()->user();
            $affiliates = Affiliate::select('affiliates.email', 'affiliates.register_method', 'programs.name as program_name')->join('programs', 'programs.id', 'affiliates.program_id')->where('affiliates.shop_id', $user->shop_id)->whereIn('affiliates.id', $affIds)->get()->toArray();
            if (isset($affiliates)) {
                $emails = array_map(function ($item) {
                    return $item['email'];
                }, $affiliates);
                $registerMethod = array_map(function ($item) {
                    switch ($item['register_method']) {
                        case 0:
                            $item['register_method'] = 'Registration form';
                            break;
                        case 1:
                            $item['register_method'] = 'Manually added';
                            break;
                        case 2:
                            $item['register_method'] = 'Manually imported';
                            break;
                        case 3:
                            $item['register_method'] = 'Post-purchase popup';
                            break;
                        case 4:
                            $item['register_method'] = 'Signed up customer';
                            break;
                        case 5:
                            $item['register_method'] = 'Customer Referral';
                            break;
                    };

                    return $item['register_method'];
                }, $affiliates);
                $programName = array_map(function ($item) {
                    return $item['program_name'];
                }, $affiliates);
                dispatch(new MixpanelAnalyticsJob('track-event', $user->shop_id, [
                    'Current page' => 'Affiliates',
                    'Affiliate email' => implode(', ', $emails),
                    'Program Name' => implode(', ', $programName),
                    'Signup source' => implode(', ', $registerMethod),
                ], [
                    'shop' => $user->shop,
                    'event_name' => 'Deactivate affiliate',
                    'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                    'params' => !empty(request()->all()) ? request()->all() : null,
                ], session()->has('sudo')));
            }
        } catch (Exception $exception) {
            logger('mixpanelDeactivateAff AffiliateController - ' . $exception->getMessage());
        }
    }

    public function manualDeActiveAff($aff)
    {
        try {
            dispatch(new MixpanelAnalyticsJob('track-event', $aff->shop_id, [
                'Current page' => 'Affiliates',
                'Affiliate email' => $aff['email'],
                'Program Name' => $aff->program->name,
                'Signup source' => registerMethodAff($aff['register_method']),
            ], [
                'shop' => $aff->shop,
                'event_name' => 'Deactivate affiliate',
                'useragent' => !empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null,
                'params' => !empty(request()->all()) ? request()->all() : null,
            ], session()->has('sudo')));
        } catch (Exception $exception) {
            logger('mixpanelDeactivateAff AffiliateController - ' . $exception->getMessage());
        }
    }

    /**
     * @param $id
     * @param $shopId
     * @param Request $request
     * @return RedirectResponse
     */
    public function loginAsBySmall($id, $shopId, Request $request): RedirectResponse
    {
        $subdomain = $request->input('subdomain');
        $affiliate = Affiliate::findOrFail($id);
        if (md5($affiliate->shop_id) === $shopId) {
            Auth::guard('affiliate')->login($affiliate);
        }
        return redirect()->route('aff.dashboard', ['domain' => $subdomain]);
    }


    public function bulkMoveAffiliateToProgram(Request $request): JsonResponse
    {
        $shopId = Auth::user()->shop_id;

        $programDestination = $request->get('toProgram');
        $affIds = $request->get('ids');

        $affMove = Affiliate::whereIn('id', $affIds)->whereShopId($shopId);

        // get customer referral program
        /** @var ProgramService $programService */
        $programService = app(ProgramService::class);
        $customerReferralProgram = $programService->programRepository->getReferCustomerProgram($shopId);
        if ($customerReferralProgram) {
            $affMove = $affMove->where('program_id', '!=', $customerReferralProgram->id);
        }

        $affMoveCount = $affMove->count();
        $affMove->update(['program_id' => $programDestination]);
        $shop = Shop::find($shopId);
        if ($shop) {
            $shopName = shopNameFromDomain($shop->shop);
            ClickTracking::clearByShopName($shopName);
            AffiliateCouponCaching::clearByShopName($shopName);
        };

        return response()->json([
            'status' => 'ok',
            'message' => 'bulk move success',
            'data' => [
                'total_affiliate_moved' => $affMoveCount,
                'total_affiliate_not_move' => count($affIds) - $affMoveCount
            ]
        ]);
    }
}

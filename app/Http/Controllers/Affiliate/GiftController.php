<?php /** @noinspection PhpUnusedParameterInspection */

namespace App\Http\Controllers\Affiliate;

use App\Libs\Currency\CurrencyFeature;
use App\Models\Gift;
use App\Models\GiftAffiliate;
use App\Models\GiftProduct;
use App\Models\MerchantSetting;
use App\Models\OrderTags;
use App\Models\ProgramsSetting;
use App\Services\GiftService\GiftService;
use App\Services\ShopifyResource\ProductService\ProductService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Secomapp\Models\Shop;
use Secomapp\ShopifyGraphql\Exceptions\ShopifyGraphqlException;
use Secomapp\ShopifyGraphql\GraphqlClient;
use Secomapp\ShopifyGraphql\ShopifyGraphql;
use Throwable;
use Ya<PERSON>ra\DataTables\DataTables;

class GiftController extends Controller
{
    public function __construct()
    {
        $this->middleware(['affiliate_auth', 'match_affiliate_subdomain']);
    }

    public function index($domain)
    {
        $user = Auth::guard('affiliate')->user();
        $merchantSettings = MerchantSetting::query()->where('shop_id', $user->shop_id)->select('sending_email')->first();
        $programsSetting = ProgramsSetting::query()->where('program_id', $user->program_id)->select('affiliate_guide')->first();
        $guideContent = str_replace(['{first_name}', '{merchant_email}'], [$user->first_name, $merchantSettings->sending_email], $programsSetting->affiliate_guide);

        if (shopSetting($user->shop_id, CurrencyFeature::DETECT_CURRENCY_CHANGE_SETTING_KEY)) {
            $latesCurrency = CurrencyFeature::latestCurrency($user->shop_id);
            if ($latesCurrency) {
                $showBannerGiftCurrency = GiftAffiliate::query()->select('gift_affiliates.id')
                    ->whereAffiliateId($user->id)
                    ->whereIn('gift_affiliates.status', [1, 2])
                    ->whereHas('gift', function ($query) use ($latesCurrency) {
                        $query->where('gifts.created_at', '<=', $latesCurrency->created_at);
                    })
                    ->first();
            }
        }

        return view('affiliate.pages.gift.index', [
            'domain' => $domain,
            'aff' => $user,
            'guideContent' => $guideContent,
            'showBannerGiftCurrency' => isset($showBannerGiftCurrency) && $showBannerGiftCurrency
        ]);
    }

    public function getDataTable(Request $request)
    {
        $user = Auth::guard('affiliate')->user();
        $gifts = GiftAffiliate::whereAffiliateId($user->id)->whereIn('status',
            [1, 2])->with('gift')->with('gift.giftProducts');

        if ($request->from && $request->to) {
            $from = Carbon::createFromTimestamp(intval($request->from))->toDateTimeString();
            $to = Carbon::createFromTimestamp(intval($request->to))->toDateTimeString();

            $gifts = $gifts->whereBetween('created_at', [$from, $to]);
        }

        $gifts = $gifts->get();

        return DataTables::of($gifts)
            ->addColumn('programs', function ($con) {
                return $con->programs;
            })->addColumn('image', function ($item) {
                return $item->image_product;
            })->addColumn('gif_value', function ($product) {
                return $product->gift_value;
            })->addColumn('claimed_product', function ($giftHistory) {
                if ($giftHistory->status == 2) {
                    return $giftHistory->claim_product;
                } else {
                    return [];
                }

            })->make();
    }

    /**
     * @param $domain
     * @param $id
     * @param  GiftService  $giftService
     * @return JsonResponse
     * @throws Throwable
     */
    public function showAffiliateGift($domain, $id, GiftService $giftService): JsonResponse
    {
        $user = auth('affiliate')->user();

        /** @var GiftAffiliate $giftAffiliate */
        $giftAffiliate = GiftAffiliate::query()
            ->where('affiliate_id', $user->id)
            ->where('id', $id)
            ->firstOrFail();

        $listProduct = $giftAffiliate->giftProducts()->with('gift')->get()->toArray();

        $result = [
            'status' => true,
            'listProductView' => view(
                'affiliate.components.modals.gift_claim_product_content',
                [
                    'listProduct' => $listProduct,
                    'giftAffiliate' => $giftAffiliate,
                    'giftId' => $giftAffiliate->gift,
                ]
            )->render()
        ];
        foreach ($listProduct as $pKey => $product) {
            $variantProduct = [];
            $options = $product['addition_info'];

            // Lọc các options có 'selected' là 'allow-aff-choose'
            $selectedOptions = array_filter($options, function ($option) {
                return $option['selected'] === 'allow-aff-choose';
            });

            // Nếu có option với 'allow-aff-choose', thực hiện call Shopify API
            if (count($selectedOptions) > 0) {

                $shopifyResponse = $giftService->fetchShopifyVariants($user->shop, $product['product_id'],
                    $selectedOptions);

                // Xử lý và thêm dữ liệu biến thể vào kết quả
                if ($shopifyResponse) {
                    $variantProduct[] = [
                        'product_id' => $product['product_id'],
                        'product_name' => $product['product_name'],
                        'variants' => $shopifyResponse
                    ];
                }
            }

            $listProduct[$pKey]['variantProduct'] = $variantProduct;
        }

        $result['listProduct'] = $listProduct;

        return response()->json($result, 200, []);
    }


    /**
     * @param  Request  $request
     * @param $domain
     * @param $id
     * @param  ProductService  $service
     * @return JsonResponse
     * @throws ShopifyGraphqlException
     * @throws Throwable
     */
    public function storeAffiliateOrder(
        Request $request,
        $domain,
        $id,
        ProductService $service
    ): JsonResponse {
        $selectedOptionFail = [];
        $user = auth('affiliate')->user();
        $shop = $user->shop;

        $listSelectedProducts = $request['select-product'];
        $listSelectedQuantity = $request['quantity'];
        $listSelectedProductOptions = $request['option'];

        if (!isset($listSelectedProductOptions)) {
            return response()->json([
                'status' => false,
                'message' => 'No products selected'
            ], 400);
        }

        /** @var GiftAffiliate $giftAff */
        $giftAff = GiftAffiliate::query()
            ->where('id', $id)
            ->where('affiliate_id', $user->id)
            ->where('gift_id', $request->gift_id)
            ->where('status', Gift::SENDING)
            ->firstOrFail();

        $giftProducts = $giftAff
            ->giftProducts()
            ->get()
            ->keyBy('id');

        foreach ($listSelectedQuantity as $productId => $quantity) {
            if (!$giftProducts->has($productId)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Product ID is not available for this gift'
                ], 400);
            }

            $dbProduct = $giftProducts->get($productId);
            if ($quantity > $dbProduct->quantity) {
                return response()->json([
                    'status' => false,
                    'message' => 'Requested quantity for product ID exceeds available quantity'
                ], 400);
            }
        }

        $giftId = $giftAff->gift;

        if ($giftId->max_number_products < count($listSelectedProducts)) {
            return response()->json([
                'status' => false,
                'message' => 'Number of products selected exceeds the maximum number of products allowed'
            ], 400);
        }

        $merchantSetting = MerchantSetting::select('sending_email')
            ->where('shop_id', $giftId->shop_id)
            ->first();

        $variantIds = [];
        foreach ($listSelectedProductOptions as $key => $option) {
            if (isset($listSelectedProducts[$key])) {
                $productId = $listSelectedProducts[$key];
                $targetVariant = $service->getVariantBySelectedOptions($productId, $option, $shop);
                if ($targetVariant) {
                    if (!isset($variantIds[$targetVariant->id])) {
                        $variantIds[$targetVariant->id] = [
                            'id' => $targetVariant->id,
                            'quantity' => $listSelectedQuantity[$key],
                            'options' => $option
                        ];
                    } else {
                        $variantIds[$targetVariant->id]['quantity'] += $listSelectedQuantity[$key];
                    }
                } else {
                    $selectOption = implode(', ', $option);
                    if ($request['alow-chooose'] == 1) {
                        $selectedOptionFail[] = [
                            'vIndex' => '',
                            'message' => 'This variant option: '.$selectOption.' was deleted/out of stock. Please contact the merchant for more details.'

                        ];
                    } else {
                        $selectedOptionFail[] = [
                            'vIndex' => '',
                            'message' => 'This variant option: '.$selectOption.' was deleted/out of stock. Please select another one.'
                        ];
                    }
                }
            }
        }

            // Tạo draft order
            $giftAmount = json_decode($giftId->gift_amounts);
            $valueType = ($giftId->type == 1) ? 'PERCENTAGE' : ($giftAmount->type == 0 ? 'PERCENTAGE' : 'FIXED_AMOUNT');
            $value = ($giftId->type == 1) ? 100 : $giftAmount->amount;
            $currencyCode = $shop->info->currency;
            $items = [];

            $shippingFee = ($giftId->include_shipping_fee != 1) ? '
            shippingLine: {
                title: "FREE SHIPPING",
                priceWithCurrency: {
                    amount:0,
                    currencyCode : ' . $currencyCode . '
                }
            }' : '';

        foreach ($variantIds as $vIndex => $variant) {
            if (!$service->checkQuantityVariantProduct($variant['id'], $variant['quantity'], $shop->id)) {
                $optionSelected = implode(' ', $variant['options']);
                if ($request['alow-chooose'] == 1) {
                    $selectedOptionFail[] = [
                        'vIndex' => $vIndex,
                        'message' => 'This variant option: '.$optionSelected.' is out of stock. Please select another one.'

                    ];
                } else {
                    $selectedOptionFail[] = [
                        'vIndex' => $vIndex,
                        'message' => 'This variant option: '.$optionSelected.' is out of stock. Please contact the merchant for more details. '
                    ];
                }
            }
            $items[] = '{
                    variantId: "gid://shopify/ProductVariant/'.$variant['id'].'",
                    quantity: '.$variant['quantity'].',
                    appliedDiscount: {
                        valueType: '.$valueType.',
                        value: '.$value.'
                    }
                }';
        }

        if (!empty($selectedOptionFail)) {
            return response()->json([
                'status' => false,
                'message' => 'success',
                'failInfo' => $selectedOptionFail
            ], 200);
        }

        $dataOrder = 'mutation draftOrderCreate {
                draftOrderCreate(input: {
                    '.$shippingFee.',
                    lineItems: [
                        '.implode(',', $items).'
                    ],
                    tags: "uppromote_gift",
                }) {
                    draftOrder {
                        id
                        order {
                            id
                        }
                        invoiceUrl
                        totalPrice
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }';

        $client = new GraphqlClient(shopNameFromDomain($shop->shop), $shop->access_token);
        $graphShopify = new ShopifyGraphql($client);
        $giftProduct = $graphShopify->send($dataOrder);

        if (empty($giftProduct->data->draftOrderCreate->draftOrder)
            && isset($giftProduct->data->draftOrderCreate->userErrors[0])) {
            return response()->json([
                'status' => false,
                'message' => $giftProduct->data->draftOrderCreate->userErrors[0]->message.' Please contact your merchant at '.$merchantSetting->sending_email.' to resolve this.',
            ], 500);
        }

        $data = [
            'product_id' => array_values($listSelectedProducts),
            'variant_id' => $variantIds,
            'draft_link' => $giftProduct->data->draftOrderCreate->draftOrder->invoiceUrl,
            'amount' => (float) $request['subtotal-claim'] ?? 0,
            'status' => Gift::CLAIM,
            'draft_order_id' => $giftProduct->data->draftOrderCreate->draftOrder->id,
        ];

        $giftAff->update($data);

        return response()->json([
            'status' => 'ok',
            'message' => 'success',
            'data' => $data,
            'contact' => $merchantSetting
        ]);
    }

    public function getLinkOrder($domain, $id): JsonResponse
    {
        $user = auth('affiliate')->user();
        $data = GiftAffiliate::query()
            ->select('gift_affiliates.gift_id', 'gift_affiliates.draft_link', 'gifts.name')
            ->join('gifts', 'gifts.id', '=', 'gift_affiliates.gift_id')
            ->where([
                'gifts.shop_id' => $user->shop_id,
                'gift_affiliates.id' => $id,
                'gift_affiliates.status' => Gift::CLAIM,
                'gift_affiliates.affiliate_id' => $user->id
            ])
            ->firstOrFail();

        $merchantSetting = MerchantSetting::select('sending_email')
            ->where('shop_id', $user->shop_id)
            ->first();

        return response()->json([
            'status' => 'ok',
            'message' => 'success',
            'data' => $data,
            'contact' => $merchantSetting
        ]);
    }

    public function addOrderTag($merchant, $orderId)
    {
        try {
            $shop = Shop::where('id', $merchant->shop_id)->first();

            if ($shop) {
                $this->clientApi->setShopName($merchant->shop_name);
                $this->clientApi->setAccessToken($shop->access_token);
                $order = new Order($this->clientApi);
                $orderInfo = $order->get($orderId);

                if ($orderInfo) {
                    // Replace variables
                    $tag = 'uppromote_gift';

                    $order->update($orderId, [
                        'id' => $orderId,
                        'tag' => $tag
                    ]);
                }
            }
        } catch (Exception $e) {
            report($e);
        }

    }
}


<?php

namespace App\Http\Controllers\Affiliate;

use App\Constant\SynchronousModelType;
use App\Http\Requests\Affiliate\Setting\UpdatePaymentMethodNotOTPRequest;
use App\Http\Requests\Affiliate\Setting\UpdatePaymentMethodRequest;
use App\Jobs\TransferProjectJob;
use App\Models\EncryptableBaseModel\EncryptableBaseModel;
use App\Models\TaxYearly;
use App\Services\Affiliate\AffiliateSettingService\AffiliateSettingService;
use App\Services\TaxYearlyService\TaxYearlyService;
use App\Services\WisePayout;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Session;
use App\Http\Requests\Affiliate\UploadW9FormRequest;
use App\Jobs\SendW9MailQueue;
use App\Models\Affiliate;
use App\Models\Payment;
use App\Models\ProgramsSetting;
use App\Services\AffiliateService\AffiliateService;
use Exception;
use Illuminate\Contracts\View\Factory;
use Illuminate\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\MerchantSetting;
use App\Models\AffiliateSetting;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\View\View;
use Secomapp\Models\Shop;
use Secomapp\Models\ShopInfo;
use Symfony\Component\HttpFoundation\BinaryFileResponse;


class SettingController extends Controller
{
    public $taxYearlyService;

    public function __construct(
        TaxYearlyService $taxYearlyService
    ) {
        $this->taxYearlyService = $taxYearlyService;
        $this->middleware(['affiliate_auth', 'match_affiliate_subdomain']);
    }

    private static function checkEnablePostbackUrl($shop_id, $enable_postback_url = -1)
    {
        //neu $enable_postback_url da duoc tinh thi ko can select lay lai
        if ($enable_postback_url == -1) {
            $res = MerchantSetting::where('shop_id', $shop_id)
                ->select('enable_postback_url')
                ->first();
            $enable_postback_url = $res->enable_postback_url;
        }
        $shop = Shop::find($shop_id);
        return $enable_postback_url && helperPlan()->planProfessionalUp($shop);
    }

    /**
     * @param $domain
     * @return Factory|Application|View
     */
    public function getPayment($domain)
    {
        $user = Auth::guard('affiliate')->user();
        $setting = $user->setting()->first();
        if (!$setting) {
            $setting = new AffiliateSetting;
            $setting->affiliate_id = $user->id;
            $setting->save();
        }
        $paymentTexts = config('myconfig.app.payment_text');
        $merchantSetting = MerchantSetting::query()
            ->where('shop_id', $user->shop_id)
            ->select(
                'shop_id',
                'payment_support',
                'other_payment_method_name',
                'other_payment_method_description',
                'payment_program',
                'enable_postback_url',
                'require_w9_form',
                'tax_option',
                'tax_type',
                'tax_value',
                'wise_integration'
            )
            ->first();
        if (!$merchantSetting->payment_support) {
            $merchantSetting->payment_support = config('myconfig.app.payment_support');
        }

        $paymentSupport = Payment::getPaymentsByProgram(
            $user->program_id,
            $merchantSetting->payment_support,
            $merchantSetting->payment_program
        );
        $enable_postback_url = self::checkEnablePostbackUrl($user->shop_id, $merchantSetting->enable_postback_url);

        if (helperPlan()->isPlanFree() && array_key_exists('store_credit', $paymentSupport)) {
            unset($paymentSupport['store_credit']);
        }

        $paymentInfo = (array) $setting->payment_info;

        if ($setting->payment_method == 'paytm' && !isset($paymentInfo['number'])) {
            $paymentInfo['number'] = $paymentInfo['mobile'] ?? '';
        }

        /** @var AffiliateSettingService $affiliateSettingService */
        $affiliateSettingService = app(AffiliateSettingService::class);
        $view = $affiliateSettingService->checkShopSavePaymentMethodNotOTP() ? 'affiliate.pages.setting.payment_not_otp' : 'affiliate.pages.setting.payment';
        $currencies = config('myconfig.currency.currencies');
        $wiseCurrencies = config('myconfig.currency.wise_currencies');
        $shopInfo = ShopInfo::where('shop_id',$user->shop_id)->select('currency')->first();

        // Check if merchant can use Wise
        $canUseWise = $this->canMerchantUseWise($user->shop_id, $merchantSetting);

        return view($view, [
            'domain' => $domain,
            'setting' => $setting,
            'currencyShopInfo' => $shopInfo->currency,
            'paymentInfo' => $paymentInfo,
            'paymentSupport' => $paymentSupport,
            'paymentTexts' => $paymentTexts,
            'merchantSetting' => $merchantSetting,
            'enable_postback_url' => $enable_postback_url,
            'tax_value' => $user->tax_value,
            'affiliate' => $user,
            'currencies' => $currencies,
            'wiseCurrencies' => $wiseCurrencies,
            'canUseWise' => $canUseWise,
            'canUseTax' => helperPlan()->planProfessionalUp() && ($merchantSetting->tax_type == MerchantSetting::TAX_VALUE_BY_AFFILIATE) && (in_array($merchantSetting->tax_option, [MerchantSetting::TAX_OPTION_MERCHANT_PAYS, MerchantSetting::TAX_OPTION_AFFILIATE_PAY]))
        ]);
    }

    /**
     * @param UpdatePaymentMethodRequest $request
     * @param $domain
     * @param $id
     * @return RedirectResponse
     */
    public function updatePayment(UpdatePaymentMethodRequest $request, $domain, $id)
    {
        $user = Auth::guard('affiliate')->user();
        $inputs = $request->validated();
        return $this->savePaymentAffiliateInfo($user, $id, $inputs);
    }

    /**
     * @param UpdatePaymentMethodNotOTPRequest $request
     * @param $domain
     * @param $id
     * @return RedirectResponse
     */
    public function updatePaymentNotOTP(UpdatePaymentMethodNotOTPRequest $request, $domain, $id)
    {
        $user = Auth::guard('affiliate')->user();
        $inputs = $request->validated();
        return $this->savePaymentAffiliateInfo($user, $id, $inputs);
    }

    public function savePaymentAffiliateInfo($user, $id, $inputs) {
        $shopId = $user->shop_id;

        if (isset($inputs['tax_percent'])) {
            $merchantSetting = MerchantSetting::query()
                ->where('shop_id', $user->shop_id)
                ->select('tax_option', 'tax_type')
                ->first();
            $canUseTax = helperPlan()->planProfessionalUp()
                && ($merchantSetting->tax_type == MerchantSetting::TAX_VALUE_BY_AFFILIATE)
                && (in_array($merchantSetting->tax_option, [MerchantSetting::TAX_OPTION_MERCHANT_PAYS, MerchantSetting::TAX_OPTION_AFFILIATE_PAY]));

            if ($canUseTax) {
                Affiliate::query()->where([
                    'id' => $user->id,
                    'shop_id' => $shopId
                ])->update(['tax_value' => $inputs['tax_percent']]);
            }
        }

        // Check if this is Wise form (dynamic fields from Wise API)
        $isWiseForm = $this->isWiseForm($inputs);

        if ($isWiseForm) {
            // This is Wise form - handle Wise recipient account creation
            return $this->handleWiseFormSubmission($user, $id, $inputs);
        }

        // Handle old form submission
        $setting = AffiliateSetting::query()
            ->where([
                'id' => $id,
                'affiliate_id' => $user->id
            ])
            ->firstOrFail();

        $paymentMethod = $inputs['payment_method'];
        unset($inputs['payment_method'], $inputs['tax_percent'], $inputs['_token']);
        $setting->payment_method = $paymentMethod != 'none' ? $paymentMethod : null;
        $setting->payment_info = $paymentMethod != 'none' ? $inputs : null;
        $setting->save();

        dispatchSyncDataSignal(
            SynchronousModelType::AFFILIATE_PAYMENT_DETAIL,
            SynchronousModelType::AFFILIATE_PAYMENT_DETAIL_ACTION['ON_SET']
        )
            ->setArguments($user->id)
            ->setIdentity($shopId)
            ->dispatch();

        Session::flash('ok', sca_trans($shopId, 'payment_updated', [], session('affiliate_locale')));
        return back()->withInput();
    }

    /**
     * Check if the submitted form is Wise form (dynamic fields from Wise API)
     * Wise form is detected by having currency field and NOT having old bank form fields
     */
    private function isWiseForm($inputs) {
        if (!isset($inputs['currency']) || empty($inputs['currency'])) {
            return false;
        }

        $oldFormFields = ['account_type', 'name', 'account', 'number', 'branch', 'swift'];
        $hasOldFields = false;

        foreach ($oldFormFields as $field) {
            if (isset($inputs[$field]) && !empty($inputs[$field])) {
                $hasOldFields = true;
                break;
            }
        }
        return !$hasOldFields;
    }

    /**
     * Handle Wise form submission - create recipient account and save to affiliate_setting
     */
    private function handleWiseFormSubmission($user, $id, $inputs) {
        try {
            // Check if merchant can use Wise
            $merchantSetting = MerchantSetting::query()
                ->where('shop_id', $user->shop_id)
                ->select('wise_integration')
                ->first();

            if (!$this->canMerchantUseWise($user->shop_id, $merchantSetting)) {
                return back()->withErrors([
                    'wise_error' => 'Your account does not have access to Wise integration. Please upgrade your plan or contact support.'
                ])->withInput();
            }

            // Check if currency is supported by Wise
            $wiseCurrencies = config('myconfig.currency.wise_currencies', []);
            $selectedCurrency = $inputs['currency'] ?? '';

            if (!in_array($selectedCurrency, $wiseCurrencies)) {
                return back()->withErrors([
                    'wise_error' => 'Selected currency is not supported by Wise integration.'
                ])->withInput();
            }

            $wiseFormData = $this->prepareWiseFormData($user, $inputs);

            $wisePayoutService = app(WisePayout::class);
            $wiseResponse = $wisePayoutService->createRecipientAccount($wiseFormData);

            if (isset($wiseResponse['error']) || !isset($wiseResponse['profile_id'])) {
                // Wise API returned error - redirect back with validation errors
                return back()->withErrors([
                    'wise_error' => $wiseResponse['error'] ?? 'Failed to create Wise recipient account. Please check your bank account information.'
                ])->withInput();
            }

            // Success - save to affiliate_setting table
            $setting = AffiliateSetting::query()
                ->where([
                    'id' => $id,
                    'affiliate_id' => $user->id
                ])
                ->firstOrFail();

            $wiseSettingData = [
                'profile_id' => $wiseResponse['profile_id'],
                'form' => $this->formatWiseFormForStorage($inputs)
            ];

            $setting->payment_method = 'bank';
            $setting->payment_info = $wiseSettingData;
            $setting->save();

            dispatchSyncDataSignal(
                SynchronousModelType::AFFILIATE_PAYMENT_DETAIL,
                SynchronousModelType::AFFILIATE_PAYMENT_DETAIL_ACTION['ON_SET']
            )
                ->setArguments($user->id)
                ->setIdentity($user->shop_id)
                ->dispatch();

            Session::flash('ok', sca_trans($user->shop_id, 'payment_updated', [], session('affiliate_locale')));
            return back()->withInput();

        } catch (\Exception $e) {
            logger()->error("Wise recipient account creation failed: {$e->getMessage()}");
            return back()->withErrors([
                'wise_error' => 'Failed to create Wise recipient account. Please try again or contact support.'
            ])->withInput();
        }
    }

    /**
     * Prepare data for Wise API call
     */
    private function prepareWiseFormData($user, $inputs) {
        return [
            'affiliate_id' => $user->id,
            'shop_id' => $user->shop_id,
            'currency' => $inputs['currency'] ?? null,
            'form_data' => $inputs
        ];
    }

    /**
     * Format Wise form data for storage in affiliate_setting table
     * Format: [{"key": "type", "label": "Recipient type", "value": "PRIVATE", "name": "Person"}, ...]
     */
    private function formatWiseFormForStorage($inputs) {
        $formData = [];

        // Common field mappings for known Wise fields
        $fieldMappings = [
            'legalType' => [
                'label' => 'Recipient type',
                'options' => [
                    'PRIVATE' => 'Person',
                    'BUSINESS' => 'Business'
                ]
            ],
            'accountHolderName' => ['label' => 'Account holder name'],
            'IBAN' => ['label' => 'IBAN'],
            'email' => ['label' => 'Email address'],
            'currency' => ['label' => 'Currency'],
            'accountNumber' => ['label' => 'Account number'],
            'routingNumber' => ['label' => 'Routing number'],
            'abartn' => ['label' => 'ABA routing number'],
            'accountType' => [
                'label' => 'Account type',
                'options' => [
                    'CHECKING' => 'Checking',
                    'SAVINGS' => 'Savings'
                ]
            ],
            'address.country' => ['label' => 'Country'],
            'address.city' => ['label' => 'City'],
            'address.postCode' => ['label' => 'Postal code'],
            'address.firstLine' => ['label' => 'Address line 1'],
            'address.stateCode' => ['label' => 'State'],
        ];

        foreach ($inputs as $key => $value) {
            if (in_array($key, ['payment_method', 'tax_percent', '_token']) || empty($value)) {
                continue;
            }

            // Get field mapping
            $mapping = $fieldMappings[$key] ?? null;
            $label = $mapping['label'] ?? ucfirst(str_replace(['_', '.'], [' ', ' '], $key));

            // Get display name for select/radio options
            $name = '';
            if ($mapping && isset($mapping['options']) && isset($mapping['options'][$value])) {
                $name = $mapping['options'][$value];
            }

            $formData[] = [
                'key' => $key,
                'label' => $label,
                'value' => $value,
                'name' => $name
            ];
        }

        return $formData;
    }

    /**
     * Check if merchant can use Wise integration
     * Conditions: Plan Professional+, Wise integration enabled
     */
    private function canMerchantUseWise($shopId, $merchantSetting): bool
    {
        $hasProfessionalPlan = helperPlan()->planProfessionalUp(shopFromId($shopId));

        $hasWiseIntegration = isset($merchantSetting->wise_integration) && $merchantSetting->wise_integration == 1;

        return $hasProfessionalPlan && $hasWiseIntegration;
    }

    public function getProfile(Request $request, $domain)
    {
        $user = Auth::guard('affiliate')->user();
        $countries = config('myconfig.countries');
        $enable_postback_url = self::checkEnablePostbackUrl($user->shop_id);
        $merchantSetting = MerchantSetting::where('shop_id', $user->shop_id)->select([
            'enable_fb_pixel', 'require_w9_form'
        ])->first();
        $programSetting = ProgramsSetting::whereShopId($user->shop_id)
            ->whereProgramId($user->program_id)
            ->first();
        $affiliateSignUpFields = [];
        if ($programSetting) {
            if ($programSetting->affiliate_registration) {
                if (isset($programSetting->affiliate_registration['signup_fields'])) {
                    $affiliateSignUpFields = is_string($programSetting->affiliate_registration['signup_fields']) ?
                        json_decode($programSetting->affiliate_registration['signup_fields']) :
                        $programSetting->affiliate_registration['signup_fields'];
                }
            }
        }
        $view = 'affiliate.pages.setting.profile';
        if ($user->shop_id == 12737) {
            $view = 'affiliate.pages.setting.12737_profile';
        }
        $user->registerMethodToText();
        return view($view, [
            'domain' => $domain,
            'aff' => $user,
            'countries' => $countries,
            'enable_postback_url' => $enable_postback_url,
            'merchantSetting' => $merchantSetting,
            'affiliateSignUpFields' => is_array($affiliateSignUpFields) ? json_decode(json_encode($affiliateSignUpFields),
                false) : $affiliateSignUpFields
        ]);
    }

    /**
     * Get notification page
     *
     * @param  Request  $request
     * @param $domain
     * @return Factory|View
     */
    public function getNotification(Request $request, $domain)
    {
        $user = Auth::guard('affiliate')->user();
        $enable_postback_url = self::checkEnablePostbackUrl($user->shop_id);
        $merchantSetting = MerchantSetting::where('shop_id', $user->shop_id)->select([
            'enable_fb_pixel', 'require_w9_form'
        ])->first();
        return view('affiliate.pages.setting.notification', [
            'domain' => $domain,
            'aff' => $user,
            'enable_postback_url' => $enable_postback_url,
            'merchantSetting' => $merchantSetting
        ]);
    }

    /**
     * Save notification setting
     *
     * @param  Request  $request
     * @return RedirectResponse
     */
    public function updateNotification(Request $request)
    {
        $inputs = $request->all();
        $inputs['receive_notification'] = isset($inputs['receive_notification']) ? 1 : 0;

        $user = Auth::guard('affiliate')->user();
        $user->receive_notification = $inputs['receive_notification'];
        $user->save();
        /** @var AffiliateService $affiliateService */
        $affiliateService = app(AffiliateService::class);
        $affiliateService->syncMultipleShop(
            auth('affiliate')->user(),
            'notification',
            $inputs
        );

        Session::flash('ok',
            sca_trans($user->shop_id, 'notification_setting_updated', [], session('affiliate_locale')));

        return back()->withInput();
    }

    public function postbackUrl(Request $request, $domain)
    {
        $user = Auth::guard('affiliate')->user();
        $merchantSetting = MerchantSetting::where('shop_id', $user->shop_id)->select([
            'enable_fb_pixel', 'require_w9_form'
        ])->first();
        $enable_postback_url = self::checkEnablePostbackUrl($user->shop_id);
        return view('affiliate.pages.setting.postback_url', [
            'domain' => $domain,
            'aff' => $user,
            'enable_postback_url' => $enable_postback_url,
            'merchantSetting' => $merchantSetting
        ]);
    }

    public function updatePostbackUrl(Request $request)
    {
        $inputs = $request->all();
        $inputs['postback_url'] = isset($inputs['postback_url']) ? trim($inputs['postback_url']) : '';

        $user = Auth::guard('affiliate')->user();
        $user->postback_url = $inputs['postback_url'];
        $user->save();

        Session::flash('ok', "Postback Url setting updated");

        return back()->withInput();
    }

    /**
     * @param $domain
     * @return Factory|Application|View
     */
    public function w9Form($domain)
    {
        $user = Auth::guard('affiliate')->user();
        $enable_postback_url = self::checkEnablePostbackUrl($user->shop_id);
        $merchantSetting = MerchantSetting::where('shop_id', $user->shop_id)->select([
            'enable_fb_pixel', 'require_w9_form', 'active_1099_form'
        ])->first();
        $list1099Form = TaxYearly::query()
            ->where('affiliate_id', $user->id)
            ->whereNotNull('1099_form_link_affiliate')
            ->select(['year', '1099_form_link_affiliate as form_link'])->get();
        $submitted = $user->w9_form_file ?? false;
        $info = json_decode(EncryptableBaseModel::decrypt($user->setting->info_w9_form));

        if (empty($info)) {
            $info = (object) [
                'full_name' => '',
                'business_name' => '',
                'address' => '',
                'detail_address_w9_form' => '',
                'tin_type' => '',
                'tin' => ''
            ];
        }

        return view('affiliate.pages.setting.w9_form',
            compact('merchantSetting', 'domain', 'submitted', 'enable_postback_url', 'list1099Form', 'info'));
    }

    /**
     * Download default W9 form file
     * @return BinaryFileResponse
     */
    public function downloadDefaultW9Form(): BinaryFileResponse
    {
        $file = resource_path('assets/pdf/fw9.pdf');
        return response()->download($file);
    }

    public function submitW9Form(UploadW9FormRequest $request): RedirectResponse
    {
        try {
            $file = $request->file('w9_form_file');
            $isMissingField = $this->taxYearlyService->validationW9Form($file->getRealPath());
//            if ($isMissingField) {
//                return back()->withErrors([
//                    'w9_form_file' => 'Some required information is missing. Please check your W-9 form and try again.'
//                ])->withInput();
//            }
            $user = Auth::guard('affiliate')->user();
            $shop = Shop::findOrFail($user->shop_id);
            $shopName = shopNameFromDomain($shop->shop);
            # Remove old file
            if (storage_s3()->exists($user->w9_form_file)) {
                storage_s3()->delete($user->w9_form_file);
            }
            # Upload | Replace
            $fileName = md5($user->id).'_'.time().'.pdf';
            $filePath = "/storage/uploads/w9_form/$shopName/";
            $fileFullPath = $filePath.$fileName;
            storage_s3()->makeDirectory($filePath);
            storage_s3()->put($fileFullPath, file_get_contents($file->getRealPath()), 'public');
            # Flash status
            if (storage_s3()->exists($fileFullPath)) {
                $user->w9_form_file = $fileFullPath;
                $user->is_miss_field_w9_form = 0;
                $user->save();
                AffiliateSetting::query()->where('affiliate_id', $user->id)->update([
                    'info_w9_form' => EncryptableBaseModel::encrypt(json_encode([
                        'full_name' => $request->full_name,
                        'business_name' => $request->business_name,
                        'address' => $request->address,
                        'detail_address_w9_form' => $request->detail_address_w9_form,
                        'tin_type' => $request->tin_type,
                        'tin' => $request->tin,
                    ]))
                ]);
                // generate 1099 form
                dispatch(new TransferProjectJob('1099-form-generate-merchant', [
                    'shop_id' => $user->shop_id,
                    'affiliate_id' => $user->id,
                ]));

                /** @var AffiliateService $affiliateService */
                $affiliateService = app(AffiliateService::class);
                $affiliateService->syncMultipleShop(
                    auth('affiliate')->user(),
                    'w9',
                    ['w9_form_file' => $fileFullPath]
                );
                session()->flash('uploaded-w9-form', 'yes');
                $merchant = MerchantSetting::whereShopId($user->shop_id)->first();
                if ($merchant) {
                    $mail = new SendW9MailQueue($user, $merchant, $fileFullPath);
                    dispatch($mail);
                }
            }
        } catch (Exception $exception) {
            logger()->error("Submit W-9 form error: {$exception->getMessage()}");
        }
        return back();
    }

    public function affiliateChatToken()
    {
        $client = new Client();
        try {
            $user = auth()->guard('affiliate')->user();
            $client = new Client();
            $requestData = new \GuzzleHttp\Psr7\Request(
                'POST',
                config('myconfig.app.chat_embed').'/api/v1/auth/login',
                [
                    'Content-Type' => 'application/json'
                ],
                json_encode([
                    'email' => $user->email,
                    'password' => $user->password,
                    'shop_id' => $user->shop_id
                ])
            );

            return response()->json([
                'token' => $client->send($requestData)->getBody()->getContents()
            ]);
        } catch (GuzzleException $e) {
            logger()->error("Can't dispatch install app: {$e->getMessage()}");
        }
    }

    /**
     * @param UpdatePaymentMethodRequest $request
     * @param $domain
     * @return JsonResponse
     */
    public function validatePaymentMethod(UpdatePaymentMethodRequest $request, $domain) {
        return response()->json(['status' => 'success']);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function checkOTP(Request $request): JsonResponse {
        $user = Auth::guard('affiliate')->user();
        $otpRequest = $request->get('otp');
        /** @var AffiliateSettingService $affiliateSettingService */
        $affiliateSettingService = app(AffiliateSettingService::class);
        return $affiliateSettingService->checkOTP($user, $otpRequest);
    }

    /**
     * @param $domain
     * @param Request $request
     * @return JsonResponse
     */
    public function checkSendOTP($domain, Request $request): JsonResponse {
        $data = $request->all();
        $resend = (!empty($request->type)) ? 'resend' : null;
        /** @var AffiliateSettingService $affiliateSettingService */
        $affiliateSettingService = app(AffiliateSettingService::class);
        return $affiliateSettingService->processSendOTP($data, $resend);
    }
}

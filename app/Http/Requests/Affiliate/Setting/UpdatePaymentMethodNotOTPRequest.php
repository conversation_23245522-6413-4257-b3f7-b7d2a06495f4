<?php

namespace App\Http\Requests\Affiliate\Setting;

use App\Rules\PaymentMethodBlacklistRule;
use App\Rules\ValidInternationalEmail;
use App\Services\Affiliate\AffiliateSettingService\AffiliateSettingService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePaymentMethodNotOTPRequest extends FormRequest
{

    public function authorize(): bool
    {
        /** @var AffiliateSettingService $affiliateSettingService */
        $affiliateSettingService = app(AffiliateSettingService::class);
        if ($affiliateSettingService->checkShopSavePaymentMethodNotOTP()) {
            return true;
        }

        return abort(403);
    }

    public function rules(): array
    {
        $generalRule = [
            'payment_method' => [
                'required',
                Rule::in([
                    'none',
                    'paypal',
                    'bank',
                    'debit',
                    'cheque',
                    'venmo',
                    'paytm',
                    'upi',
                    'store_credit',
                    'other'
                ])
            ],
            'tax_percent' => [
                'nullable',
                'numeric'
            ]
        ];

        $paymentMethod = $this->get('payment_method');
        if ($paymentMethod == 'paypal') {
            return array_merge($generalRule, $this->paypalRule());
        }

        if ($paymentMethod == 'bank') {
            return array_merge($generalRule, $this->bankRule());
        }

        if ($paymentMethod == 'debit') {
            return array_merge($generalRule, $this->debitRule());
        }

        if ($paymentMethod == 'cheque') {
            return array_merge($generalRule, $this->chequeRule());
        }

        if ($paymentMethod == 'venmo') {
            return array_merge($generalRule, $this->venmoRule());
        }

        if ($paymentMethod == 'paytm') {
            return array_merge($generalRule, $this->paytmRule());
        }

        if ($paymentMethod == 'upi') {
            return array_merge($generalRule, $this->upiRule());
        }

        if ($paymentMethod == 'other') {
            return array_merge($generalRule, $this->otherRule());
        }

        return $generalRule;
    }

    private function paypalRule(): array
    {
        return [
            'email' => [
                'required_if:payment_method,paypal',
                new ValidInternationalEmail(),
                new PaymentMethodBlacklistRule()
            ]
        ];
    }

    private function bankRule(): array
    {
        return [
            'account_type' => [
                'nullable',
                Rule::in(['checking', 'saving'])
            ],
            'name' => [
                'nullable',
                'string',
                'min:1'
            ],
            'account' => [
                'nullable',
                'string',
                'min:1',
                new PaymentMethodBlacklistRule()
            ],
            'number' => [
                'nullable',
                'string',
                'min:1',
                new PaymentMethodBlacklistRule()
            ],
            'branch' => [
                'nullable',
                'string',
                'min:1'
            ],
            'swift' => [
                'nullable',
                'string',
                'min:1'
            ]
        ];
    }

    private function debitRule(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'min:2'
            ],
            'number' => [
                'required',
                'max:255'
            ]
        ];
    }

    private function chequeRule(): array
    {
        return [
            'payto' => [
                'required',
                'string',
                'min:3'
            ],
            'address' => [
                'required',
                'string',
                'min:5'
            ]
        ];
    }

    private function venmoRule(): array
    {
        return [
            'mobile_email' => [
                'required',
                'min:5'
            ]
        ];
    }

    private function paytmRule(): array
    {
        return [
            'number' => [
                'required',
                'min:5'
            ]
        ];
    }

    private function upiRule(): array
    {
        return [
            'id' => [
                'required',
                'min:1'
            ]
        ];
    }

    private function otherRule(): array
    {
        return [
            'other' => [
                'nullable',
                'min:1',
                'max:300',
                new PaymentMethodBlacklistRule()
            ]
        ];
    }
}

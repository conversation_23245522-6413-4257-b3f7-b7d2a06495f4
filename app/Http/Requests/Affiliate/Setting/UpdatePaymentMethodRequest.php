<?php

namespace App\Http\Requests\Affiliate\Setting;

use App\Models\AffiliateSetting;
use App\Models\Otps;
use App\Rules\PaymentMethodBlacklistRule;
use App\Rules\ValidInternationalEmail;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdatePaymentMethodRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $generalRule = [
            'payment_method' => [
                'required',
                Rule::in([
                    'none',
                    'paypal',
                    'bank',
                    'debit',
                    'cheque',
                    'venmo',
                    'paytm',
                    'upi',
                    'store_credit',
                    'other',
                    'wise'
                ]),
                function ($attribute, $value, Closure $fail) {
                    if (request()->route()->getName() === 'aff.setting.payment.validate') {
                        return true;
                    }

                    $affiliate = Auth::guard('affiliate')->user();
                    $affiliateId = $affiliate->id;
                    $affiliateSetting = AffiliateSetting::query()->where('affiliate_id', $affiliateId)->first();
                    $inputs = request()->all();

                    if (is_null($affiliateSetting->payment_method) && request()->get('payment_method') == 'none') {
                        $inputs['payment_method'] = null;
                    }

                    $taxPercentChange = isset($inputs['tax_percent']) && $inputs['tax_percent'] != $affiliate->tax_value;
                    $inputRequest['payment_method'] = $inputs['payment_method'];
                    unset($inputs['payment_method'], $inputs['tax_percent'], $inputs['_token']);
                    $inputRequest['payment_info'] = !empty($inputs) ? $inputs : $affiliateSetting->payment_info;

                    $affiliateSetting->fill($inputRequest);
                    if ($affiliateSetting->isDirty() || $taxPercentChange) {
                        $otpRequest = session('payment_setting_affiliate_otp_request');

                        if (is_null($otpRequest)) {
                            return $fail('OTP is required to update payment method');
                        } else {
                            $otp = Otps::query()->select('otp')
                                ->where([
                                    'shop_id' => $affiliate->shop_id,
                                    'affiliate_id' => $affiliateId,
                                    'type' => Otps::TYPE_OTP_SAVE_PAYMENT_AFFILIATE,
                                ])
                                ->where('created_at', '>=', now())
                                ->first();

                            if ($otp && $otp->otp == $otpRequest) {
                                return true;
                            } else {
                                return $fail('Invalid OTP. Please try again or click Resend to get a new code.');
                            }
                        }
                    } else {
                        return true;
                    }
                }
            ],
            'tax_percent' => [
                'nullable',
                'numeric'
            ]
        ];

        $paymentMethod = $this->get('payment_method');
        if ($paymentMethod == 'paypal') {
            return array_merge($generalRule, $this->paypalRule());
        }

        if ($paymentMethod == 'bank') {
            return array_merge($generalRule, $this->bankRule());
        }

        if ($paymentMethod == 'debit') {
            return array_merge($generalRule, $this->debitRule());
        }

        if ($paymentMethod == 'cheque') {
            return array_merge($generalRule, $this->chequeRule());
        }

        if ($paymentMethod == 'venmo') {
            return array_merge($generalRule, $this->venmoRule());
        }

        if ($paymentMethod == 'paytm') {
            return array_merge($generalRule, $this->paytmRule());
        }

        if ($paymentMethod == 'upi') {
            return array_merge($generalRule, $this->upiRule());
        }

        if ($paymentMethod == 'other') {
            return array_merge($generalRule, $this->otherRule());
        }

        if ($paymentMethod == 'wise') {
            return array_merge($generalRule, $this->wiseRule());
        }

        return $generalRule;
    }

    private function paypalRule(): array
    {
        return [
            'email' => [
                'required_if:payment_method,paypal',
                new ValidInternationalEmail(),
                new PaymentMethodBlacklistRule()
            ]
        ];
    }

    private function bankRule(): array
    {
        $rules =  [
            'account_type' => [
                'nullable',
                Rule::in(['checking', 'saving'])
            ],
            'name' => [
                'nullable',
                'string',
                'min:1'
            ],
            'account' => [
                'nullable',
                'string',
                'min:1',
                new PaymentMethodBlacklistRule()
            ],
            'number' => [
                'nullable',
                'string',
                'min:1',
                new PaymentMethodBlacklistRule()
            ],
            'branch' => [
                'nullable',
                'string',
                'min:1'
            ],
            'swift' => [
                'nullable',
                'string',
                'min:1'
            ]
        ];
        $requestData = $this->all();
        foreach ($requestData as $key => $value) {
            if (!in_array($key, ['_token', 'payment_method', 'tax_percent']) && !isset($rules[$key])) {
                $rules[$key] = ['nullable'];
            }
        }
        return $rules;
    }

    private function debitRule(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'min:2'
            ],
            'number' => [
                'required',
                'max:255'
            ]
        ];
    }

    private function chequeRule(): array
    {
        return [
            'payto' => [
                'required',
                'string',
                'min:3'
            ],
            'address' => [
                'required',
                'string',
                'min:5'
            ]
        ];
    }

    private function venmoRule(): array
    {
        return [
            'mobile_email' => [
                'required',
                'min:5'
            ]
        ];
    }

    private function paytmRule(): array
    {
        return [
            'number' => [
                'required',
                'min:5'
            ]
        ];
    }

    private function upiRule(): array
    {
        return [
            'id' => [
                'required',
                'min:1'
            ]
        ];
    }

    private function otherRule(): array
    {
        return [
            'other' => [
                'nullable',
                'min:1',
                'max:300',
                new PaymentMethodBlacklistRule()
            ]
        ];
    }

    private function wiseRule(): array
    {
        // Since validation is already done in JS, just allow all dynamic fields
        $rules = [];

        $requestData = $this->all();
        foreach ($requestData as $key => $value) {
            if (!in_array($key, ['_token', 'payment_method', 'tax_percent'])) {
                $rules[$key] = ['nullable']; // Allow all dynamic fields from Wise API
            }
        }

        return $rules;
    }
}

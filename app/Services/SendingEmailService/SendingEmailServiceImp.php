<?php

namespace App\Services\SendingEmailService;

use App\Repositories\MerchantSettingRepository\MerchantSettingRepository;
use App\Repositories\PendingVerifySendingEmailRepository\PendingVerifySendingEmailRepository;
use App\Services\AmazonSES;
use Aws\Ses\SesClient;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class SendingEmailServiceImp implements SendingEmailService
{
    /** @var AmazonSES $awsClient */
    private $awsClient;
    /** @var SesClient $sesClient */
    private $sesClient;
    /** @var PendingVerifySendingEmailRepository $pendingVerifyEmailRepo */
    private $pendingVerifyEmailRepo;
    /** @var MerchantSettingRepository $merchantSettingRepository */
    private $merchantSettingRepository;

    public function __construct()
    {
        try {
            $this->awsClient = new AmazonSES();
            $this->sesClient = new SesClient([
                'version' => 'latest',
                'region' => config('services.ses.region'),
                'credentials' => [
                    'key' => config('services.ses.key'),
                    'secret' => config('services.ses.secret')
                ]
            ]);
        } catch (Exception $exception) {
            logger()->warning('Amazon SES configuration is missing. (SendingEmailServiceImp.php)');
        }
        $this->pendingVerifyEmailRepo = app(PendingVerifySendingEmailRepository::class);
        $this->merchantSettingRepository = app(MerchantSettingRepository::class);

    }

    public function emailVerified(string $email): bool
    {
        $record = $this->merchantSettingRepository
            ->findByCondition([
                'sending_email' => $email,
                'use_sending_email' => 1,
                'verify_status' => 1
            ]);
        return (bool)$record;
    }

    public function deleteIdentity(string $email): bool
    {
        try {
            if (config('app.env') != 'production') return true;
            $this->awsClient->deleteIdentity($email);
            return true;
        } catch (Exception $exception) {
            report($exception);
        }
        return false;
    }

    public function sendVerifyEmail(string $sendingEmail)
    {
        try {
            $this->awsClient->sendVerifyEmail($sendingEmail);
        } catch (Exception $exception) {
            report($exception);
        }
    }

    public function getIdentityStatus(array $emails): array
    {
        $response = $this->sesClient->getIdentityVerificationAttributes([
            'Identities' => $emails
        ]);
        return $response->toArray()['VerificationAttributes'] ?? [];
    }

    public function bulkUpdateVerificationStatus(string $email, bool $status)
    {
        $this->merchantSettingRepository->query()
            ->where('sending_email', $email)
            ->update(['verify_status' => $status]);
    }

    public function stillUseSendingEmail(string $email, int $currentShopId): bool
    {
        $records = $this->merchantSettingRepository
            ->query()
            ->where([
                'sending_email' => $email,
                'use_sending_email' => 1,
                'verify_status' => 1
            ])
            ->where('shop_id', '!=', $currentShopId)
            ->first();
        return (bool)$records;
    }

    public function storeNewPendingVerifyEmail(string $email)
    {
        $mapAndAttributes = compact('email');
        $this->pendingVerifyEmailRepo->firstOrCreate($mapAndAttributes, $mapAndAttributes);
    }

    public function verificationEmailSent(string $email): bool
    {
        $record = $this->pendingVerifyEmailRepo->findByCondition(compact('email'));
        return (bool)$record;
    }

    public function deleteInvalidatePendingEmail()
    {
        $this->pendingVerifyEmailRepo->deleteInvalidateEmail();
    }

    public function getEmailQueryBuilder(): Builder
    {
        return $this->pendingVerifyEmailRepo->query();
    }

    public function deletePendingEmail(string $email)
    {
        $this->pendingVerifyEmailRepo->query()->where(compact('email'))->delete();
    }
}

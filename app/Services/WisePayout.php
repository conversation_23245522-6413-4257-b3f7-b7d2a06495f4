<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class WisePayout
{
    protected $client;
    protected $host;

    public function __construct()
    {
        $this->host = config('payout.uppromote_payout_host');
        $this->client = new Client([
            'base_uri' => $this->host,
            'verify' => app()->environment('production'),
            'timeout' => 30,
        ]);
    }

    /**
     * Sends a POST request to the specified URI with the provided body.
     * @param string $uri
     * @param array $body
     * @return array
     */

    public function post(string $uri, array $body) :array
    {
        try{
            $body['client_uuid'] = config('payout.uppromote_client_uuid');

            // Prepare headers with authentication
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . config('payout.uppromote_payout_token'),
            ];

            $res = $this->client->request('POST', $uri, [
                'json' => $body,
                'headers' => $headers
            ]);

            return json_decode($res->getBody(), true);
        }catch(Exception $e){
            report($e);
            return ['error' => $e->getMessage()];
        } catch (GuzzleException $e) {
            return ['error' => $e->getMessage()];
        }
    }
    public function sentRecipientAccount(array $body)
    {
        return $this->post('wise/sent-recipient-wise', $body);
    }
}

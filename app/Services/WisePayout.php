<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\DB;

class WisePayout
{
    protected $client;
    protected $host;

    public function __construct()
    {
        $this->host = config('payout.uppromote_payout_host');
        $this->client = new Client([
            'base_uri' => $this->host,
            'verify' => app()->environment('production'), // Only verify SSL in production
            'timeout' => 30,   // Set timeout
        ]);
    }

    /**
     * Sends a POST request to the specified URI with the provided body.
     * @param string $uri
     * @param array $body
     * @param int|null $shopId
     * @return array
     */
    public function post(string $uri, array $body, $shopId = null) :array
    {
        try{
            $body['client_uuid'] = config('payout.uppromote_client_uuid');

            // Get token from wise_integration table based on shop_id
            $token = $this->getWiseToken($shopId);
            if (!$token) {
                return ['error' => 'Wise token not found for this merchant'];
            }

            // Prepare headers with authentication
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token,
            ];

            $res = $this->client->request('POST', $uri, [
                'json' => $body,
                'headers' => $headers
            ]);

            return json_decode($res->getBody(), true);
        }catch(Exception $e){
            report($e);
            return ['error' => $e->getMessage()];
        } catch (GuzzleException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get Wise token from wise_integration table
     * @param int|null $shopId
     * @return string|null
     */
    private function getWiseToken($shopId = null)
    {
        if (!$shopId) {
            return null;
        }

        $wiseIntegration = DB::table('wise_integration')
            ->where('shop_id', $shopId)
            ->first();

        return $wiseIntegration ? $wiseIntegration->wise_token : null;
    }

    public function sentRecipientAccount(array $body, $shopId = null)
    {
        return $this->post('wise/sent-recipient-wise', $body, $shopId);
    }
}

<?php

namespace App\Services;

use GuzzleHttp\Client;
use Secomapp\Models\ShopInfo;

class SecomappPartner
{
    const APP_ID = 11;
    const UNINSTALLED = 1;
    const CHARGE = 4;
    const DOWNGRADE = 99;

    /**
     * @param $storeName
     * @param $shopPlan
     * @param $statusId
     * @param null $comment
     */
    public static function notify($storeName, $shopPlan, $statusId, $comment = null)
    {
        try {
            $client = new Client();
            $referralCode = null;
            if (isset($_COOKIE['sca_ref_code'])) {
                $referralCode = $_COOKIE['sca_ref_code'];
            }

            $request = [
                'app_id' => self::APP_ID,
                'shop_name' => $storeName,
                'log_date' =>  date('Y-m-d H:i:s'),
                'shop_plan' => $shopPlan,
                'aff_code' => $referralCode,
                'status' => $statusId,
                'comment' => $comment,
                'access_token' => 'abcd'
            ];

            $request['shop_plan'] = self::getShopPlanDisplayName($storeName);
            $client->post('https://api-partners.secomapp.com/api/log.json', [
                'form_params' => $request,
                'headers' => [
                    'domain' => 'secomapp'
                ]
            ])->getBody();
        } catch(\Exception $e) {
            report($e);
        }
    }

    /**
     * @param $storeName
     * @param $planName
     * @return string
     */
    public static function getSSOurl($storeName, $planName)
    {
        try {
            if ($storeName && $planName && session('have_just_installed')) {
                session()->remove('have_just_installed');
                $publicKey = config('myconfig.app.secomapp_partner.sso_public_key');
                $data = $storeName . '@@@' . $planName . '@@@' . self::APP_ID;
                openssl_public_encrypt($data, $encrypted, $publicKey);
                $hex_data = bin2hex($encrypted);

                return config('myconfig.app.secomapp_partner.sso_url'). '?data='. $hex_data;
            }
        } catch (\Exception $e) {
            report($e);
        }

        return '';
    }

    /**
     * Check this installation has special discount or not
     *
     * @param $shopName
     */
    public static function checkDiscount($shopName)
    {
        if (!isset($_COOKIE['sca_ref_code']) || !isset($_COOKIE['sca_ref_offer']) || $_COOKIE['sca_ref_offer'] != 'all') {
            return;
        }

        try {
            $client = new Client();
            $request = [
                'shop_name' => $shopName,
                'app_name' => 'affiliate',
                'sca_ref_code' => $_COOKIE['sca_ref_code'],
                'sca_ref_offer' =>  $_COOKIE['sca_ref_offer'],
                'access_token' => "abcd"
            ];

            if (isset($_COOKIE['sca_ref_discount']) &&
                in_array($_COOKIE['sca_ref_discount'], ['20', '30', '90', '30trials', '2030'])
            ) {
                $request['sca_ref_discount'] = $_COOKIE['sca_ref_discount'];
            }

            $request['shop_plan'] = self::getShopPlanDisplayName($shopName);
            $client->post('https://api-partners.secomapp.com/api/validate/coupon.json', [
                'form_params' => $request,
                'headers' => [
                    'domain' => 'secomapp'
                ]
            ])->getBody();
        } catch(\Exception $e) {
            report($e);
        }
    }

    /**
     * @param $shopName
     * @return mixed|string
     */
    public static function getShopPlanDisplayName($shopName)
    {
        $shopInfo = ShopInfo::query()->where('myshopify_domain', $shopName)->first();
        if ($shopInfo) {
            return $shopInfo->plan_display_name;
        }

        return '';
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncProductAnalyticsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600;
    public $tries = 2;


    private $shopId;
    private $type;
    private $order;
    private $startDay;
    private $endDay;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shopId, $type, $order = null, $startDay = null, $endDay = null
    ) {
        $this->onQueue('product-analytics');
        $this->onConnection('redis');
        $this->shopId = $shopId;
        $this->type = $type;
        $this->order = $order;
        $this->startDay = $startDay;
        $this->endDay = $endDay;
    }

    public function handle()
    {
        // Logic handle project api.uppromote.com
    }
}

<?php

namespace App\Models;

use App\Traits\LogsActivityEmailTemplateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class EmailTemplate extends Model
{
    use LogsActivityEmailTemplateTrait;

    protected $fillable = [
        'user_id',
        'shop_id',
        'name',
        'subject',
        'type',
        'slug',
        'status',
        'content',
        'program_id',
    ];
    protected $table = 'email_template';
    protected $casts = [
        'tags' => 'array',
    ];

    protected static $logName = 'email_template';
    protected static $logOnlyDirty = true;
    protected static $logAttributes = [
        'status',
        'subject'
    ];

    const PENDING_AFFILIATE_NOTE = 'Sent when an affiliate registers an account, waiting to be reviewed.';
    const NEW_AFFILIATE_NOTE = 'Sent when an affiliate is approved.';
    const DENIED_AFFILIATE_NOTE = 'Sent when an affiliate is denied.';
    const NEW_REFERRAL_NOTE = 'Sent when a referral order is recorded.';
    const APPROVE_REFERRAL_NOTE = 'Sent when a referral is approved.';
    const DENIED_REFERRAL_NOTE = 'Sent when a referral is denied.';
    const NEW_PAYMENT_ALERT_NOTE = 'Sent when a commission has been marked as paid.';
    const IMPORT_AFFILIATE_ALERT_NOTE = 'Sent to notify the affiliate(s) when you add them to your affiliate program.';
    const NEW_COUPON = 'Sent when a coupon is assigned manually to approved affiliates.';

    const AFFILIATE_VERIFICATION = 9;

    /**
     * Get notice for each email template
     *
     * @param $slug
     * @return string
     */

    public static function getEmailNotice($slug)
    {
        switch ($slug) {
            case 'pending_affiliate': return self::PENDING_AFFILIATE_NOTE;
            case 'approve_affiliate': return self::NEW_AFFILIATE_NOTE;
            case 'denied_affiliate': return self::DENIED_AFFILIATE_NOTE;
            case 'new_referral': return self::NEW_REFERRAL_NOTE;
            case 'approve_referral': return self::APPROVE_REFERRAL_NOTE;
            case 'denied_referral': return self::DENIED_REFERRAL_NOTE;
            case 'new_payment_alert': return self::NEW_PAYMENT_ALERT_NOTE;
            case 'invitation_affiliate': return self::IMPORT_AFFILIATE_ALERT_NOTE;
            case 'new_coupon': return self::NEW_COUPON;
            default: return '';
        }
    }


    public function getDescriptionForEvent(string $eventName): string
    {
        if($eventName == "updated") {
            $eventName = "edited";
        }
        return $eventName.' email template <a href="/admin/email/:subject.slug">:subject.name</a>';
        
    }

    public static function previewEmail(){
        $user = Auth::user();
        $setting = MerchantSetting::select('shop_id','affiliate_registration','brand_name', 'custom_domain','referral_link')->where('shop_id', $user->shop_id)->first();
        if($setting->custom_domain) {
            $aff_login_url = 'https://'.$setting->custom_domain.'/login' ;
        } else {
            $aff_login_url = env('APP_URL').'/'.$user->subdomain.'/login' ;
        }
        $verify_link = env('APP_URL').'/lalala/verify_email/MHq8MlzfsKkVkrpdfsYSXTU8e9UTDXoWH';
        $affiliate_link = $setting->referral_link.'?sca_ref=1323793.w6pS1G0KBy';
        $data_logo =(!empty($setting->affiliate_registration['logo']) ? asset_s3($setting->affiliate_registration['logo']) : '');
        $programId = !empty(Program::getLatestProgram()) ? Program::getLatestProgram()->id  : 0  ;
        $commission = Program::select('commission_amount','commission_type','rule','advance_amount')->where('shop_id', $user->shop_id);
        if ($programId) {
            $commission = $commission->where('id', $programId);
        }
        $commission = $commission->first();
        $dataCommission = get_commission_text($commission->commission_type, $commission->rule, $commission->commission_amount, $commission->advance_amount , session('money_format'));

        $replace = array(
            '{logo}' => "<img src='$data_logo' alt='' style='width: 100%;'>",
            '{brand_name}' => $setting->brand_name,
            '{first_name}' => 'John',
            '{last_name}' => 'Smith',
            '{support_email}' => $user->email,
            '{affiliate_login_url}' =>  $aff_login_url,
            '{affiliate_login_link}' => $aff_login_url,
            '{coupon}' => 'JOHNDOE',
            '{affiliate_link}' => $affiliate_link,
            '{referral_url}' => $affiliate_link,
            '{program_name}' => 'Standard Affiliate Commission',
            '{commission_structure}' => 'Percent of Sale',
            '{commission_amount}' => $dataCommission ?? "10%",
            '{affiliate_name}' => 'John',
            '{temporary_password}' => '123456',
            '{verify_link}' => $verify_link,
            '{referral_id}' => '479400',
            '{order_id}' => '#1001',
            '{order_number}' => '4287155175620',
            '{customer_name}' => 'John',
            '{commission_value}' => '$10',
            '{gift_name}'  => 'Welcome gift',
            '{gift_value}' => '$100',
            '{gift_type}'  => 'Free gift, shipping included',
            '{redeem_your_reward}'  => 'javascript:void(0)',
            '_blank' => ''
        );
        return $replace;
    }
}

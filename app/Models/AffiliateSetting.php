<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class AffiliateSetting extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    const SESSION_TIMEOUT_MINIUTES = 5; // miniutes
    const SESSION_TIMEOUT_SECONDS = 300; // seconds

    protected $fillable = ['payment_method', 'payment_info', 'info_w9_form', 'wise_form_data'];
    protected $table = 'affiliate_settings';
    protected $casts = [
        'payment_info' => 'array',
        'wise_form_data' => 'array'
    ];

    public $timestamps = false;
}

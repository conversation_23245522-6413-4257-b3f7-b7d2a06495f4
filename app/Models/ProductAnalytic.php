<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;

class ProductAnalytic extends Model
{
    protected $table = 'product_analytics';
    protected $connection = 'mongodb';

    const NEW_PRODUCT_ANALYTIC = 'new_product_analytic';
    const LIMIT_DATE = 90;
    const IS_SYNC_PRODUCT_ANALYTICS = 1;

    protected $fillable = [
        'shop_id',
        'shop_key',
        'order_id',
        'affiliate_id',
        'product_id',
        'product_name',
        'product_url',
        'img',
        'price',
        'quantity',
        'is_referral',
        'created_at',
        'updated_at',
        'currency',
        'variant_id',
        'variant_name'
    ];

    protected $casts = [
        'created_at' => 'date',
        'updated_at' => 'date',
        'is_referral' => 'bool',
        'price' => 'float'
    ];

    public function setAttribute($key, $value)
    {
        if ($this->hasCast($key)) {
            $value = $this->castAttribute($key, $value);
        }
        if ($key == 'is_referral') {
            $value = !!$value;
        }
        return parent::setAttribute($key, $value);
    }
}

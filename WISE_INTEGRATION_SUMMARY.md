# Wise Integration Summary

## Tổng quan Implementation

Đã implement thành công Wise integration cho affiliate payment settings với các điều kiện kiểm tra đầy đủ.

## Files đã thay đổi

### 1. SettingController.php
- **Added `canMerchantUseWise()`** method kiểm tra điều kiện:
  - Plan Professional trở lên (`helperPlan()->planProfessionalUp()`)
  - Wise integration enabled (`merchant_setting.wise_integration = 1`)
- **Updated `getPayment()`** để pass `canUseWise` xuống view
- **Updated `handleWiseFormSubmission()`** với validation:
  - Kiểm tra lại `canMerchantUseWise()`
  - Kiểm tra currency thuộc `wiseCurrencies`
  - Call `WisePayout::createRecipientAccount()`
  - Lưu với `payment_method = 'bank'` và data vào `payment_info`

### 2. WisePayout Service
- **Added `createRecipientAccount()`** method để call API endpoint
- Sử dụng existing service structure

### 3. UpdatePaymentMethodRequest
- **Không thay đổi validation** - vẫn dùng `payment_method = 'bank'`
- **Dynamic fields được accept** thông qua existing validation logic
- **Không validate chi tiết** vì đã validate ở JS rồi

### 4. Form Detection Logic
```php
private function isWiseForm($inputs) {
    // Must have currency field (from Wise API)
    if (!isset($inputs['currency']) || empty($inputs['currency'])) {
        return false;
    }

    // Check if it has old bank form fields - if yes, it's not Wise form
    $oldFormFields = ['account_type', 'name', 'account', 'number', 'branch', 'swift'];
    $hasOldFields = false;

    foreach ($oldFormFields as $field) {
        if (isset($inputs[$field]) && !empty($inputs[$field])) {
            $hasOldFields = true;
            break;
        }
    }

    // If has currency and no old form fields = Wise form
    return !$hasOldFields;
}
```

### 5. Field Mapping Logic
```php
private function formatWiseFormForStorage($inputs) {
    // Common field mappings for known Wise fields
    $fieldMappings = [
        'legalType' => [
            'label' => 'Recipient type',
            'options' => ['PRIVATE' => 'Person', 'BUSINESS' => 'Business']
        ],
        'accountType' => [
            'label' => 'Account type',
            'options' => ['CHECKING' => 'Checking', 'SAVINGS' => 'Savings']
        ],
        'accountHolderName' => ['label' => 'Account holder name'],
        'IBAN' => ['label' => 'IBAN'],
        'accountNumber' => ['label' => 'Account number'],
        'routingNumber' => ['label' => 'Routing number'],
        'address.country' => ['label' => 'Country'],
        // ... more mappings
    ];

    // Auto-generate labels for unknown fields
    $label = $mapping['label'] ?? ucfirst(str_replace(['_', '.'], [' ', ' '], $key));

    // Map option values to display names
    $name = '';
    if ($mapping && isset($mapping['options']) && isset($mapping['options'][$value])) {
        $name = $mapping['options'][$value];
    }
}
```

### 6. JavaScript Updates
- **Updated `select-currency.js`** để check `canUseWise` từ server
- **Disable standard bank form inputs** khi hiển thị Wise form
- **Enable lại inputs** khi quay về standard form

### 7. View Updates
- **Added `canUseWise`** variable vào payment.blade.php
- **Pass xuống JavaScript** để control form display

## Flow hoạt động

1. **User vào affiliate setting** → `getPayment()` → check `canMerchantUseWise()` → pass `canUseWise`, `wiseCurrencies` xuống view
2. **JS kiểm tra điều kiện** → chỉ hiển thị Wise form nếu `canUseWise = true` và currency thuộc `wiseCurrencies`
3. **User clicks "Save Changes"** → OTP sent → OTP verification → form.submit()
4. **Server receives data** → `updatePayment()` → `savePaymentAffiliateInfo()`
5. **System detects Wise form** bằng `isWiseForm()` (có currency, không có old fields)
6. **If Wise form:**
   - Kiểm tra lại `canMerchantUseWise()` và currency support
   - Calls `handleWiseFormSubmission()`
   - Calls `WisePayout::createRecipientAccount()`
   - If success: saves với `payment_method = 'bank'` và data vào `payment_info`
   - If error: redirects back với validation errors
7. **If old form:** xử lý như cũ

## Error Handling

- **Plan không đủ:** "Your account does not have access to Wise integration. Please upgrade your plan or contact support."
- **Currency không support:** "Selected currency is not supported by Wise integration."
- **Wise API error:** Show error từ API response hoặc generic message
- **Form validation errors:** Inline validation với Bootstrap classes

## Key Features

✅ **Kiểm tra điều kiện đầy đủ** - Plan Professional+ và Wise integration enabled
✅ **Currency validation** - chỉ cho phép currencies trong `wiseCurrencies`
✅ **Form detection thông minh** - dựa trên currency field và absence của old fields
✅ **Không tạo field mới** - dùng existing `payment_info`
✅ **Dynamic fields** từ Wise API được accept
✅ **Validation đã có ở JS** - server chỉ allow nullable
✅ **Detect bằng currency field** và không có old bank fields
✅ **Lưu với payment_method = 'bank'** (user vẫn chọn bank)
✅ **Backward compatible** với old forms

## Testing

- **Unit tests** cho `isWiseForm()`, `canMerchantUseWise()`, `formatWiseFormForStorage()`
- **Integration tests** cho full flow từ form submission đến database save
- **Error handling tests** cho các edge cases

## Security

- **Server-side validation** cho tất cả điều kiện
- **Double-check** permissions trước khi call Wise API
- **Input sanitization** cho dynamic fields từ Wise API
- **Error message** không expose sensitive information

## Performance

- **Minimal database queries** - chỉ query khi cần thiết
- **Efficient form detection** - early return cho performance
- **Cached plan checks** thông qua existing helper functions

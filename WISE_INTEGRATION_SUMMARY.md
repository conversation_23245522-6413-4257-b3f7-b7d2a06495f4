# Wise Integration Implementation - Updated

## Overview
Implementation đã được sửa đổi theo yêu cầu:
- **Không tạo field mới** - sử dụng `payment_method = 'wise'` và lưu vào `payment_info` 
- **Các field động** từ Wise API đã được validate ở JS, không cần validate lại ở server
- **Detect form** dựa trên có `currency` field và không có old bank fields

## Changes Made

### 1. SettingController.php
- **Modified `savePaymentAffiliateInfo()`** để detect Wise form và xử lý
- **Added `isWiseForm()`** method để detect form có `currency` và không có old bank fields
- **Added `handleWiseFormSubmission()`** để xử lý Wise form và call API
- **Set `payment_method = 'wise'`** cho Wise forms
- **Save data vào `payment_info`** với format bạn yêu cầu

### 2. WisePayout Service  
- **Added `createRecipientAccount()`** method để call API endpoint
- Sử dụng existing service structure

### 3. UpdatePaymentMethodRequest
- **Added `'wise'` to payment_method validation**
- **Added `wiseRule()`** method cho phép tất cả dynamic fields (nullable)
- **Không validate chi tiết** vì đã validate ở JS rồi

### 4. Form Detection Logic
```php
private function isWiseForm($inputs) {
    // Must have currency field (from Wise API)
    if (!isset($inputs['currency']) || empty($inputs['currency'])) {
        return false;
    }
    
    // Check if it has old bank form fields - if yes, it's not Wise form
    $oldFormFields = ['account_type', 'name', 'account', 'number', 'branch', 'swift'];
    foreach ($oldFormFields as $field) {
        if (isset($inputs[$field]) && !empty($inputs[$field])) {
            return false; // Has old form fields, not Wise form
        }
    }
    
    return true; // Has currency and no old form fields = Wise form
}
```

### 5. Data Storage Format
Lưu vào `affiliate_settings.payment_info` với format:
```json
{
  "profile_id": *********,
  "form": [
    {
      "key": "legalType",
      "label": "Recipient type", 
      "value": "BUSINESS",
      "name": "Business"
    },
    {
      "key": "accountHolderName",
      "label": "Account name",
      "value": "UpPromote",
      "name": ""
    },
    {
      "key": "IBAN",
      "label": "IBAN", 
      "value": "***********************",
      "name": ""
    }
  ]
}
```

## Flow Description

1. **User fills Wise form** (dynamic fields từ `https://api.sandbox.transferwise.tech/v1/account-requirements`)
2. **JS validation** đã xử lý validation
3. **User clicks "Save Changes"** → OTP sent → OTP verification → form.submit()
4. **Server receives data** → `updatePayment()` → `savePaymentAffiliateInfo()`
5. **System detects Wise form** bằng `isWiseForm()` (có currency, không có old fields)
6. **If Wise form:**
   - Calls `handleWiseFormSubmission()`
   - Calls `WisePayout::createRecipientAccount()`
   - If success: saves với `payment_method = 'wise'` và data vào `payment_info`
   - If error: redirects back với validation errors
7. **If old form:** xử lý như cũ

## Error Handling

- **Wise API errors** được catch và hiển thị như inline validation errors
- **Form validation** chỉ allow nullable cho tất cả dynamic fields
- **Backward compatibility** với old forms được giữ nguyên

## API Integration

- **Endpoint:** `POST /wise/create-recipient-account` 
- **Payload:** affiliate info, shop info, form data
- **Response:** `profile_id` on success hoặc `error` message

## Key Points

✅ **Không tạo field mới** - dùng existing `payment_info`  
✅ **Dynamic fields** từ Wise API được accept  
✅ **Validation đã có ở JS** - server chỉ allow nullable  
✅ **Detect bằng currency field** và không có old bank fields  
✅ **Lưu với payment_method = 'wise'**  
✅ **Backward compatible** với old forms  

## Testing

Created tests để verify:
- Form detection logic
- Data formatting 
- Service integration
- Error handling

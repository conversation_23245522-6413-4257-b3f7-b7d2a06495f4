@extends('app.layouts.default')
@section('title', 'Customer referral')

@section('page_heading')
    @include('app.includes.page_heading', ['title' => 'Customer referral', 'br'=>'refer_customer', 'object'=>null])
@endsection

@section('custom-css-by-page')
    <link href="{{asset('css/plugins/colorpicker/bootstrap-colorpicker.min.css')}}" rel="stylesheet">
    <link href="{{cdn_asset('css/merchant/convert_customer/index.min.css')}}" rel="stylesheet">
    <link href="{{cdn_asset('css/merchant/convert_customer/refer_customer.min.css')}}" rel="stylesheet">
    <style>
        .tox.tox-silver-sink.tox-tinymce-aux {
            position: fixed;
            z-index: 9999;
        }
    </style>
@endsection

@section('content')
    @include('app.includes.modals.youtube', ['id' => 'customer-refer', 'title' => 'Customer referral', 'link' => 'https://www.youtube.com/embed/zO5b5lptdbM'])
    <div class="row row-ibox-instruction">
        <div class="col-md-4 col-xs-6">
            <a href="#youtube-modal-customer-refer" data-toggle="modal" class="inbox ibox-instruction">
                <div class="ibox-content">
                    <i class="fab fa-youtube"></i>
                    <div class="ibox-instruction-content"></div>
                </div>
            </a>
        </div>
        <div class="col-md-4 col-xs-6">
            <a href="https://docs.uppromote.com/recruit-affiliates/new-customer-referral?utm_source=backend&utm_medium=customer-referral&utm_campaign=featureview"
               target="_blank"
               class="inbox ibox-instruction">
                <div class="ibox-content">
                    <i class="fal fa-book"></i>
                    <div class="ibox-instruction-content"></div>
                </div>
            </a>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-3 col-xs-12 col-md-3 col-sm-3">
            <div class="file-manager">
                <div class="space-25"></div>
                <h3 class="file-manager__title mt-0">Convert by:</h3>
                @include('app.includes.convert_customer_sidebar')
                <div class="clearfix"></div>
            </div>
        </div>
        <div class="col-lg-9 col-md-9 col-sm-9 col-xs-12 animated fadeInRight">
            <form method="POST" action="{{route('refer_customer.update')}}" name="frm_customer_referral">
                {!! csrf_field() !!}
                <div class="ibox float-e-margins">
                    <div class="ibox-title @if(!$canActive) message-upgrade message-upgrade-r0 @endif">
                        <h3>Customer referral</h3>
                    </div>
                    <div class="ibox-content">
                        <div class="tabs-container refer_customer_list">
                            <ul class="nav nav-tabs">
                                <li class="active"><a data-toggle="tab" href="#settings">Settings</a></li>
                                <li class=""><a data-toggle="tab" href="#design">Design</a></li>
                                <li class=""><a data-toggle="tab" href="#reward_email">Reward email</a></li>
                            </ul>

                            <div class="tab-content">
                                <div id="settings" class="tab-pane active form-horizontal">
                                    <div class="panel-body">
                                        <div class="row">
                                            @if ($errors->any())
                                                <div class="alert alert-danger">
                                                    <ul>
                                                        @foreach ($errors->all() as $error)
                                                            <li>{!! $error !!}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            @endif

                                            @if(session()->has('ok'))
                                                @include('app.includes.error', ['type' => 'success', 'message' => session('ok')])
                                            @endif

                                            <div class="col-md-6 col-lg-6 col-sm-6 col-xs-12 pd0-left-right">
                                                <h3>General Information</h3>
                                                <section class="box-forms mb-5">
                                                    <div class="row">
                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="name" class="form-label-line">
                                                                <span><strong>Program name</strong></span>
                                                                <input name="program_name" id="program_name" required=""
                                                                       type="text"
                                                                       class="form-control form-control-line"
                                                                       value="{{$programRefer['name'] ?? ''}}">
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms">
                                                            <label for="active"
                                                                   class="form-label-line form-label-checkbox label__pointer @if(!$canActive) {{'cursor-disabled message-upgrade-feature'}} @else {{'cursor-pointer'}} @endif"
                                                                   @if(!$canActive) data-toggle="popover"
                                                                   data-placement="bottom" data-html="true"
                                                                   data-content="Available from Growth plan. Try it free for 14 days. <a class='tab-parent' href='/admin/pricing?utm_source=backend&utm_medium=customer-referral&utm_campaign=featureview&up={{ Request::getRequestUri()}}&focusing-element=customer-referral&focusing-feature=customer-referral#affiliate-team' target='_blank'><br/><b>Upgrade to unlock</b></a>"@endif>
                                                                <span><strong>Active</strong></span>
                                                                <input class="i-checks" type="checkbox"
                                                                       {{($programRefer['status']) ? 'checked' : ''}} name="active"
                                                                       id="active" @if(!$canActive)
                                                                    {{'disabled'}}
                                                                        @endif>
                                                            </label>
                                                            <i class="text-note">By activating this feature, your
                                                                customers will automatically get discounts when visiting
                                                                your shop via a customer referral link.</i>
                                                        </div>
                                                    </div>
                                                </section>

                                                <h3>Friend incentive</h3>
                                                <section class="box-forms mb-5">
                                                    <div class="row">
                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms">
                                                            <label for="incentive_type"
                                                                   class="form-label-line form-label-select">
                                                                <span><strong>Discount type</strong></span>
                                                                <select class="form-control form-control-select label__pointer"
                                                                        name="incentive_type" id="incentive_type">
                                                                    <option value="{{\App\Models\Program::INCENTIVE_NONE}}" {{($programRefer['refer_customer_incentive']['type'] == \App\Models\Program::INCENTIVE_NONE) ? 'selected' : ''}}>
                                                                        None
                                                                    </option>
                                                                    <option value="{{\App\Models\Program::INCENTIVE_FIXED_AMOUNT}}" {{($programRefer['refer_customer_incentive']['type'] == \App\Models\Program::INCENTIVE_FIXED_AMOUNT) ? 'selected' : ''}}>
                                                                        Fixed amount
                                                                    </option>
                                                                    <option value="{{\App\Models\Program::INCENTIVE_PERCENTAGE}}" {{($programRefer['refer_customer_incentive']['type'] == \App\Models\Program::INCENTIVE_PERCENTAGE) ? 'selected' : ''}}>
                                                                        Percentage
                                                                    </option>
                                                                    <option value="{{\App\Models\Program::INCENTIVE_FREE_SHIPPING}}" {{($programRefer['refer_customer_incentive']['type'] == \App\Models\Program::INCENTIVE_FREE_SHIPPING) ? 'selected' : ''}}>
                                                                        Free shipping
                                                                    </option>
                                                                </select>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms box_incentive_amount mt-2 {{($programRefer['refer_customer_incentive']['type'] == 'none' || $programRefer['refer_customer_incentive']['type'] == 'shipping_line') ? 'd-none' : 'd-block' }}">
                                                            <label for="incentive_amount" class="form-label-line">
                                                                <span><strong>Amount</strong></span>
                                                                <div class="input-group" id="incentive_amount">
                                                                    <input type="number" step="any"
                                                                           class="form-control form-control-line"
                                                                           name="incentive_amount" required=""
                                                                           placeholder="0.00"
                                                                           @if($programRefer['refer_customer_incentive']['type'] != 'none') min="0.1"
                                                                           @endif
                                                                           @if($programRefer['refer_customer_incentive']['type'] == 'percentage') max="100"
                                                                           @endif
                                                                           pattern="^[0-9]{1,8}$"
                                                                           value="{{number_format($programRefer['refer_customer_incentive']['value'],2,'.','') ?? ''}}" />
                                                                    <span class="input-group-addon">{{($programRefer['refer_customer_incentive']['type'] == 'percentage') ? '%' : $currency}}</span>
                                                                </div>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms box_incentive_coupon_name mt-2 {{($programRefer['refer_customer_incentive']['type'] == 'none') ? 'd-none' : 'd-block' }}">
                                                            <label for="coupon_name" class="form-label-line">
                                                                <span><strong>Coupon name</strong></span>
                                                                <input name="coupon_name" id="coupon_name" required="" type="text" class="form-control form-control-line" value="{{$discountName}}">
                                                            </label>
                                                        </div>
                                                    </div>
                                                </section>

                                                <h3>Customer Rewards</h3>
                                                <section class="box-forms">
                                                    <div class="row">
                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="commission_type"
                                                                   class="form-label-line form-label-select">
                                                                <span><strong>Reward type</strong></span>
                                                                <select class="form-control form-control-select label__pointer"
                                                                        name="commission_type" id="commission_type">
                                                                    <option value="{{\App\Models\Program::FLAT_RATE_PER_ORDER}}" {{($programRefer['commission_type'] == \App\Models\Program::FLAT_RATE_PER_ORDER) ? 'selected' : '' }}>
                                                                        Flat Rate Per Order
                                                                    </option>
                                                                    <option value="{{\App\Models\Program::PERCENT_OF_SALE}}" {{($programRefer['commission_type'] == \App\Models\Program::PERCENT_OF_SALE) ? 'selected' : '' }}>
                                                                        Percent Of Sale
                                                                    </option>
                                                                </select>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms">
                                                            <label for="commission_amount" class="form-label-line">
                                                                <span><strong>Amount</strong></span>
                                                                <div class="input-group" id="commission_amount">
                                                                    <input type="number" step="any"
                                                                           class="form-control form-control-line"
                                                                           name="commission_amount" required=""
                                                                           placeholder="0.00" min="0.1"
                                                                           @if($programRefer['commission_type'] == 2) max="100"
                                                                           @endif
                                                                           pattern="^[0-9]{1,8}$"
                                                                           value="{{$programRefer['commission_amount'] ?? ''}}" />
                                                                    <span class="input-group-addon">{{($programRefer['commission_type'] == 2) ? '%' : $currency}}</span>
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </section>
                                            </div>
                                            @if($programRefer && isset($programRefer['id']) && $programRefer['status'])
                                                <div class="col-md-6 col-lg-6 col-sm-6 col-xs-12 pd0-left-right">
                                                    @include('app.includes.theme-app-extension.turn-on-customer-referral')
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div id="design" class="tab-pane">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-5 col-lg-5 col-sm-5 col-xs-12 pd0-left b-r mb-5">
                                                <h3>Refer & Earn button</h3>
                                                <section class="box-forms">
                                                    <div class="row">
                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="refer_button_text" class="form-label-line">
                                                                <span><strong>Button text</strong></span>
                                                                <input name="refer_button_text" id="refer_button_text"
                                                                       required="" type="text"
                                                                       class="form-control form-control-line"
                                                                       value="{{$programRefer['refer_customer_button']['refer_button_text'] ?? ''}}">
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="label_refer_background_color"
                                                                   class="form-label-line">
                                                                <span><strong>Background color</strong></span>
                                                                <div id="refer_background_color"
                                                                     class="input-group colorpicker-component">
                                                                    <input type="text"
                                                                           value="{{$programRefer['refer_customer_button']['refer_background_color'] ?? ''}}"
                                                                           name="refer_background_color"
                                                                           class="form-control form-control-line"
                                                                           id="label_refer_background_color" />
                                                                    <span class="input-group-addon input-group-addon-color"><i></i></span>
                                                                </div>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="label_refer_text_color" class="form-label-line">
                                                                <span><strong>Text color</strong></span>
                                                                <div id="refer_text_color"
                                                                     class="input-group colorpicker-component">
                                                                    <input type="text"
                                                                           value="{{$programRefer['refer_customer_button']['refer_text_color'] ?? ''}}"
                                                                           name="refer_text_color"
                                                                           class="form-control form-control-line"
                                                                           id="label_refer_text_color" />
                                                                    <span class="input-group-addon input-group-addon-color"><i></i></span>
                                                                </div>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="web_position"
                                                                   class="form-label-line form-label-select">
                                                                <span><strong>Web position</strong></span>
                                                                <select class="form-control form-control-select label__pointer"
                                                                        name="web_position" id="web_position">
                                                                    <option value="right-bottom" {{($programRefer['refer_customer_button']['web_position'] == 'right-bottom') ? 'selected' : ''}}>
                                                                        Right bottom
                                                                    </option>
                                                                    <option value="right-top" {{($programRefer['refer_customer_button']['web_position'] == 'right-top') ? 'selected' : ''}}>
                                                                        Right top
                                                                    </option>
                                                                    <option value="right-center" {{($programRefer['refer_customer_button']['web_position'] == 'right-center') ? 'selected' : ''}}>
                                                                        Right center
                                                                    </option>
                                                                    <option value="left-top" {{($programRefer['refer_customer_button']['web_position'] == 'left-top') ? 'selected' : ''}}>
                                                                        Left top
                                                                    </option>
                                                                    <option value="left-center" {{($programRefer['refer_customer_button']['web_position'] == 'left-center') ? 'selected' : ''}}>
                                                                        Left center
                                                                    </option>
                                                                    <option value="left-bottom" {{($programRefer['refer_customer_button']['web_position'] == 'left-bottom') ? 'selected' : ''}}>
                                                                        Left bottom
                                                                    </option>
                                                                    <option value="center-top" {{($programRefer['refer_customer_button']['web_position'] == 'center-top') ? 'selected' : ''}}>
                                                                        Center top
                                                                    </option>
                                                                    <option value="center-bottom" {{($programRefer['refer_customer_button']['web_position'] == 'center-bottom') ? 'selected' : ''}}>
                                                                        Center bottom
                                                                    </option>
                                                                </select>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms">
                                                            <label for="mobile_position"
                                                                   class="form-label-line form-label-select">
                                                                <span><strong>Mobile position</strong></span>
                                                                <select class="form-control form-control-select label__pointer"
                                                                        name="mobile_position" id="mobile_position">
                                                                    <option value="right-bottom" {{($programRefer['refer_customer_button']['mobile_position'] == 'right-bottom') ? 'selected' : ''}}>
                                                                        Right bottom
                                                                    </option>
                                                                    <option value="right-top" {{($programRefer['refer_customer_button']['mobile_position'] == 'right-top') ? 'selected' : ''}}>
                                                                        Right top
                                                                    </option>
                                                                    <option value="right-center" {{($programRefer['refer_customer_button']['mobile_position'] == 'right-center') ? 'selected' : ''}}>
                                                                        Right center
                                                                    </option>
                                                                    <option value="left-top" {{($programRefer['refer_customer_button']['mobile_position'] == 'left-top') ? 'selected' : ''}}>
                                                                        Left top
                                                                    </option>
                                                                    <option value="left-center" {{($programRefer['refer_customer_button']['mobile_position'] == 'left-center') ? 'selected' : ''}}>
                                                                        Left center
                                                                    </option>
                                                                    <option value="left-bottom" {{($programRefer['refer_customer_button']['mobile_position'] == 'left-bottom') ? 'selected' : ''}}>
                                                                        Left bottom
                                                                    </option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </section>
                                            </div>

                                            <div class="col-md-7 col-lg-7 col-sm-7 col-xs-12">
                                                <h3>Preview</h3>
                                                <div class="preview-box">
                                                    <iframe id="preview_refer_button" frameborder="0" scrolling="no"
                                                            data-hj-allow-iframe="true"
                                                            src="{{route('refer_customer.preview', 'button')}}"
                                                            style="width: 100%; height: 100px;"></iframe>
                                                </div>
                                                <i class="text-note">This will show a widget on every pages on your
                                                    website</i>
                                            </div>
                                        </div>

                                        <div class="row mt-xs-2">
                                            <div class="col-md-5 col-lg-5 col-sm-5 col-xs-12 pd0-left b-r mb-5">
                                                <h3>Get invite link popup</h3>
                                                <section class="box-forms">
                                                    <div class="row">
                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="popup_header" class="form-label-line">
                                                                <span><strong>Header</strong></span>
                                                                <input name="popup_header" id="popup_header" required=""
                                                                       type="text"
                                                                       class="form-control form-control-line"
                                                                       value="{{$programRefer['refer_customer_invite']['popup_header'] ?? ''}}">
                                                                <p id="popup_header_hidden" class="hidden"></p>
                                                            </label>
                                                        </div>

                                                        <div class="col-md-12 col-lg-12 col-sm-12 col-xs-12 mb-2">
                                                            <label for="popup_description" class="form-label-line">
                                                                <span><strong>Description</strong></span>
                                                                <textarea name="popup_description"
                                                                          id="popup_description"
                                                                          class="form-control">{{$programRefer['refer_customer_invite']['popup_description'] ?? ''}}</textarea>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="button_text_get_invite" class="form-label-line">
                                                                <span><strong>Button text Get invite link</strong></span>
                                                                <input name="button_text_get_invite"
                                                                       id="button_text_get_invite" required=""
                                                                       type="text"
                                                                       class="form-control form-control-line"
                                                                       value="{{$programRefer['refer_customer_invite']['button_text_get_invite'] ?? ''}}">
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="button_copy_invite" class="form-label-line">
                                                                <span><strong>Copy text Get invite link</strong></span>
                                                                <input name="button_copy_invite" id="button_copy_invite"
                                                                       required="" type="text"
                                                                       class="form-control form-control-line"
                                                                       value="{{$programRefer['refer_customer_invite']['button_copy_invite'] ?? ''}}">
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="label_popup_background_color"
                                                                   class="form-label-line">
                                                                <span><strong>Background color</strong></span>
                                                                <div id="popup_background_color"
                                                                     class="input-group colorpicker-component">
                                                                    <input type="text"
                                                                           value="{{$programRefer['refer_customer_invite']['popup_background_color'] ?? ''}}"
                                                                           name="popup_background_color"
                                                                           class="form-control form-control-line"
                                                                           id="label_popup_background_color" />
                                                                    <span class="input-group-addon input-group-addon-color"><i></i></span>
                                                                </div>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms">
                                                            <label for="label_popup_text_color" class="form-label-line">
                                                                <span><strong>Text color</strong></span>
                                                                <div id="popup_text_color"
                                                                     class="input-group colorpicker-component">
                                                                    <input type="text"
                                                                           value="{{$programRefer['refer_customer_invite']['popup_text_color'] ?? ''}}"
                                                                           name="popup_text_color"
                                                                           class="form-control form-control-line"
                                                                           id="label_popup_text_color" />
                                                                    <span class="input-group-addon input-group-addon-color"><i></i></span>
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </section>
                                            </div>

                                            <div class="col-md-7 col-lg-7 col-sm-7 col-xs-12">
                                                <h3>Preview</h3>
                                                <div class="preview-box">
                                                    <iframe id="preview_refer_customer" frameborder="0" scrolling="auto"
                                                            data-hj-allow-iframe="true"
                                                            src="{{route('refer_customer.preview', 'widget')}}"
                                                            style="width: 100%; min-height: 330px;"></iframe>
                                                </div>
                                                <i class="text-note">This will pop up once the widget is clicked on</i>
                                            </div>
                                        </div>

                                        <div class="row mt-xs-2">
                                            <div class="col-md-5 col-lg-5 col-sm-5 col-xs-12 pd0-left b-r">
                                                <h3>Shop now popup</h3>
                                                <section class="box-forms">
                                                    <div class="row">
                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="shop_header" class="form-label-line">
                                                                <span><strong>Header</strong></span>
                                                                <input name="shop_header" id="shop_header" required=""
                                                                       type="text"
                                                                       class="form-control form-control-line"
                                                                       value="{{str_replace('{brand_name}', $brandName, $programRefer['refer_customer_incentive_popup']['shop_header'])}}">
                                                                <p id="shopnow_header_hidden" class="hidden"></p>
                                                            </label>
                                                        </div>

                                                        <div class="col-md-12 col-lg-12 col-sm-12 col-xs-12 mb-2">
                                                            <label for="shop_description" class="form-label-line">
                                                                <span><strong>Description</strong></span>
                                                                <textarea name="shop_description" id="shop_description"
                                                                          class="form-control">{{$programRefer['refer_customer_incentive_popup']['shop_description'] ?? ''}}</textarea>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="shop_button_text" class="form-label-line">
                                                                <span><strong>Button text</strong></span>
                                                                <input name="shop_button_text" id="shop_button_text"
                                                                       required="" type="text"
                                                                       class="form-control form-control-line"
                                                                       value="{{$programRefer['refer_customer_incentive_popup']['shop_button_text'] ?? ''}}">
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms mb-2">
                                                            <label for="label_shop_background_color"
                                                                   class="form-label-line">
                                                                <span><strong>Background color</strong></span>
                                                                <div id="shop_background_color"
                                                                     class="input-group colorpicker-component">
                                                                    <input type="text"
                                                                           value="{{$programRefer['refer_customer_incentive_popup']['shop_background_color'] ?? ''}}"
                                                                           name="shop_background_color"
                                                                           class="form-control form-control-line"
                                                                           id="label_shop_background_color" />
                                                                    <span class="input-group-addon input-group-addon-color"><i></i></span>
                                                                </div>
                                                            </label>
                                                        </div>

                                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms">
                                                            <label for="label_shop_text_color" class="form-label-line">
                                                                <span><strong>Text color</strong></span>
                                                                <div id="shop_text_color"
                                                                     class="input-group colorpicker-component">
                                                                    <input type="text"
                                                                           value="{{$programRefer['refer_customer_incentive_popup']['shop_text_color'] ?? ''}}"
                                                                           name="shop_text_color"
                                                                           class="form-control form-control-line"
                                                                           id="label_shop_text_color" />
                                                                    <span class="input-group-addon input-group-addon-color"><i></i></span>
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </section>
                                            </div>

                                            <div class="col-md-7 col-lg-7 col-sm-7 col-xs-12 mt-xs-2">
                                                <h3>Preview</h3>
                                                <div class="preview-box">
                                                    <iframe id="preview_refer_shop" frameborder="0" scrolling="auto"
                                                            data-hj-allow-iframe="true"
                                                            src="{{route('refer_customer.preview', 'shop-now')}}"
                                                            style="width: 100%;  min-height: 220px;"></iframe>
                                                </div>
                                                <i class="text-note">This will show your website when friends access the
                                                    referral link</i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="reward_email" class="tab-pane">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-12 col-lg-12 col-sm-12 col-xs-12 d-flex pd0-left-right"
                                                 translate="no">
                                                <label for="refer_customer_email_subject"
                                                       class="form-label-line mb-0 flex-grow mr-2">
                                                    <span><strong>Subject</strong></span>
                                                    <input name="refer_customer_email_subject"
                                                           id="refer_customer_email_subject" type="text"
                                                           class="form-control form-control-line"
                                                           value="{{$programRefer['refer_customer_email_subject']}}"
                                                           required="">
                                                </label>
                                                <button class="btn btn-sm btn-default" data-toggle="modal"
                                                        data-target="#tagsModal" title="Tags" onclick="return false">
                                                    <i class="fal fa-tags"></i> Tags
                                                </button>
                                            </div>

                                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms pd0-left-right">
                                                <div class="tabs-container nav-tab-collection">
                                                    <ul class="nav nav-tabs d-flex">
                                                        <li class="active">
                                                            <a data-toggle="tab" href="#email_content"
                                                               aria-expanded="false">
                                                                Content
                                                            </a>
                                                        </li>
                                                        <li class="">
                                                            <a data-toggle="tab" href="#reward_email_preview"
                                                               aria-expanded="false">
                                                                Preview&nbsp; <i
                                                                        class="fal fa-info-circle text-success tooltip-email"
                                                                        data-toggle="tooltip" data-placement="top"
                                                                        data-original-title="Kindly note that some of the content in this preview is sample. Your email will be automatically filled out with correct information once sent."></i>
                                                            </a>
                                                        </li>
                                                    </ul>

                                                    <div class="tab-content">
                                                        <div class="tab-content">
                                                            <div id="email_content" class="tab-pane active">
                                                                <textarea class="reward_summernote summernote"
                                                                          name="reward_summernote"
                                                                          id="reward_summernote" required>@if(isset($programRefer['refer_customer_email']))
                                                                        {{html_entity_decode($programRefer['refer_customer_email'])}}
                                                                    @else
                                                                        @include('app.pages.email.template.refer_customer.reward_email')
                                                                    @endif</textarea>
                                                            </div>
                                                            <div id="reward_email_preview" class="tab-pane"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="customer_refer_handle" class="d-flex">
                    <button id="submit-button" name="submit_refer_customer" class="btn btn-primary"
                            type="button" @if(!$canActive)
                        {{'disabled'}}
                            @endif>
                        Save changes
                    </button>

                    <button class="btn btn-default reset-template" style="display: none;" type="button"
                            value="{{!empty($programRefer['id']) ? $programRefer['id'] : ''}}" @if(!$canActive)
                        {{'disabled'}}
                            @endif>
                        Reset default template
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="modal inmodal" id="tagsModal" tabindex="-1" role="dialog" aria-hidden="true" translate="no">
        <div class="modal-dialog">
            <div class="modal-content animated fadeIn">
                <div class="modal-header">
                    <h4 class="modal-title">Tags</h4>
                    <div class="ibox-tools">
                        <a class="close-link close" data-dismiss="modal">
                        <span class="mdiv">
                            <span class="md"></span>
                        </span>
                        </a>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="ibox mb-0">
                        <div class="ibox-content border-radius-25">
                            <table style="width:100%">
                                <tr>
                                    <td>{{'{logo}'}}</td>
                                </tr>
                                <tr>
                                    <td>{{'{brand_name}'}}</td>
                                </tr>
                                <tr>
                                    <td>{{'{shop_name}'}}</td>
                                </tr>
                                <tr>
                                    <td>{{'{reward}'}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script_inline')
    <script src="{{cdn_asset('js/plugins/tinymce/6.1.0/tinymce.min.js')}}"></script>
    <script src="{{cdn_asset('js/plugins/colorpicker/bootstrap-colorpicker.min.js')}}"></script>
    <script src="{{cdn_asset('js/app/refer_customer/index.js')}}"></script>
    <script>
      const createdRecord = Boolean({{ $createdRecord }})
      const originStatus = Boolean({{ $programRefer['status'] }})
      $(function() {
        $('.colorpicker-component').colorpicker()
        let brandName = '{{$brandName}}'
        tinymce.init($.extend({}, tinymceGlobalConfig, {
          selector: '#reward_summernote',
          height: 800
        }))

        // Preview email
        $('li a[href="#reward_email_preview"]').click(function() {
          let logo = '{{ $logo }}'
          let shopName = '{{ $shopName }}'
          let previewEmail = tinymce.activeEditor.getContent()

          $('#reward_email_preview').html(previewEmail)
          $('#reward_email_preview').html(function(index, html) {
              let contentPreview = html.replaceAll('{logo}', `<img src='${logo}' width='100%' title='logo brand' alt='logo brand' />`)
              contentPreview = contentPreview.replaceAll('{brand_name}', `${brandName}`)
              contentPreview = contentPreview.replaceAll('{shop_name}', `${shopName}`)
              contentPreview = contentPreview.replaceAll('{reward}', Shopify.formatMoney(10, currencyFormat))
              return contentPreview
          })

          $('a[href="{redeem_your_reward}"]').attr('href', '#').removeAttr('target')
          $('.note-popover.popover.in.note-link-popover.bottom').attr('style', 'display: none !important')
        })

        $('a[href="#reward_email"]').click(function() {
          $('#customer_refer_handle button.reset-template').show()
        })

        $('.refer_customer_list a[href="#settings"], .refer_customer_list a[href="#design"]').click(function() {
          $('#customer_refer_handle button.reset-template').hide()
        })

        const getHeaderAndDescription = () => {
          let rewardsType = $('select#commission_type').val()
          var amountRewards = $('#commission_amount input[name="commission_amount"]').val()
          if (rewardsType == 2) {
            amountRewards = amountRewards + '%'
          } else {
            amountRewards = '{{$currency}}' + amountRewards
          }

          let discountType = $('select#incentive_type').val()
          var amountIncentive = $('#incentive_amount input[name="incentive_amount"]').val()
          if (discountType == 'percentage') {
            amountIncentive = amountIncentive + '%'
          } else if (discountType == 'fixed_amount') {
            amountIncentive = '{{$currency}}' + amountIncentive
          }

          // replace text **invite link popup**
          var description = referCustomerInviteDescription(discountType, rewardsType, amountIncentive, amountRewards)
          $('textarea#popup_description').html(description)
          $('#preview_refer_customer').contents().find('#customer_popup_header').text($('#popup_header').val())
          $('#preview_refer_customer').contents().find('#customer_popup_description').html(description)

          // replace text shop now preview
          var descriptionShopNow = referCustomerShowNowDescription(discountType)
          var showNowHeader = referCustomerShowNowHeader(discountType, amountIncentive, brandName)
          $('textarea#shop_description').html(descriptionShopNow)
          $('#preview_refer_shop').contents().find('#shop_now_header').html(showNowHeader)
          $('#preview_refer_shop').contents().find('#show_now_description').html(descriptionShopNow)
          $('#shopnow_header_hidden').html(referCustomerShowNowHeader(discountType, amountIncentive, brandName))
          $('#shop_header').val($('#shopnow_header_hidden').text())
        }

        const getReferCustomerInviteHeader = () => {
          let rewardsType = $('select#commission_type').val()

          if (rewardsType == 2) {
            $('#commission_amount .input-group-addon').html('%')
            $('#commission_amount input[name="commission_amount"]').attr('max', 100)
            $('#popup_header').val(referCustomerInviteHeader(rewardsType, $('#commission_amount input[name="commission_amount"]').val() + '%'))
          } else {
            $('#commission_amount .input-group-addon').html('{{$currency}}')
            $('#commission_amount input[name="commission_amount"]').removeAttr('max')
            $('#popup_header_hidden').html(referCustomerInviteHeader(rewardsType, `{{$currency}}` + $('#commission_amount input[name="commission_amount"]').val()))
            $('#popup_header').val($('#popup_header_hidden').text())
          }
        }

        $('select#incentive_type').change(function() {
          let discountType = $(this).val()

          switch (discountType) {
            case 'none':
              $('div.box_incentive_amount, div.box_incentive_coupon_name').addClass('d-none').removeClass('d-block')
              $('#incentive_amount input[name="incentive_amount"]').val(10)
              $('#incentive_amount input[name="incentive_amount"]').removeAttr('max').removeAttr('min')
              break
            case 'shipping_line':
              $('div.box_incentive_amount').addClass('d-none').removeClass('d-block')
              $('div.box_incentive_coupon_name').addClass('d-block').removeClass('d-none')
              $('#incentive_amount input[name="incentive_amount"]').val(10)
              $('#incentive_amount input[name="incentive_amount"]').removeAttr('max').removeAttr('min')
              break

            case 'percentage':
              $('div.box_incentive_amount, div.box_incentive_coupon_name').addClass('d-block').removeClass('d-none')
              $('#incentive_amount .input-group-addon').html('%')
              $('#incentive_amount input[name="incentive_amount"]').attr('max', 100).attr('min', '0.1')
              break

            case 'fixed_amount':
              $('div.box_incentive_amount, div.box_incentive_coupon_name').addClass('d-block').removeClass('d-none')
              $('#incentive_amount .input-group-addon').html('{{$currency}}')
              $('#incentive_amount input[name="incentive_amount"]').removeAttr('max').attr('min', '0.1')
              break
            default:
              break
          }
          getHeaderAndDescription()
        })

        $('select#commission_type').change(function() {
          getReferCustomerInviteHeader()
          getHeaderAndDescription()
        })

        $('#incentive_amount input[name="incentive_amount"], #commission_amount input[name="commission_amount"]').on('input', function() {
          getReferCustomerInviteHeader()
          getHeaderAndDescription()
        })

        /************ START Refer & Earn button **********/
        $('#refer_button_text').on('input', function() {
          $('#preview_refer_button').contents().find('#button_refer_button_text').text($(this).val())
        })

        $('#refer_background_color').colorpicker().on('changeColor', function(e) {
          $('#preview_refer_button').contents().find('#button_refer_background_color').css({
            'backgroundColor': e.color.toString('rgba'),
            'borderColor': e.color.toString('rgba')
          })
        })

        $('#refer_text_color').colorpicker().on('changeColor', function(e) {
          $('#preview_refer_button').contents().find('#button_refer_button_text').css('color', e.color.toString('rgba'))
        })

        /************ START Get invite link popup **********/
        $('#popup_header').on('input', function() {
          $('#preview_refer_customer').contents().find('#customer_popup_header').text($(this).val())
        })

        $('textarea#popup_description').on('keyup', function() {
          $('#preview_refer_customer').contents().find('#customer_popup_description').text($(this).val())
        })

        $('#button_text_get_invite').on('input', function() {
          $('#preview_refer_customer').contents().find('#preview_invite__link-button').text($(this).val())
        })

        $('#popup_background_color').colorpicker().on('changeColor', function(e) {
          $('#preview_refer_customer').contents().find('span#preview_invite__link-button:last-child').css({
            'backgroundColor': e.color.toString('rgba'),
            'borderColor': e.color.toString('rgba')
          })
        })

        $('#popup_text_color').colorpicker().on('changeColor', function(e) {
          $('#preview_refer_customer').contents().find('span#preview_invite__link-button:last-child').css('color', e.color.toString('rgba'))
        })

        /************ START Shop now popup **********/
        $('#shop_header').on('input', function() {
          $('#preview_refer_shop').contents().find('#shop_now_header').text($(this).val())
        })

        $('textarea#shop_description').on('keyup', function() {
          $('#preview_refer_shop').contents().find('#show_now_description').text($(this).val())
        })

        $('#shop_button_text').on('input', function() {
          $('#preview_refer_shop').contents().find('#preview_shop__link-button').text($(this).val())
        })

        $('#shop_background_color').colorpicker().on('changeColor', function(e) {
          $('#preview_refer_shop').contents().find('#preview_shop__link-button').css({
            'backgroundColor': e.color.toString('rgba'),
            'borderColor': e.color.toString('rgba')
          })
        })

        $('#shop_text_color').colorpicker().on('changeColor', function(e) {
          $('#preview_refer_shop').contents().find('#preview_shop__link-button').css('color', e.color.toString('rgba'))
        })

        const submitForm = $(`form[name=frm_customer_referral]`), submitButton = $(`#submit-button`)
        submitButton.on('click', function() {
          const activated = $(`#active`)
          if (!createdRecord) {
            submitForm.submit()
            return;
          }
          if (!activated.prop('checked') && originStatus) {
            swal({
              title: 'Are you sure?',
              text: 'Deactivating this program will prevent future customers from signing up. However, current customers will still get commissions for referrals if their friends buy from the existing invite links.',
              type: '',
              showCancelButton: true,
              confirmButtonColor: '#DD6B55',
              confirmButtonText: 'Yes',
              closeOnConfirm: true
            }, () => submitForm.submit())
          } else {
            submitForm.submit()
          }
        })

        $('#customer_refer_handle .reset-template').click(function() {
          let programId = $(this).val()

          $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
          })
          swal({
            title: 'Are you sure?',
            text: 'If you reset this email template, its content and format will be restored to the default version. This action cannot be undone.',
            type: '',
            showCancelButton: true,
            confirmButtonColor: '#DD6B55',
            confirmButtonText: 'Yes',
            closeOnConfirm: true
          }, () => {
            $.ajax({
              url: '{{route('refer_customer.email_reset')}}',
              type: 'POST',
              dataType: 'json',
              data: { program_id: programId },

              success: function(result) {
                if (result.status == 'ok') {
                  location.reload()
                }
              }
            })
          })
        })
      })
    </script>
@endsection
@extends('app.layouts.default')
@section('title', 'Bulk Email')

@section('page_heading')
    @include('app.includes.page_heading', ['title' => 'Email', 'br' => 'email.bulk_email', 'object' => null])
@endsection

@section('custom-css-by-page')
    <link href="{{asset('css/plugins/bootstrap-select/bootstrap-select.css')}}" rel="stylesheet"/>
    <link rel="stylesheet" href="{{ cdn_asset('css/merchant/email/bulk_email.min.css') }}" />
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <form action="{{ route('email.bulk') }}" method="post" id="bulk_form">
                {!! csrf_field() !!}
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <span class="alert-link">Errors: </span>
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(session()->has('ok'))
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-success">
                                <button type="button" class="close" data-dismiss="alert">
                                    <span aria-hidden="true">&times;</span>
                                    <span class="sr-only">Close</span>
                                </button>
                                <b>Success:</b> {{ session('ok') }}<br>
                                @if(session()->has('fail-mail'))
                                    Please find the list of failed emails <span style="color: #0d8ddb;text-decoration:underline;"><a
                                                href="{{ route('email.fail.export') }}" >here</a></span> (it can only be downloaded in this session).
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                @if(session()->has('error'))
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-danger">
                                <button type="button" class="close" data-dismiss="alert">
                                    <span aria-hidden="true">&times;</span>
                                    <span class="sr-only">Close</span>
                                </button>
                                {{ session('error') }}
                            </div>
                        </div>
                    </div>
                @endif

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h3>Bulk Email</h3>
                    </div>

                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <h4 class="m-t-none m-b">From</h4>
                                <section class="box-forms mb-2">
                                    <div class="row">
                                        <div class="col-md-12 col-lg-12 col-sm-12 col-xs-12">
                                            @if ($setting->verify_status && isset($setting->use_sending_email) && $setting->use_sending_email == 1)
                                                <input type="text" id="from_email" class="form-control" value="{{ $setting->sending_email }}" disabled />
                                            @else
                                                <input type="text" id="from_email" class="form-control" value="{{ env('MAIL_FROM_ADDRESS') }}" disabled />
                                                <p class="text-note">
                                                    If you want to <span style="color: var(--text-body);"><strong>use your email address as the sender</strong></span>, click <a href="{{ route('settings.general') }}"><strong>HERE</strong></a> to enable that feature.
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                </section>

                                <h4 class="m-t-none m-b">To</h4>
                                <section class="box-forms mb-2">
                                    <div class="row mb-2">
                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
                                            <label for="recipients" class="form-label-line form-label-select">
                                                <span><strong>Select recipients</strong></span>
                                                <select name="recipients" id="recipients" class="form-control form-control-select label__pointer">
                                                    <option value="1" selected>Program(s) and specific affiliates</option>
                                                    <option value="2">Specific affiliates</option>
                                                    <option value="3">Program(s)</option>
                                                </select>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="row" translate="no">
                                        <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12 box-program">
                                            <label for="bulk_program" class="form-label-line form-label-select">
                                                <span>
                                                    <strong>Program
                                                    <i class="fal fa-info-circle text-success" data-toggle="tooltip" data-placement="top" data-original-title="When choosing a program, all affiliates on that program will receive your email"></i>
                                                    </strong>
                                                </span>

                                                <select multiple="multiple" name="bulk_program[]" id="bulk_program" class="form-control form-control-select label__pointer">
                                                    @foreach($programs as $program)
                                                        <option data-affiliate={{$program['affiliates_count']}} value="{{$program->id}}">{{$program->name}} ({{ $program['affiliates_count']. ' affiliates' }})</option>
                                                    @endforeach
                                                </select>
                                            </label>
                                        </div>

                                        <div class="col-lg-7 col-md-7 col-sm-7 col-xs-12 mt-xs-2 box-specific_affiliates d-flex align-items-center">
                                            <h4 class="box-specific_and">and</h4>
                                            <label class="control-label form-label-line flex-grow" for="affiliate_email">
                                                <span>
                                                    <strong>Send to specific affiliates</strong>
                                                    <i class="fal fa-info-circle text-success" data-toggle="tooltip" data-placement="top" data-original-title="Type affiliate's name/email then select from the suggestion to choose that affiliate."></i>
                                                </span>
                                                <input style="width: 100%;" type="text" id="affiliate_email" name="bulk_affiliate_email" placeholder="Email" class="form-control form-control-line">
                                            </label>
                                        </div>

                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 mt-1 box-message">
                                            <div class="alert alert-info mb-0">
                                                Affiliates who haven't logged in to their accounts or turned off email notification won't receive bulk email. The daily limit of bulk email for your plan is {{ \App\Models\SendMailLogs::getQuota(helperPlan()->planName()) }}.
                                            </div>
                                        </div>
                                    </div>
                                </section>

                                <h4 class="m-t-none m-b">Subject</h4>
                                <section class="box-forms mb-2">
                                    <div class="row">
                                        <div class="col-md-12 col-lg-12 col-sm-12 col-xs-12">
                                            <input type="text" class="form-control bulk_subject" name="bulk_subject" placeholder="Subject" required />
                                        </div>
                                    </div>
                                </section>
                            </div>

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms">
                                <div class="tabs-container nav-tab-collection">
                                    <ul class="nav nav-tabs d-flex">
                                        <li class="active">
                                            <a data-toggle="tab" href="#email_content" aria-expanded="false">
                                                Content
                                            </a>
                                        </li>
                                        <li class="">
                                            <a data-toggle="tab" href="#email_preview" aria-expanded="false">
                                                Preview&nbsp; <i class="fal fa-info-circle text-success tooltip-email" data-toggle="tooltip" data-placement="top" data-original-title="Kindly note that some of the content in this preview is sample. Your email will be automatically filled out with correct information once sent."></i>
                                            </a>
                                        </li>
                                    </ul>

                                    <div class="tab-content">
                                        <div class="tab-content">
                                            <div id="email_content" class="tab-pane active">
                                                <textarea class="bulk_content summernote" name="bulk_content" id="bulk_content-summernote" required></textarea>
                                            </div>
                                            <div id="email_preview" class="tab-pane"></div>
                                        </div>
                                    </div>
                                </div>
                                <p class="pull-right mb-0 mt-1 tags-list"><strong>Tags</strong>: {logo} &nbsp;&nbsp; {brand_name} &nbsp;&nbsp; {first_name} &nbsp;&nbsp; {last_name} &nbsp;&nbsp; {affiliate_link}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-forma"><i class="fa fa-paper-plane" aria-hidden="true"></i>&nbsp;&nbsp;Send emails</button>
            </form>
            <div class="bulk-template d-none">@include('app.pages.email.template.bulk_email')</div>
        </div>
    </div>
@endsection

@section('script_inline')
    <script src="{{cdn_asset('js/plugins/tinymce/6.1.0/tinymce.min.js')}}"></script>
    <script src="{{cdn_asset('js/plugins/typehead/bootstrap3-typeahead.min.js')}}"></script>
    <script src="{{cdn_asset('js/plugins/bootstrap-select/bootstrap-select.min.js')}}"></script>
    <script>
        $(function () {
            $('#bulk_content-summernote').val($('.bulk-template').html());
            tinymce.init($.extend({}, tinymceGlobalConfig,{
                selector:'#bulk_content-summernote',
            }));
            
            $('#bulk_program').selectpicker();                       

            // Preview email
            $('li a[href="#email_preview"]').click(function() {
                let logo           = '{{ $logo }}';
                let affiliate_link = '{{ $affiliate_link }}';
                let setting        = '{{ $setting->brand_name }}';
                let previewEmail   = tinymce.activeEditor.getContent();
                var contentPreview = '';
                $('#email_preview').html(previewEmail);
                $('#email_preview').html(function(index, html){
                    contentPreview = html.replaceAll('{logo}', `<img width="100%" src='${logo}' />`);
                    contentPreview = contentPreview.replaceAll('{logo}', `<img width="200" src='${logo}' />`);
                    contentPreview = contentPreview.replaceAll('{first_name}', `John`);
                    contentPreview = contentPreview.replaceAll('{last_name}', `Smith`);
                    contentPreview = contentPreview.replaceAll('{brand_name}', `${setting}`);
                    contentPreview = contentPreview.replaceAll('{affiliate_link}', `<a target="_blank" href="${affiliate_link}">${affiliate_link}</a>`);
                    return contentPreview;
                });
            });

            $('#affiliate_email').tagsinput({
                itemValue: 'id',
                itemText: function (item) {
                    return item.first_name + ' ' + item.last_name + '( ' + item.email + ' )';
                },
                typeahead: {
                    displayKey: 'text',
                    afterSelect: function (val) {
                        this.$element.val("");
                    },
                    source: function (query) {
                        return $.get('/admin/affiliates/search', {query: query});
                    }
                }
            });

            $('.btn-forma').on('click', function(){
                $('#bulk_content-summernote').val(tinymce.activeEditor.getContent());
            });

            $("#recipients").change(function() {
                let option = $(this).val();
                let box_program = $(".box-program");
                let box_specific_and = $(".box-specific_and");
                let box_specific_affiliates = $(".box-specific_affiliates");

                if (option == 2) {
                    box_program.hide(); box_specific_and.hide();
                    box_specific_affiliates.show().removeClass('col-md-7 col-lg-7 col-sm-7').addClass('col-md-12 col-lg-12 col-sm-12');
                } else if (option == 3) {
                    box_program.show();
                    box_specific_and.hide(); box_specific_affiliates.hide();

                }else {
                    box_program.show(); box_specific_and.show(); box_specific_affiliates.show().removeClass('col-md-12 col-lg-12 col-sm-12').addClass('col-md-7 col-lg-7 col-sm-7');
                }
            });
        });
    </script>
@endsection
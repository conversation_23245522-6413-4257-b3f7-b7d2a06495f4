@extends('affiliate.layouts.default')
@section('title', sca_trans( $groupLang,'gift',[], $locale ))

@section('page_heading')
    @include('app.includes.page_heading', [
        'title' => sca_trans($groupLang,'gift',[], $locale),
        'br' => 'affiliate.gift',
        'object'=> ['domain' => $domain, 'groupLang' => $groupLang, 'locale' => $locale]
    ])
@endsection

@section('css_addition')
    <link href="{{ cdn_asset('affbootstrap/css/aff-styleV2.min.css') }}" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css"
          href="{{ cdn_asset('css/plugins/daterangepicker/local/daterangepicker.css') }}"/>
    <link href="{{cdn_asset('css/affiliate/gift/style.min.css')}}" rel="stylesheet">
@endsection

@section('content')
    @if ($showBannerGiftCurrency)
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="block-currency">
                    <div class="alert alert-warning alert-dismissible" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>

                        <div>
                            <span class="alert-link d-flex" style="color: var(--text-body); gap: 4px; align-items: baseline;">
                                <i class="fal fa-exclamation-triangle text-warning" style="font-size: 12px;"></i>
                                <p  class="mb-0">
                                    {{ sca_trans( $groupLang,'your_gift_may_show',[], $locale ) }}
                                </p>
                            </span>
                        </div>

                        <div class="d-flex">
                            <i class="fal fa-exclamation-triangle" style="visibility: hidden;"></i>
                            <p class="mb-0" style="color: var(--text-body)">
                                {{ sca_trans( $groupLang,'your_merchant_has_changed',[], $locale ) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title" style="padding:0">
                    <div class="tabs-container affiliate-account__tabs login-tab" style="border:none;">
                        <ul class="nav nav-tabs affiliate-account__nav" style="border-bottom: none;">
                            <li class="active"><a class="tab-parent" data-toggle="tab" href="#tab_gift_list"
                                                  id="available-gift" style="border-bottom: 0 solid !important; font-size: 18px;">{{ sca_trans( $groupLang,'available_gift',[], $locale ) }}
                                                </a></li>
                        </ul>
                    </div>
                </div>
                <div class="tab-content">
                    <div id="tab_gift_list" class="tab-pane active">
                        <div class="ibox ibox-datatable-filter no-margins">
                            <div class="ibox-content-custom no-margins">
                                <div class="datatable-filter-container">
                                    <div class="col-md-8 col-lg-8 col-sm-8 col-xs-12 d-flex pd0-left-right affiliate-table-section__tool">
                                        <div class="col-md-5 col-sm-5 col-lg-5 col-xs-12 datatable-filter-item mb-xs-2">
                                            <div class="form-group">
                                                <label for="date-range-filter">{{ sca_trans( $groupLang,'date_and_time',[], $locale ) }}</label>
                                                <div id="date-range-filter">
                                                    <span></span>
                                                    <i class="fal fa-calendar-alt"></i>
                                                    <input type="hidden" id="start_date" value="">
                                                    <input type="hidden" id="end_date" value="">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4 col-sm-4 col-lg-4 col-xs-12 datatable-filter-item mb-xs-2">
                                            <div class="form-group">
                                                <label for="status-filter">{{ sca_trans( $groupLang,'status',[], $locale ) }}</label>
                                                <select class="form-control" id="status-filter">
                                                    <option value="0">{{ sca_trans( $groupLang,'all',[], $locale ) }}</option>
                                                    <option value="1">{{ sca_trans( $groupLang,'pending',[], $locale ) }}</option>
                                                    <option value="2">{{ sca_trans( $groupLang,'Unclaimed',[], $locale ) }}</option>
                                                    <option value="3">{{ sca_trans( $groupLang,'Claimed',[], $locale ) }}</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <button class="btn text-primary bg-info-overley mb-xs-2" id="search_btn">
                                            <strong>{{ sca_trans( $groupLang,'filter',[], $locale ) }}</strong>
                                        </button>
                                    </div>

                                    <div class="col-md-3 col-sm-3 col-lg-3 col-xs-12 datatable-filter-item datatable-filter-item-search">
                                        <div class="form-group">
                                            <label for="affiliate-search-datatable">{{ sca_trans( $groupLang,'search',[], $locale ) }}</label>
                                            <div>
                                                <input type="text" id="affiliate-search-datatable"
                                                       class="form-control"/>
                                                <i class="fal fa-search"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="ibox-content pd-lr0">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-hover" id="gift_datatable">
                                    <thead>
                                    <tr>
                                        <th>{{ sca_trans( $groupLang,'sent_date',[], $locale ) }}</th>
                                        <th>{{ sca_trans( $groupLang,'gift',[], $locale ) }}</th>
                                        <th>{{ sca_trans( $groupLang,'upcase_products',[], $locale ) }}</th>
                                        <th>{{ sca_trans( $groupLang,'product_selection',[], $locale ) }}</th>
                                        <th>{{ sca_trans( $groupLang,'gift_type',[], $locale ) }}</th>
                                        <th>{{ sca_trans( $groupLang,'gift_value',[], $locale ) }} <i class="fal fa-info-circle text-success i-gift" data-toggle="tooltip" data-placement="top" data-html="true" title="" data-original-title="{{ sca_trans( $groupLang,'general_gift_value',[], $locale ) }}"></i></th>
                                        <th>{{ sca_trans( $groupLang,'actual_claimed_value',[], $locale ) }}
                                            <i style="margin-left: 0 !important;" class="fal fa-info-circle text-success i-gift" data-toggle="tooltip"
                                               data-placement="top" data-html="true" title=""
                                               data-original-title="{{ sca_trans( $groupLang,'this_value_will',[], $locale ) }}"></i>
                                        </th>
                                        <th>{{ sca_trans( $groupLang,'status',[], $locale ) }}</th>
                                        <th>{{ sca_trans( $groupLang,'action_up',[], $locale ) }}</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal inmodal fade" id="link_email_modal" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <div class="ibox-tools">
                                <a class="close-link close close-modal" data-dismiss="modal">
                        <span class="mdiv">
                            <span class="md"></span>
                        </span>
                                </a>
                            </div>
                            <h4 class="modal-title box-text-content">{{ sca_trans( $groupLang,'claim_your_gift',[], $locale ) }} <span class="name-gift"></span></h4>
                            <small class="font-bold"></small>
                        </div>
                        <div class="modal-body" style="padding-bottom: 1px;">
                            <div class="ibox">
                                <div class="row ibox-content" style="margin: 0">
                                    <div class="col-xs-12 col-lg-12 col-sm-12 col-md-12" style="padding-left: 15px;">
                                        <h4 style="font-weight: 600;">{{ sca_trans( $groupLang,'use_this_link',[], $locale ) }}</h4>
                                    </div>
                                    <div class="col-lg-12 col-sm-12 col-md-12 col-xs-12" id="registration-form">
                                        <div class="input-group m-b">
                                            <input id="link_draft_order" type="text" class="form-control link-custom-domain" name="link_register" value="" readonly="">
                                            <span id="btn_copy_register_link"  style="padding: 10px;" class="input-group-addon" onclick="copy('link_draft_order', 'btn_copy_register_link')">{{ sca_trans( $groupLang,'copy_link',[], $locale ) }}</span>
                                        </div>

                                        <div class="text text-default" style="margin-bottom: 10px; line-height: 22px; font-size: 12px; font-style: italic;">
                                            {{ sca_trans( $groupLang,'need_help',[], $locale ) }} <a class="click-here" style="font-size: 12px;color: #1D85E8;text-decoration: none;">{{ sca_trans( $groupLang,'contact',[], $locale ) }} <span id="contact_email" style="color: #1D85E8"></span> </a>
                                            <br>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white close-link close-modal" data-dismiss="modal">{{ sca_trans( $groupLang,'close',[], $locale ) }}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('affiliate.components.modals.gift_claim_modal')
@endsection

@section('script_inline')
    <script src="{{ cdn_asset('js/app/jquery-confirm.min.js') }}"></script>
    <script type="text/javascript" src="{{ cdn_asset('js/plugins/fullcalendar/moment.min.js') }}"></script>
    <script src="{{cdn_asset('js/plugins/momentjs/moment-with-locales.min.js')}}"></script>
    <script type="text/javascript" src="{{ cdn_asset('js/plugins/datapicker/daterangepicker.min.js') }}"></script>
    <script src="{{ cdn_asset('js/plugins/debounce-search/debounce-search.min.js') }}"></script>
    <script src="{{cdn_asset('js/plugins/typehead/bootstrap3-typeahead.min.js')}}"></script>
    <script type="text/javascript">
        moment.locale('{{$locale == 'cn' ? 'zh-cn' : $locale}}');

        var giftProductList = [];
        const titleLanguage = '{{ sca_trans( $groupLang,'complete_your_gift',[], $locale ) }}';

        var datePickerRange = {
            '{{ sca_trans( $groupLang,"today",[], $locale ) }}': [moment(), moment()],
            '{{ sca_trans( $groupLang,"yesterday",[], $locale ) }}': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            '{{ sca_trans( $groupLang,"last_7_days",[], $locale ) }}': [moment().subtract(6, 'days'), moment()],
            '{{ sca_trans( $groupLang,"last_30_days",[], $locale ) }}': [moment().subtract(29, 'days'), moment()],
            '{{ sca_trans( $groupLang,"this_month",[], $locale ) }}': [moment().startOf('month'), moment().endOf('month')],
            '{{ sca_trans( $groupLang,"last_month",[], $locale ) }}': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }

        var startDate = '';
        var endDate = '';
        const dateRangePicker = `#date-range-filter`;

        function chartDateRangeCallback(start, end) {
            if (start && end) {
                $('#date-range-filter span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
                startDate = start;
                endDate = end;
                $('#start_date').val(start.format('YYYY-MM-DD'));
                $('#end_date').val(end.format('YYYY-MM-DD'));
            } else {
                startDate = '';
                endDate = '';
                $('#start_date').val('');
                $('#end_date').val('');
            }
        }

        $('#date-range-filter').daterangepicker({
            ranges: datePickerRange,
            "locale": {
                "customRangeLabel": "{{ sca_trans( $groupLang,'custom_range',[], $locale ) }}",
                "applyLabel": "{{ sca_trans( $groupLang, 'apply',[], $locale ) }}",
                "cancelLabel": "{{ sca_trans( $groupLang, 'cancel',[], $locale ) }}"
            }
        }, chartDateRangeCallback);
                chartDateRangeCallback(null, null);

        function buildQuery() {
            var from = startDate === '' ? '' : startDate.unix();
            var to = endDate === '' ? '' : endDate.unix();
            var status = $('#status-filter').val() || 'all';
            return `?from=${from}&to=${to}&status=${status}`;
        }

        var giftDatatable = $('#gift_datatable').DataTable({
            pageLength: 10,
            responsive: true,
            scrollX: true,
            scrollCollapse: true,
            processing: true,
            serverSide: false,
            searching: true,
            order: [[0, "asc"]],
            dom: '<"datatable_toolbar"><"html5buttons"B>lTfgtip',
            "drawCallback": function (settings) {
                $('.i-check').iCheck({
                    checkboxClass: 'icheckbox_square-blue',
                    radioClass: 'iradio_minimal-red',
                    increaseArea: '20%' // optional
                });
                var triggeredByChild = false;
                $('#check_all_conversion').on('ifUnchecked', function (event) {
                    if (!triggeredByChild) {
                        $('.checkbox_conversion').iCheck('uncheck');
                    }

                    triggeredByChild = false;
                });

                $('[data-toggle="tooltip"]').tooltip();
            },
            createdRow: function (row) {
                $(row).find('td:eq(0)').attr('translate', 'no');
            },
            ajax: {
                url: "{{ route('aff.gifts.datatables', ['domain' => $domain] )}}" + buildQuery(),
            },
            language: {!! json_encode(config("myconfig.datatables_language.".$locale)) !!},
            buttons: [],
            columnDefs: [
                {width: "12%", targets: 0, orderable: true, className: "text-center data-content", searchable: false},
                {width: "10%", targets: 1, orderable: false, className: "text-center box-image data-content", searchable: true},
                {width: "20%", targets: 2, orderable: false, className: "text-center data-content", searchable: true},
                {width: "12%", targets: 3, orderable: false, className: "text-center data-content", searchable: false},
                {width: "10%", targets: 4, orderable: false, className: "text-center data-content", searchable: false},
                {width: "8%", targets: 5, orderable: false, className: "text-center data-content", searchable: false},
                {width: "7%", targets: 6, orderable: false, className: "text-center data-content", searchable: false},
                {width: "10%", targets: 7, orderable: false, className: "text-center data-content", searchable: false},
                {width: "11%", targets: 8, orderable: false, className: "text-center data-content action", searchable: false},

            ],
            columns: [
                {
                    data: 'created_at', name: 'created_at', render: function (data, type, row) {
                        return '<span>' + moment.unix(data).format('lll') + '</span>';
                    }
                },
                {
                    data: 'gift.name', name: 'gift.name', render: function (data, type, row) {
                        if (data == null || row.gift.gift_products[0] == null) {
                            return '';
                        }

                        let images = row.gift.gift_products.map(_e => _e.image);

                        return '<img class="img-responsive" src="' + images[0] + '">'  + '<p style="text-align: center;padding-top: 5px;">' + data + '</p>';
                    }
                },
                {
                    data: 'gift.gift_products',
                    name: 'gift.gift_products',
                    render: function (data, type, row) {
                        const MAX_VISIBLE = 3;
                        let toggleLink =  ``;
                        let total = data.length;
                        if (!data || total === 0) {
                            return '';
                        }
                        const shopDomain = $('meta[name="up:shopDomain"]').attr('content');

                        // Render MAX_VISIBLE
                        let visibleItems = data.slice(0, MAX_VISIBLE).map(_cur => {
                            let url = `https://${shopDomain}/products/${_cur.slug}`;
                            return `<i class="fas fa-circle" style="color: #2f4050;font-size: 6px !important;margin: 0 6px;"></i><a style="text-decoration: underline;" href="${url}" target="_blank">${_cur.product_name}</a><br>`;
                        }).join('');
                        //Render DEFAULT MAX_VISIBLE
                        if (total <= MAX_VISIBLE) {
                            return visibleItems;
                        }

                        // > 3 ITEM => HIDDEN
                        let hiddenItems = data.slice(MAX_VISIBLE).map(_cur => {
                            let url = `https://${shopDomain}/products/${_cur.slug}`;
                            return `<i class="fas fa-circle" style="color: #2f4050;font-size: 6px !important;margin: 0 6px;"></i>
        <a style="text-decoration: underline;word-break:break-all;" href="${url}" target="_blank">${_cur.product_name}</a><br>
      `;
                        }).join('');

                        //  <div> display:none
                        let hiddenDiv = `<div class="hidden-products" style="display: none;">${hiddenItems}</div>`;

                        // data-expanded = false
                        toggleLink = `
                          <a href="javascript:void(0);"
                             class="toggle-products"
                             data-expanded="false"
                             style="text-align: center;color:rgb(9, 44, 76); font-weight: 600;display:inline-block;margin-top:4px;">
                             {{ sca_trans( $groupLang,"see_more",[], $locale ) }} <i class="fas fa-caret-down"></i>
                          </a>
                        `;
                        return visibleItems + hiddenDiv + toggleLink;
                    }
                },
                {
                    data: 'gift.max_number_products',
                    name: 'gift.max_number_products',
                    render: function (data, type, row) {
                        product = row.gift.gift_products.length;
                        dataText1 = '{{ sca_trans( $groupLang,"choose",[], $locale ) }} ';
                        dataText2 = ' {{ sca_trans( $groupLang,"of",[], $locale ) }} ';
                        dataText3 = ' {{ sca_trans( $groupLang,"products",[], $locale ) }}';
                        return '<p >' + dataText1 + data  + dataText2 +  product + dataText3 + '</p>';
                    }
                },
                {
                    data: 'gift.type', name: 'gift.type', render: function (data, type, row) {
                        if(row.gift.type == 1 && row.gift.include_shipping_fee == 0)
                        {
                            textType = '{{ sca_trans( $groupLang,"free_gift_shipping_included",[], $locale ) }}' ;
                        }else if(row.gift.type == 1 && row.gift.include_shipping_fee == 1)
                        {
                            textType = '{{ sca_trans( $groupLang,"free_gift_shipping_not_included",[], $locale ) }}' ;
                        }else if(row.gift.type == 2 && row.gift.include_shipping_fee == 0)
                        {
                            textType = '{{ sca_trans( $groupLang,"discounted_shipping_included",[], $locale ) }}' ;
                        } else if (row.gift.type == 2 && row.gift.include_shipping_fee == 1)
                        {
                            textType = '{{ sca_trans( $groupLang,"discounted_shipping_not_included",[], $locale ) }}' ;
                        } else {
                            textType = '';
                        }

                        return '<span>' + textType + '</span>';
                    }
                },
                {
                    data: 'gift.gift_products', name: 'gift.gift_products', render: function (data, type, row) {
                        let totalPrice = 0;
                        if(data)
                        totalPrice = data.reduce((totalPrice, _cur) => {
                            totalPrice += (_cur.price * _cur.quantity);
                            return totalPrice;
                        }, 0);

                        return '<strong>' + Shopify.formatMoney(totalPrice.toFixed(2), currencyFormat) + '</strong>';
                    }
                },
                {
                    data: 'gif_value', name: 'gif_value', render: function (data, type, row) {
                        if (row.amount == null || row.status != "2") {
                            return '';
                        } else {
                            return '<strong>' + Shopify.formatMoney(row.amount, currencyFormat) + '</strong>';
                        }
                    }
                },
               {
                    data: 'status_text', name: 'status_text', render: function (data, type, row) {
                        let statusClass = 'status-' + data;
                        let statusText = '';
                        
                        if (data === 'pending') {
                            statusText = '{{ sca_trans($groupLang, "pending", [], $locale) }}';
                        } else if (data === 'unclaimed') {
                            statusText = '{{ sca_trans($groupLang, "Unclaimed", [], $locale) }}';
                        } else if (data === 'claimed') {
                            statusText = '{{ sca_trans($groupLang, "Claimed", [], $locale) }}';
                        } else {
                            statusText = data;
                        }
                        
                        return ` <div class="row d-flex mg0-left-right"><div class="icon-frame"><div class="${statusClass}">${statusText}</div></div>`;
                    }
                },
            {
                data: 'id', name: 'id', render: function (data, type, row) {
                    let status = row.status_text;
                    
                    if (status === 'pending') {
                        return `<div class="row d-flex mg0-left-right"><div class="icon-frame"><button data-toggle="tooltip" data-placement="top" style="margin-right: 4px;" class="open-modal-order btn-select-product" data-choose="${row.gift.max_number_products}" data-id="${data}">{{ sca_trans($groupLang,"select_product", [], $locale) }}</button></div>`;
                    } else if (status === 'unclaimed') {
                        return `<div class="row d-flex mg0-left-right"><div class="icon-frame"><a data-toggle="tooltip" data-placement="top" style="margin-right: 4px;" class="open-modal-link btn-send" data-id="${data}">{{ sca_trans($groupLang,"claim", [], $locale) }}</a></div>`;
                    } else if (status === 'claimed') {
                        return '';
                    } else {
                        return '';
                    }
                }
            }
            ]
        });

        $(document).ready(function () {
            $('#affiliate-search-datatable').debounceSearch({
                callback: () => giftDatatable.search($('#affiliate-search-datatable').val()).draw()
            });
            $(document).on('click', '.toggle-products', function(e) {
                e.preventDefault();
                let $this = $(this);
                let expanded = $this.attr('data-expanded') === 'true';

                if(!expanded) {
                    $this.prev('.hidden-products').slideDown();
                    $this.html('{{ sca_trans( $groupLang,"see_less",[], $locale ) }} <i class="fas fa-caret-up"></i>');
                    $this.attr('data-expanded', 'true');
                } else {
                    $this.prev('.hidden-products').slideUp();
                    $this.html('{{ sca_trans( $groupLang,"see_more",[], $locale ) }} <i class="fas fa-caret-down"></i>');
                    $this.attr('data-expanded', 'false');
                }
            });

            $(document).on('click', '#search_btn', function () {
                let url = "{{ route('aff.gifts.datatables', ['domain' => $domain] )}}" + buildQuery();
                giftDatatable.ajax.url(url).load();
            }).on('click', '.close-modal', function () {
                $('.modal-backdrop').removeClass('modal-backdrop');
            }).on('click', '.close-link', function () {
                $(".modal-backdrop").removeClass('modal-backdrop');
            }).on('click', '.close-modal', function () {
                let url = "{{ route('aff.gifts.datatables', ['domain' => $domain] )}}" + buildQuery();
                giftDatatable.ajax.url(url).load();
            }).on('click', '#draft-order', function () {
                var id = $('#gift_aff_id').val();
                var form = $('#form-gift-aff')[0];
                var data = new FormData(form);
                $("#draft-order").addClass('sk-loading');

                $(`a[data-id=${id}]`).prop( "disabled", true );
                $.ajax({
                    type: "POST",
                    enctype: 'multipart/form-data',
                    url: '/{{$domain}}/gifts/store/' + id,
                    data: data,
                    processData: false,
                    contentType: false,
                    cache: false,
                    success: function (data) {
                        $("#output").text(data);
                        $("#btnSubmit").prop("disabled", false);
                        if(data.status){
                            $("#draft-order").removeClass('sk-loading');
                            $('#detail_order').modal('hide');
                            $('#contact_email').text(data.contact.sending_email);
                            $('.click-here').attr("href",'mailto:' + data.contact.sending_email );
                            $('#link_draft_order').val(data.data.draft_link);
                            // setTimeout(() => $('#link_email_modal').modal('show'), 400);
                            window.open(data.data.draft_link, '_blank');
                        }else{
                            // hien loi theo tung product
                            data.failInfo.map(_e => {
                                $('[data-v-index="' + _e.vIndex + '"]').text(_e.message);
                                $('#error-content').text(_e.message);
                            });

                            $('#detail_order').modal('show');
                        }
                    },
                    error: function (e) {
                        $('.text-error').text(e.responseJSON.message);
                        $('.text-error').removeClass('d-none');
                        $("#output").text(e.responseText);
                        $("#btnSubmit").prop("disabled", false);
                    },complete: function(data) {
                        let url = "{{ route('aff.gifts.datatables', ['domain' => $domain] )}}" + buildQuery();
                        giftDatatable.ajax.url(url).load();
                        $(`a[data-id=${id}]`).prop("disabled", false);
                        $('.modal-backdrop').removeClass('modal-backdrop');
                    }
                });
            })
        }).on('hidden.bs.modal', '#link_email_modal', function (){
            $('.modal-backdrop').removeClass('modal-backdrop');
        });

        $(document).on('click', '.open-modal-order', function () {
            var id = $(this).data('id');
            $('#send_gift_btn').attr('data-id', id);
            showingReferralId = id;
            $('#draft-order').attr('disabled', true);
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                type: 'GET',
                url: '/{{$domain}}/gifts/' + id,
                dataType: 'json',
                success: function (results) {
                    if (results.status) {
                        $('.gift-name').text(results.listProduct[0].gift.name);
                        giftProductList = results.listProduct;
                        $('.claim-product').html(results.listProductView);
                        $('#detail_order').modal('toggle');

                        if (giftProductList[0].gift.max_number_products == giftProductList.length) {
                            $(document).find('.select-product:checkbox').prop('checked', true);
                            $('.select').attr("style", "display: none !important");
                        }

                        calcSelectedProduct();
                        $('.select-product').trigger('change');

                        $(document).find('.claim-product select.select-variant').toArray().map(_item => priceVariantByShopify.call(_item));
                    } else {
                        swal({
                            title: "Error",
                            text: results.message
                        });
                    }
                },
                fail: function (jqXHR, textStatus, errorThrown) {
                    console.log('Could not get posts, server response: ' + textStatus + ': ' + errorThrown);
                }
            });
        }).on('click', '.open-modal-link', function () {
            var id = $(this).data('id');
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                type: 'GET',
                url: '/{{$domain}}/gifts/get_link/' + id,
                dataType: 'json',
                success: function (results) {
                    if (results.status) {
                        setTimeout(() => $('#link_email_modal').modal('show'), 400);
                        $('#contact_email').text(results.contact.sending_email);
                        $('.click-here').attr("href",'mailto:' + results.contact.sending_email );
                        $('#link_draft_order').val(results.data.draft_link);
                        $('#link_email_modal').modal('show');
                        $('.name-gift').text(results.data.name);
                        $('.box-text-content').text(titleLanguage);
                    } else {
                        swal({
                            title: "Error",
                            text: results.message
                        });
                    }

                },
                fail: function (jqXHR, textStatus, errorThrown) {
                    console.log('Could not get posts, server response: ' + textStatus + ': ' + errorThrown);
                }
            });
        }).on('change', 'select.select-variant', function (){
            priceVariantByShopify.call(this);
        });

        function priceVariantByShopify() {
            let pContainer = $(this).closest('div[data-product-id]');
            let pId = $(pContainer).data('product-id');

            // Lấy tất cả các lựa chọn biến thể đã chọn trong sản phẩm
            let variantsSelected = $(pContainer).find('select.select-variant').toArray().map(_item => {
                return {
                    option: $(_item).data('variant-option'),
                    value: $(_item).val(),
                };
            });

            let newPrice = 0;
            giftProductList = giftProductList.map(_item => {
                if (_item.product_id == pId) {
                    if (_item.variantProduct && _item.variantProduct.length > 0) {
                        // Khớp các variants_selected với variantProduct
                        let matchedVariant = _item.variantProduct[0].variants.find(variant => {
                            return variantsSelected.every(selected => {
                                return variant.selectedOptions.some(option => {
                                    return option.name == selected.option && option.value == selected.value;
                                });
                            });
                        });

                        newPrice = matchedVariant ? matchedVariant.price : _item.price;

                        return {
                            ..._item,
                            price: newPrice,
                            variants_selected: variantsSelected
                        };
                    }

                    newPrice = _item.price; // Gán giá mặc định
                    return {
                        ..._item,
                        price: newPrice,
                        variants_selected: variantsSelected
                    };
                }
                return _item;
            });

            $(pContainer).find('.content-price').text(Shopify.formatMoney(Number(newPrice), currencyFormat));
            let result = calcGiftValue();
            $('#subtotal-claim').val(result.subtotal);
            $('.subtotal').text(Shopify.formatMoney(result.subtotal.toFixed(2), currencyFormat));
            $('.discount').text(Shopify.formatMoney(result.discount.toFixed(2), currencyFormat));
            $('.estimated').text(Shopify.formatMoney(result.estimated.toFixed(2), currencyFormat));

            calcSelectedProduct();
        }


        $(document).on('hidden.bs.modal', '#detail_order', function () {
            $('.subtotal').text(Shopify.formatMoney(0, currencyFormat));
            $('.discount').text(Shopify.formatMoney(0, currencyFormat));
            $('.estimated').text(Shopify.formatMoney(0, currencyFormat));
            $('.text-error').addClass('d-none');
        });

        $(document).on('change', '.select-product', function () {
            let result = calcGiftValue();
            $('#subtotal-claim').val(result.subtotal);
            $('.subtotal').text(Shopify.formatMoney(result.subtotal.toFixed(2), currencyFormat));
            $('.discount').text(Shopify.formatMoney(result.discount.toFixed(2), currencyFormat));
            $('.estimated').text(Shopify.formatMoney(result.estimated.toFixed(2), currencyFormat));

            calcSelectedProduct();
        });

        function calcSelectedProduct() {
            let result = {
                totalProduct: 0,
                selectedProduct: 0,
                maxSelectedProduct: giftProductList[0].gift.max_number_products ,
            };

            result.totalProduct = giftProductList.length;

            result = $('.select-product').toArray().reduce((result, cur) => {
                if ($(cur).prop('checked')) {
                    result['selectedProduct'] += 1;
                }
                return result;
            }, result);

            if (result.selectedProduct > 0)
            {
                $('#draft-order').attr('disabled', false);
            } else {
                $('#draft-order').attr('disabled', true);
            }

            if (result.selectedProduct >=  result.maxSelectedProduct){
                $('input[type="checkbox"]:not(:checked)').attr('disabled', true);
            } else {
                $('input[type="checkbox"]:not(:checked)').attr('disabled', false);
            }


            $('.choose').text(result.maxSelectedProduct);
            $('.choose-of').text(result.totalProduct);
            return result;
        }

        function calcGiftValue() {
            let result = {
                subtotal: 0,
                discount: 0,
                estimated: 0
            };

            result = $('.select-product').toArray().reduce((result, cur) => {
                if ($(cur).prop('checked')) {
                    let id = $(cur).data('id');
                    let product = giftProductList.filter(_p => _p.id == id)[0];
                    result['subtotal'] += product.price * product.quantity;

                    if (product.gift.type === 1) {
                        result['discount'] = result['subtotal'];
                    } else if (product.gift.type === 2) {
                        let gift_amount = JSON.parse(product.gift.gift_amounts);

                        gift_amount.amount = Number(gift_amount.amount);
                        gift_amount.type = Number(gift_amount.type);

                        if (gift_amount.type === 0) {
                            result['discount'] += gift_amount.amount * ((product.price * product.quantity) / 100);
                        } else if (gift_amount.type === 1) {
                            result['discount'] += gift_amount.amount;
                        }
                    }
                }
                return result;
            }, result);

            result['estimated'] = result['subtotal'] - result['discount'];

            result['estimated'] = result['estimated'] < 0 ? 0 : result['estimated'];

            return result;
        }

        function listProducts(data) {
            let product = data.data;

            $('.gift_list_product .list').html('');

            product.map(_e => {
                renderProductsListItem(_e);
            })
        }


        function renderProductsListItem(product) {
            let pListItem = $('.product-list-temp').clone();

            $(pListItem).find('h4.product-name').text(product.product_name);
            $(pListItem).find('img.product-img').attr('src', product.image);
            $(pListItem).find('span.price').text(Shopify.formatMoney(product.price, currencyFormat));
            $(pListItem).find('span.quantity').text('{{ sca_trans( $groupLang,"quantity:",[], $locale ) }}' + ' ' + product.quantity);

            JSON.parse(product.addition_info).map((_o, _oIndex) => {
                $(pListItem).find('span.option' + _o.position).text((_oIndex ? ' , ' : '') + _o.name + ': ');
                $(pListItem).find('span.option-select' + _o.position).text(_o.selected);
            });

            pListItem.removeClass('d-none');
            pListItem.removeClass('product-list-temp');

            $('.gift_list_product .list').append(pListItem);
        }
    </script>
@endsection
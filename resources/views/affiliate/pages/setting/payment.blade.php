@extends('affiliate.layouts.default')
@section('title', sca_trans($groupLang, 'payment_settings', [], $locale))

@section('page_heading')
    @include('app.includes.page_heading', [
        'title' => sca_trans($groupLang, 'payment', [], $locale),
        'br' => 'affiliate.settings.payment',
        'object' => ['domain' => $domain, 'groupLang' => $groupLang, 'locale' => $locale],
    ])
@endsection

@section('css_addition')
    <meta name="aff:shop_id" content="{{ $merchantSetting->shop_id }}">
    <link href="{{ cdn_asset('css/affiliate/settings/settings-all.min.css') }}" rel="stylesheet">
    <link href="{{ cdn_asset('css/affiliate/settings/payment.min.css') }}" rel="stylesheet">
    <link href="{{ cdn_asset('css/affiliate/settings/select-currency.css') }}" rel="stylesheet">
    <link href="{{ cdn_asset('css/plugins/select2/select2.min.css') }}" rel="stylesheet">
@endsection

@section('content')
    <div class="row">
        @include('affiliate.includes.setting_sidebar')

        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12 animated fadeInRight">
            <form class="form-horizontal" id="payment_setting" method="POST"
                action="{{ route('aff.setting.payment.update', ['domain' => $domain, 'id' => $setting->id]) }}">
                {!! csrf_field() !!}

                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if (session()->has('ok'))
                    @include('affiliate.includes.error', [
                        'type' => 'success',
                        'title' => sca_trans($groupLang, 'success', [], $locale),
                        'message' => session('ok'),
                    ])
                @endif

                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h3>{{ sca_trans($groupLang, 'payment', [], $locale) }}</h3>
                    </div>

                    <div class="ibox-content" style="display:flex;gap:20px;">
                        <div class="ibox-content-left">
                            <div class="row">
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms box-forms-response">
                                    <label for="payment_method_sl" class="form-label-line form-label-select">
                                        <span><strong>{{ sca_trans($groupLang, 'payment_method', [], $locale) }}</strong></span>
                                        <select class="form-control form-control-select label__pointer"
                                            name="payment_method" id="payment_method_sl">
                                            <option value="none" @if (!$setting->payment_method) selected @endif>
                                                {{ sca_trans($groupLang, 'no_payment_method', [], $locale) }}</option>
                                            @foreach ($paymentSupport as $k => $v)
                                                @if ($k == 'other' && $merchantSetting->other_payment_method_name != null)
                                                    <option value="{{ $k }}"
                                                        @if ($setting->payment_method == $k) selected @endif>
                                                        {{ $merchantSetting->other_payment_method_name }}</option>
                                                @else
                                                    @switch($k)
                                                        @case('bank')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'bank_transfer', [], $locale) }}</option>
                                                        @break

                                                        @case('paypal')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'paypal', [], $locale) }}</option>
                                                        @break

                                                        @case('debit')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'debit_card', [], $locale) }}</option>
                                                        @break

                                                        @case('cheque')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'cheque', [], $locale) }}</option>
                                                        @break

                                                        @case('upi')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'upi', [], $locale) }}</option>
                                                        @break

                                                        @case('venmo')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'venmo', [], $locale) }}</option>
                                                        @break

                                                        @case('paytm')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'pay_tm', [], $locale) }}</option>
                                                        @break

                                                        @case('store_credit')
                                                            <option value="{{ $k }}"
                                                                @if ($setting->payment_method == $k) selected @endif>
                                                                {{ sca_trans($groupLang, 'store_credit', [], $locale) }}</option>
                                                        @break
                                                    @endswitch
                                                @endif
                                            @endforeach
                                        </select>
                                    </label>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms box-forms-response">
                                    <div class="payment-info-input">

                                    </div>
                                </div>
                            </div>

                            @if ($canUseTax)
                                <div class="row mt-2">
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 box-forms box-forms-response">
                                        <label for="tax_percent" class="form-label-line">
                                            <span><strong>{{ sca_trans($groupLang, 'tax', [], $locale) }}</strong></span>
                                            <div class="input-group">
                                                <input name="tax_percent" step="any" id="tax_percent" required
                                                    type="number" min="0" max="100" pattern="^[0-9]{1,8}$"
                                                    title="Input number is required" class="form-control form-control-line"
                                                    value="{{ $tax_value ? $tax_value : 0 }}" />
                                                <span class="input-group-addon border-none">%</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="ibox-content-right">
                            <div id="wise-form-container" style="display: none;"></div>
                            <!-- Template for standard bank form (hidden) -->
                            <div id="standard-bank-form-template" class="d-none">
                                <h3 style="text-align: center;">Bank account info</h3>
                                @if ($merchantSetting->shop_id != 39632 && $merchantSetting->shop_id != 39894)
                                    <label for="account_type_alt" class="form-label-line form-label-select mb-2">
                                        <span><strong>{{ sca_trans($groupLang, 'status', [], $locale) }}</strong></span>
                                        <select class="form-control form-control-select label__pointer" name="account_type"
                                            id="account_type_alt">
                                            <option value="checking" @if (
                                                $setting->payment_method == 'bank' &&
                                                    isset($paymentInfo['account_type']) &&
                                                    $paymentInfo['account_type'] == 'checking') selected="" @endif>
                                                {{ sca_trans($groupLang, 'checking', [], $locale) }}</option>
                                            <option value="saving" @if (
                                                $setting->payment_method == 'bank' &&
                                                    isset($paymentInfo['account_type']) &&
                                                    $paymentInfo['account_type'] == 'saving') selected="" @endif>
                                                {{ sca_trans($groupLang, 'savings', [], $locale) }}</option>
                                        </select>
                                    </label>
                                @endif
                                <input type="text" class="form-control mb-2" name="name"
                                    @if ($setting->payment_method == 'bank' && isset($paymentInfo['name'])) value="{{ $paymentInfo['name'] }}" @endif
                                    placeholder="{{ sca_trans($groupLang, 'bank_name', [], $locale) }}">
                                <input type="text" class="form-control mb-2" name="account"
                                    @if ($setting->payment_method == 'bank' && isset($paymentInfo['account'])) value="{{ $paymentInfo['account'] }}" @endif
                                    placeholder="{{ sca_trans($groupLang, 'account_name', [], $locale) }}">
                                <input type="text" class="form-control mb-2" name="number"
                                    @if ($setting->payment_method == 'bank' && isset($paymentInfo['number'])) value="{{ $paymentInfo['number'] }}" @endif
                                    placeholder="{{ sca_trans($groupLang, 'account_number', [], $locale) }}">
                                <input type="text" class="form-control mb-2" name="branch"
                                    @if ($setting->payment_method == 'bank' && isset($paymentInfo['branch'])) value="{{ $paymentInfo['branch'] }}" @endif
                                    placeholder="{{ sca_trans($groupLang, 'branch_code', [], $locale) }}">
                                <input type="text" class="form-control" name="swift"
                                    @if ($setting->payment_method == 'bank' && isset($paymentInfo['swift'])) value="{{ $paymentInfo['swift'] }}" @endif
                                    placeholder="{{ sca_trans($groupLang, 'swift_code', [], $locale) }}"
                                    @if ($merchantSetting->shop_id == 39632 || $merchantSetting->shop_id == 39894) required="required" @endif>
                                @if ($merchantSetting->shop_id == 39632 || $merchantSetting->shop_id == 39894)
                                    <input type="text" class="form-control mb-2" name="iban"
                                        @if ($setting->payment_method == 'bank' && isset($paymentInfo['iban'])) value="{{ $paymentInfo['iban'] }}" @endif
                                        placeholder="{{ sca_trans($groupLang, 'iban_code', [], $locale) }}"
                                        required="required">
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12">
                        <button class="btn btn-primary" type="submit"
                            data-loading-text="<i class='fal fa-spinner fa-spin'></i> {{ sca_trans($groupLang, 'loading', [], $locale) }}">
                            {{ sca_trans($groupLang, 'save_changes', [], $locale) }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="payment-info-input-template d-none">
        @foreach ($paymentSupport as $paymentMethodName => $v)
            @switch($paymentMethodName)
                @case('paypal')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        <label for="email" class="form-label-line mt-2">
                            <span><strong>{{ sca_trans($groupLang, 'paypal_email', [], $locale) }}</strong></span>
                            <input type="text" class="form-control form-control-line"
                                placeholder="{{ sca_trans($groupLang, 'paypal_email', [], $locale) }}" name="email"
                                id="email" @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['email'])) value="{{ $paymentInfo['email'] }}" @endif
                                required="">
                        </label>
                    </div>
                @break

                @case('bank')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        {{-- @if ($merchantSetting->shop_id != 39632 && $merchantSetting->shop_id != 39894)
                            <label for="account_type" class="form-label-line form-label-select my-2">
                                <span><strong>{{ sca_trans($groupLang, 'status', [], $locale) }}</strong></span>
                                <select class="form-control form-control-select label__pointer" name="account_type"
                                    id="account_type">
                                    <option value="checking" @if (
                                        $setting->payment_method == $paymentMethodName &&
                                            isset($paymentInfo['account_type']) &&
                                            $paymentInfo['account_type'] == 'checking') selected="" @endif>
                                        {{ sca_trans($groupLang, 'checking', [], $locale) }}</option>
                                    <option value="saving" @if (
                                        $setting->payment_method == $paymentMethodName &&
                                            isset($paymentInfo['account_type']) &&
                                            $paymentInfo['account_type'] == 'saving') selected="" @endif>
                                        {{ sca_trans($groupLang, 'savings', [], $locale) }}</option>
                                </select>
                            </label>
                        @endif
                        <input type="text" class="form-control mb-2" name="name"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['name'])) value="{{ $paymentInfo['name'] }}" @endif
                            placeholder="{{ sca_trans($groupLang, 'bank_name', [], $locale) }}">
                        <input type="text" class="form-control mb-2" name="account"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['account'])) value="{{ $paymentInfo['account'] }}" @endif
                            placeholder="{{ sca_trans($groupLang, 'account_name', [], $locale) }}">
                        <input type="text" class="form-control mb-2" name="number"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['number'])) value="{{ $paymentInfo['number'] }}" @endif
                            placeholder="{{ sca_trans($groupLang, 'account_number', [], $locale) }}">
                        <input type="text" class="form-control mb-2" name="branch"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['branch'])) value="{{ $paymentInfo['branch'] }}" @endif
                            placeholder="{{ sca_trans($groupLang, 'branch_code', [], $locale) }}">
                        <input type="text" class="form-control" name="swift"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['swift'])) value="{{ $paymentInfo['swift'] }}" @endif
                            placeholder="{{ sca_trans($groupLang, 'swift_code', [], $locale) }}"
                            @if ($merchantSetting->shop_id == 39632 || $merchantSetting->shop_id == 39894) required="required" @endif>
                        @if ($merchantSetting->shop_id == 39632 || $merchantSetting->shop_id == 39894)
                            <input type="text" class="form-control mb-2" name="iban"
                                @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['iban'])) value="{{ $paymentInfo['iban'] }}" @endif
                                placeholder="{{ sca_trans($groupLang, 'iban_code', [], $locale) }}" required="required">
                        @endif --}}

                        <label for="currency" class="form-label-line-select my-2">
                            <span><strong
                                    class="currency-text">{{ sca_trans($groupLang, 'currency', [], $locale) }}</strong></span>

                            <select class="form-control" id="currency" name="currency">
                                <option value="">Select Currency</option>
                                @foreach ($currencies as $code => $currency)
                                    <option value="{{ $currency['code'] }}" @if (
                                        $setting->payment_method == $paymentMethodName &&
                                            isset($paymentInfo['currency']) &&
                                            $paymentInfo['currency'] == $code) selected @endif
                                        data-is-wise="{{ in_array($code, $wiseCurrencies) ? 'true' : 'false' }}"
                                        title="{{ is_array($currency) ? $currency['name'] : $currency }}">
                                        {{ $code }} - {{ is_array($currency) ? $currency['name'] : $currency }}
                                    </option>
                                @endforeach
                            </select>
                        </label>

                    </div>
                @break

                @case('debit')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        <input type="text" class="form-control my-2"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['name'])) value="{{ $paymentInfo['name'] }}" @endif name="name"
                            placeholder="{{ sca_trans($groupLang, 'name', [], $locale) }}" required="">
                        <input type="text" class="form-control card"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['number'])) value="{{ $paymentInfo['number'] }}" @endif name="number"
                            placeholder="{{ sca_trans($groupLang, 'debit_card_number', [], $locale) }}" required="">
                    </div>
                @break

                @case('cheque')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        <input type="text" class="form-control my-2"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['payto'])) value="{{ $paymentInfo['payto'] }}" @endif name="payto"
                            placeholder="{{ sca_trans($groupLang, 'pay_to', [], $locale) }}" required="">
                        <input type="text" class="form-control"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['address'])) value="{{ $paymentInfo['address'] }}" @endif name="address"
                            placeholder="{{ sca_trans($groupLang, 'address', [], $locale) }}" required="">
                    </div>
                @break

                @case('venmo')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        <input type="text" class="form-control mt-2"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['mobile_email'])) value="{{ $paymentInfo['mobile_email'] }}" @endif
                            name="mobile_email" placeholder="{{ sca_trans($groupLang, 'venmo_mobile', [], $locale) }}"
                            required="">
                    </div>
                @break

                @case('paytm')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        <input type="text" class="form-control mt-2"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['number'])) value="{{ $paymentInfo['number'] }}" @endif name="number"
                            placeholder="{{ sca_trans($groupLang, 'paytm_mobile', [], $locale) }}" required="">
                    </div>
                @break

                @case('upi')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        <input type="text" class="form-control mt-2"
                            @if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['id'])) value="{{ $paymentInfo['id'] }}" @endif name="id"
                            placeholder="{{ sca_trans($groupLang, 'upi_id', [], $locale) }}" required="">
                    </div>
                @break

                @case('other')
                    <div data-payment-method="{{ $paymentMethodName }}">
                        @if ($merchantSetting->other_payment_method_description != null)
                            <label class="mt-2 mb-1">{{ $merchantSetting->other_payment_method_description }}</label>
                        @endif
                        <textarea name="other" style="width:100%; height:100px;" class="form-control">
@if ($setting->payment_method == $paymentMethodName && isset($paymentInfo['other']))
{{ $paymentInfo['other'] }}
@endif
</textarea>
                    </div>
                @break

                @default
                    <span></span>
            @endswitch
        @endforeach
    </div>

    {{-- OTP modal --}}
    <div class="modal inmodal fade" id="otp-payment-setting" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="ibox-tools">
                        <a class="close-link close" data-dismiss="modal">
                            <span class="mdiv">
                                <span class="md"></span>
                            </span>
                        </a>
                    </div>
                    <h4 class="modal-title font-size-small otp-payment-setting__title">
                        {{ sca_trans($groupLang, 'otp_verification', [], $locale) }}
                    </h4>
                    <small class="font-bold"></small>
                </div>

                <div class="modal-body text-left">
                    <div class="row alert-new-otp-block" style="display: none;">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="alert alert-default-new-otp alert-dismissible mb-1" role="alert">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>

                                <div>
                                    <span class="d-flex"
                                        style="color: var(--text-body); gap: 4px; align-items: baseline;">
                                        <i class="fal fa-info-circle" style="font-size: 12px;"></i>
                                        <p class="mb-0">
                                            {{ sca_trans($groupLang, 'new_otp_sent_email', [], $locale) }}
                                            <b>{{ $affiliate->email }}</b>.
                                        </p>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <p class="direction_rtl">{{ sca_trans($groupLang, 'enter_otp_verification', [], $locale) }}
                        <b>{{ $affiliate->email }}</b>.</p>

                    <div class="form-group otp-groups d-flex justify-content-left">
                        <input id="otp-change-input-1" class="otp otp-input-change" type="text"
                            oninput='digitValidate(this)' maxlength=1>
                        <input id="otp-change-input-2" class="otp otp-input-change" type="text"
                            oninput='digitValidate(this)' maxlength=1>
                        <input id="otp-change-input-3" class="otp otp-input-change" type="text"
                            oninput='digitValidate(this)' maxlength=1>
                        <input id="otp-change-input-4" class="otp otp-input-change" type="text"
                            oninput='digitValidate(this)' maxlength=1>
                        <input id="otp-change-input-5" class="otp otp-input-change" type="text"
                            oninput='digitValidate(this)' maxlength=1>
                        <input id="otp-change-input-6" class="otp otp-input-change" type="text"
                            oninput='digitValidate(this)' maxlength=1>
                    </div>

                    <div class="otp-payment-setting__footer">
                        <input type="text" id="otp-payment-setting__footer-clock" hidden
                            value="{{ session('payment_setting_affiliate_resend_timeout') }}">

                        <p class="text-danger otp-invalid direction_rtl" style="display: none;"><i
                                class="fal fa-info-circle text-danger"></i>
                            <i> {{ sca_trans($groupLang, 'invalid_otp', [], $locale) }} </i>
                        </p>

                        <div class="otp-payment-setting__block direction_rtl d-flex align-items-center">
                            <div class="title_did_receive">{{ sca_trans($groupLang, 'did_receive_otp', [], $locale) }}
                            </div>
                            <button
                                class="text-primary @if (session('payment_setting_affiliate_resend_timeout') > time()) cursor-disabled @else otp-payment-setting__resend @endif"
                                data-loading-text="<i class='fal fa-spinner fa-spin '></i> {{ sca_trans($groupLang, 'loading', [], $locale) }}">
                                {{ sca_trans($groupLang, 'resend', [], $locale) }}
                            </button>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary" id="verify-otp"
                        data-loading-text="<i class='fal fa-spinner fa-spin'></i> {{ sca_trans($groupLang, 'loading', [], $locale) }}">
                        {{ sca_trans($groupLang, 'verify', [], $locale) }}
                    </button>
                    <button type="button" class="btn btn-white" data-dismiss="modal">
                        {{ sca_trans($groupLang, 'cancel', [], $locale) }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal Confirm  --}}
   <div class="modal fade" id="wise-confirm-modal" tabindex="-1" role="dialog" aria-labelledby="wiseConfirmLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
            <h1 class="modal-title font-size-small otp-payment-setting__title">
                Verify your bank account info
            </h1>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" style="background:#f8fafb">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-white" data-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary" id="wise-confirm-submit">Next</button>
        </div>
      </div>
    </div>
  </div>

@endsection

@section('script_inline')
    <script src="{{ cdn_asset('js/plugins/inputmask/jquery.inputmask.bundle.js') }}"></script>
    <script src="{{ cdn_asset('js/affiliate/settings/settings-all.js') }}"></script>
    <script>
        const sendOTP = "{{ route('aff.setting.payment.send.otp', ['domain' => $domain]) }}";
        const checkOTP = "{{ route('aff.setting.payment.send.check_otp', ['domain' => $domain]) }}";
        const validatePayment = "{{ route('aff.setting.payment.validate', ['domain' => $domain]) }}";
        const resendText = '{{ sca_trans($groupLang, 'resend', [], $locale) }}';

        const wiseCurrencies = {!! json_encode($wiseCurrencies) !!};
        const currenciesData = {!! json_encode(config('myconfig.currency.currencies')) !!};
        const currencyShopInfo = {!! json_encode($currencyShopInfo) !!};
        const canUseWise = {!! json_encode($canUseWise) !!};
    </script>
    <script src="{{ cdn_asset('js/affiliate/settings/select-currency.js') }}"></script>
    <script src="{{ cdn_asset('js/affiliate/settings/payment.min.js') }}"></script>
@endsection

# Wise Integration Implementation

## Overview
This implementation adds Wise recipient account creation functionality to the affiliate payment settings. After OTP verification, the system will create a recipient account on Wise and handle errors appropriately.

## Changes Made

### 1. SettingController.php
- **Modified `savePaymentAffiliateInfo()` method** to detect form type and handle Wise integration
- **Added `isOldBankForm()` method** to distinguish between old bank form and new Wise form
- **Added `handleWiseFormSubmission()` method** to process Wise form data and call API
- **Added `prepareWiseFormData()` method** to format data for Wise API
- **Added `formatWiseFormForStorage()` method** to format data for database storage

### 2. WisePayout Service
- **Added `createRecipientAccount()` method** to call the Wise API endpoint
- Uses existing service structure with proper error handling

### 3. Database Changes
- **Created migration** to add `wise_form_data` column to `affiliate_settings` table
- **Updated AffiliateSetting model** to include the new field with JSON casting

### 4. Form Detection Logic
The system detects form type based on submitted fields:

**Old Form Fields:**
- `account_type`, `name`, `account`, `number`, `branch`, `swift`

**New Wise Form Fields:**
- `currency`, `legalType`, `email`, `accountHolderName`, `IBAN`

### 5. Data Storage Format
Wise form data is stored in the following format:
```json
{
  "profile_id": *********,
  "form": [
    {
      "key": "legalType",
      "label": "Recipient type", 
      "value": "PRIVATE",
      "name": "Person"
    },
    {
      "key": "accountHolderName",
      "label": "Account name",
      "value": "UpPromote",
      "name": ""
    },
    {
      "key": "IBAN",
      "label": "IBAN", 
      "value": "45786-56756-87686-987",
      "name": ""
    }
  ]
}
```

## Flow Description

1. **User fills payment form** (either old bank form or new Wise form)
2. **User clicks "Save Changes"** → form validation → OTP sent
3. **User enters OTP** → OTP verification → form.submit()
4. **Server receives form data** → `updatePayment()` → `savePaymentAffiliateInfo()`
5. **System detects form type** using `isOldBankForm()`
6. **If new Wise form:**
   - Calls `handleWiseFormSubmission()`
   - Prepares data for Wise API
   - Calls `WisePayout::createRecipientAccount()`
   - If successful: saves to `affiliate_settings.wise_form_data`
   - If error: redirects back with validation errors
7. **If old form:** processes normally using existing logic

## Error Handling

- **Wise API errors** are caught and displayed as inline validation errors
- **Service unavailable** errors show user-friendly messages
- **Form validation** continues to work as before
- **Fallback** to old form processing if form type is unclear

## API Integration

The system calls the Wise API endpoint:
- **Endpoint:** `POST /wise/create-recipient-account`
- **Payload:** Contains affiliate info, shop info, and form data
- **Response:** Expected to contain `profile_id` on success or `error` message on failure

## Testing

Created `WisePaymentTest.php` with tests for:
- Form type detection (old vs new)
- Data formatting for storage
- Service method existence
- Integration flow validation

## Configuration

Uses existing payout configuration:
- `UPPROMOTE_PAYOUT_HOST` - API host URL
- `UPPROMOTE_PAYOUT_CLIENT_UUID` - Client identification

## Backward Compatibility

- **Old forms continue to work** exactly as before
- **No breaking changes** to existing functionality
- **Graceful fallback** if Wise integration fails
- **Database migration** is additive only

## Next Steps

1. **Run migration:** `php artisan migrate`
2. **Configure environment variables** for Wise API
3. **Test with sample data** to verify integration
4. **Monitor logs** for any integration issues
5. **Update frontend** if needed to handle new error messages

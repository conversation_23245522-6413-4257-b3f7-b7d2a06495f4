# ✅ WISE INTEGRATION - FINAL IMPLEMENTATION

## 📊 Data Storage Format

Wise payment info được lưu trong `affiliate_setting.payment_info` với format:

```json
{
  "profile_id": ********,
  "form": [
    {"key": "legalType", "label": "Recipient type", "value": "BUSINESS", "name": "Business"},
    {"key": "email", "label": "Email address", "value": "<EMAIL>", "name": ""},
    {"key": "accountHolderName", "label": "Account holder name", "value": "UpPromote", "name": ""},
    {"key": "IBAN", "label": "IBAN", "value": "**********************", "name": ""}
  ]
}
```

## 🔧 Implementation Details

### 1. API Integration
- **Uses existing `sentRecipientAccount()` API**
- **Expected response:** `['profile' => ********, ...]` (Wise API response)
- **Error handling:** redirect back với validation errors nếu API fail
- **Success condition:** Check `isset($wiseResponse['profile'])`

### 2. Data Processing
- **Extract `profile_id`** từ API response field `profile`
- **Format form data** với `formatWiseFormForStorage()` method
- **Save both** `profile_id` và `form` array vào `payment_info`

### 3. Form Detection (Simplified)
```php
private function isWiseForm($inputs) {
    return isset($inputs['wise_form']) && $inputs['wise_form'] === '1';
}
```

**Frontend Implementation:**
```html
<!-- Add this hidden field to Wise forms -->
<input type="hidden" name="wise_form" value="1">
```

### 4. Field Mapping
```php
private function formatWiseFormForStorage($inputs) {
    $fieldMappings = [
        'legalType' => [
            'label' => 'Recipient type',
            'options' => ['PRIVATE' => 'Person', 'BUSINESS' => 'Business']
        ],
        'accountType' => [
            'label' => 'Account type',
            'options' => ['CHECKING' => 'Checking', 'SAVINGS' => 'Savings']
        ],
        'accountHolderName' => ['label' => 'Account holder name'],
        'IBAN' => ['label' => 'IBAN'],
        'email' => ['label' => 'Email address'],
        // ... more mappings
    ];

    // Process each field
    foreach ($inputs as $key => $value) {
        if (in_array($key, ['_token', 'payment_method'])) continue;

        $mapping = $fieldMappings[$key] ?? null;
        $label = $mapping['label'] ?? ucfirst(str_replace(['_', '.'], [' ', ' '], $key));

        $name = '';
        if ($mapping && isset($mapping['options']) && isset($mapping['options'][$value])) {
            $name = $mapping['options'][$value];
        }

        $result[] = [
            'key' => $key,
            'label' => $label,
            'value' => $value,
            'name' => $name
        ];
    }

    return $result;
}
```

## 🎯 Key Features

✅ **Uses existing API** `sentRecipientAccount()`
✅ **Saves profile_id** từ API response
✅ **Form detection** dựa trên currency field và absence của old fields
✅ **Field mapping** với predefined labels và option values
✅ **Auto-generate labels** cho unknown fields
✅ **Error handling** với redirect back và validation messages
✅ **Backward compatible** với existing bank forms
✅ **No view changes** - chỉ lưu data, không hiển thị

## 📋 Flow Summary

1. **User submits form** với Wise fields (có currency, không có old bank fields)
2. **Detect Wise form** với `isWiseForm()` method
3. **Validate conditions** - merchant có Wise enabled, plan Professional+, currency supported
4. **Call API** `sentRecipientAccount()` với form data
5. **Check response** - success nếu có `profile` field
6. **Save data** với format: `{profile_id: xxx, form: [...]}`
7. **Redirect success** hoặc back với errors

## 🔒 Security & Validation

- **Server-side validation** của merchant eligibility
- **Currency validation** against `wiseCurrencies` list
- **API error handling** với proper error messages
- **Input sanitization** trong `formatWiseFormForStorage()`
- **Existing validation rules** vẫn apply cho `payment_method = 'bank'`

Perfect implementation! 🎉

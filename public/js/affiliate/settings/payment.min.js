const digitValidate = function (ele) {
    ele.value = ele.value.replace(/[^0-9]/g, '');
}

$(document).ready(function () {
    // Debit Card
    $('#payment_method_sl').change(function(){
        var val = $(this).val();
        var paymentInfoWrapper = $('.payment-info-input');
        let inputTemp = $('.payment-info-input-template div[data-payment-method="'+val+'"]').html();

        paymentInfoWrapper.html('');

        if(inputTemp){
            paymentInfoWrapper.html(inputTemp);
        }

        $(".card").inputmask({'mask': "*{1,4} *{1,4} *{1,4} *{1,4} *{1,4}"});
    });
    $('#payment_method_sl').trigger('change');
    $(".card").inputmask({'mask': "*{1,4} *{1,4} *{1,4} *{1,4} *{1,4}"});

    // Verify OTP save payment setting
    $('#otp-payment-setting .otp').keypress(function (e) {
        if (e.which === 13) { // Enter
            e.preventDefault();
            return false;
        }
    });

    const $inp = $("#otp-payment-setting .otp");
    $inp.on({
        paste: function (e) {
            const pastedData = e.originalEvent.clipboardData.getData('text');
            const pastedChars = pastedData.split("");

            const curIndex = $inp.index(this);

            for (let i = 0; i < pastedChars.length; i++) {
                const char = pastedChars[i];
                $inp.eq(curIndex + i).val(char).focus();
            }
        }
    });

    $('.otp-input-create, .otp-input-change').keyup(function (e) {
        addLogs('typing');
        const nextInput = $(this).next();
        if (!isNaN(e.key)) {
            $(this).val(e.key);
        }
        if ((e.which === 8 || e.which === 46) && $(this).val() === '') {
            $(this).prev().focus();
        }
        const inputValue = $(this).val();
        if (nextInput && inputValue !== "" && inputValue !== " " && !isNaN(e.key)) {
            nextInput.focus();
        }
    });

    const form = document.querySelector('form#payment_setting');
    const buttonFormSubmit = document.querySelector(`form#payment_setting button[type="submit"]`);
    let blockTimeButton = $(`.otp-payment-setting__block button`);
    let alertResendOtp = $(`.alert-new-otp-block`);
    let initialFormData = new FormData(form);
    console.log('123', initialFormData)
    let isFormChanged = false;

    function isFormDataChanged(form, initialData) {
        const currentData = new FormData(form);
        for (let [key, value] of currentData.entries()) {
            
            if (initialData.get(key) !== value) {
                return true; // is changed
            }
        }

        return false;
    }

    $('#payment_setting').on('input change', 'input, select, textarea', function() {
        isFormChanged = isFormDataChanged(form, initialFormData);
        console.log('Form changed:', isFormChanged);
      });

    form.addEventListener('submit', function (event) {
        if ($('#wise-form-container').is(':visible') && !validateWiseFields()) {
            event.preventDefault();
            $(buttonFormSubmit).button('loading');
            $(this).find('button[type=submit]').button('reset');
            return;
        }
        if (isFormChanged) {
            event.preventDefault();
            $(buttonFormSubmit).button('loading');
            const data = $(form)
            .find('input:visible, select:visible, textarea:visible')
            .serialize();
            // Validate payment method
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                type: 'POST',
                url: validatePayment,
                data: data,
                dataType: 'json',
                success: function(validationResult) {
                    if (validationResult.status === 'success') {
                        $(`form .alert`).remove();
                        
                        $.ajax({
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            type : 'POST',
                            url : sendOTP,
                            dataType: 'json',
                            success : function(result) {
                                if (result.status === 'ok') { // send mail success
                                    addLogs('remove');
                                    populateConfirmModal();          
                                    $('#wise-confirm-modal').modal('show');
                                    // $(`#otp-payment-setting`).modal('show');
                                } else { // no_send_mail_otp -> auto
                                    form.submit();
                                }
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                                console.log('Could not get posts, server response: ' + textStatus + ': ' + errorThrown);
                            },
                            complete: function () {
                                $(buttonFormSubmit).button('reset');
                            }
                        });
                    } else {
                        // Handle validation errors
                        if (validationResult.errors) {
                            displayErrors(validationResult.errors);
                        }
                        $(buttonFormSubmit).button('reset');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    if (jqXHR.status === 422) {
                        // Handle validation errors
                        const errors = jqXHR.responseJSON.errors;
                        displayErrors(errors);
                    } else {
                        console.log('Could not validate, server response: ' + textStatus + ': ' + errorThrown);
                    }
                    $(buttonFormSubmit).button('reset');
                }
            });
        }
    });

    function displayErrors(errors) {
        $(`form .alert`).remove();
        let errorHtml = '<div class="alert alert-danger"><ul>';
        for (const field in errors) {
            if (errors.hasOwnProperty(field)) {
                errors[field].forEach(error => {
                    errorHtml += `<li>${error}</li>`;
                });
            }
        }
        errorHtml += '</ul></div>';
        $(`form#payment_setting`).prepend(errorHtml);
    }

    // Process verify OTP
    $(`#otp-payment-setting #verify-otp`).click(function (event) {
        let buttonVerify = $(`#otp-payment-setting #verify-otp`);
        $(buttonVerify).button('loading');

        let otpNumber = '';
        for (let i = 1; i < 7; i++) {
            otpNumber += $('#otp-change-input-' + i).val();
        }

        if(!otpNumber) {
            $(buttonVerify).button('reset');
            addLogs();
        } else {
            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                type : 'POST',
                url : checkOTP,
                dataType: 'json',
                data : {otp : otpNumber},

                success : function(result) {
                    if (result.status == 'ok') {
                        form.submit();
                    } else {
                        $(buttonVerify).button('reset');
                        addLogs();
                    }
                },

                error: function (jqXHR, textStatus, errorThrown) {
                    console.log('Could not get posts, server response: ' + textStatus + ': ' + errorThrown);
                },
                complete: function () {
                    $(`#otp-payment-setting #verify-otp`).button('reset');
                }
            });
        }
    });

    // Resend OTP email
    $(document).on('click', 'button.otp-payment-setting__resend', function () {
        $(this).button('loading');

        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type : 'POST',
            url : sendOTP,
            dataType: 'json',
            data : {type : true},

            success : function(result) {
                if (result.status == 'ok') {
                    blockTimeButton.removeClass('otp-payment-setting__resend').addClass('cursor-disabled');
                    let timeResendMail = parseInt(new Date().getTime() / 1000) + 120;
                    alertResendOtp.show();
                    $(`input#otp-payment-setting__footer-clock`).val(timeResendMail);
                    showTimeCounterResendOTP();
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log('Could not get posts, server response: ' + textStatus + ': ' + errorThrown);
            },
            complete: function () {
                $(`button.otp-payment-setting__resend`).button('reset');
            }
        });
    });

    function addLogs(action = null) {
        if (action == 'remove') {
            $(`p.otp-invalid`).hide();
            $(`#otp-payment-setting .otp`).removeAttr('style');
            for (let i = 1; i < 7; i++) {
                $('#otp-change-input-' + i).val('');
            }
        } else if (action == 'typing') {
            $(`p.otp-invalid`).hide();
            $(`#otp-payment-setting .otp`).removeAttr('style');
        } else {
            $(`p.otp-invalid`).show();
            $(`#otp-payment-setting .otp`).css('border', '1px solid #FF4141');
        }
    }

    const showTimeCounterResendOTP = () => {
        let secondsNow = parseInt(new Date().getTime() / 1000);
        let timeResendMail = $(`input#otp-payment-setting__footer-clock`).val();
        let unixTimestamp = timeResendMail - secondsNow;

        if (unixTimestamp < 0) {
            alertResendOtp.hide();
            blockTimeButton.removeClass('cursor-disabled disabled').addClass('otp-payment-setting__resend');
            blockTimeButton.removeAttr('disabled');
            blockTimeButton.html(resendText);
            return;
        } else {
            alertResendOtp.show();
            var date = new Date(unixTimestamp * 1000);
            var minutes = date.getMinutes();
            var seconds = date.getSeconds();

            minutes = (minutes < 10) ? "0" + minutes : minutes;
            seconds = (seconds < 10) ? "0" + seconds : seconds;

            var time = minutes + ":" + seconds;
            blockTimeButton.html(time);

            setTimeout(
                showTimeCounterResendOTP,
                1000
            );
        }
    }

    showTimeCounterResendOTP();

    function populateConfirmModal() {
        var $body = $('#wise-confirm-modal .modal-body');
        var html  = '<div class="body-wise-confirm">';
        
        // Xác định container đang hiển thị
        var $container = $('#wise-form-container').is(':visible')
            ? $('#wise-form-container')
            : $('#standard-bank-form-template');
        
  
      // 2) Các field khác (text, select, date…)
      $container
        .find('input:not([type="radio"]), select')
        .each(function() {
          var $el   = $(this);
          var tag   = $el.prop('tagName').toLowerCase();
          var name  = $el.attr('name') || $el.attr('id');
          var label = $('label[for="' + $el.attr('id') + '"]').text()
                      || $el.attr('placeholder')
                      || name;
  
          var value = '(blank)';
          if (tag === 'select') {
            value = $el.find('option:selected').text() || '(blank)';
          } else if (tag === 'input') {
            var type = $el.attr('type');
            if (type === 'date') {
              value = $el.val() || '(blank)';
            } else {
              value = $el.val() || '(blank)';
            }
          }
  
          html += '<p><strong>' + label + ':</strong> ' + value + '</p>';
        });

        $container
        .find('input[type="radio"]:checked')
        .each(function() {
          var $el   = $(this);
          var id    = $el.attr('id');
          // lấy label tương ứng
          var label = $('label[for="' + id + '"]').text().trim() || $el.val();
          var value = $el.val();
          html += '<p><strong>' + label + ':</strong> ' + value + '</p>';
        });
  
      html += '</div>';
      $body.html(html);
  }
  $(document).on('click', 'button#wise-confirm-submit', function () {
    $('#wise-confirm-modal').modal('hide');
    // $(`#otp-payment-setting`).modal('show');
    setTimeout(() => $('#otp-payment-setting').modal('show'), 400);

  });
});
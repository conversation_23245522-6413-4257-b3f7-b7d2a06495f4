$(document).ready(function() {
  // <PERSON><PERSON>ến lưu trữ dữ liệu requirements hiện tại
  var currentRequirements = null;
  var currentMethodIndex = 0;

  $('#payment_method_sl').on('change', function() {
      setTimeout(function() {
          initCurrencySelect();
      }, 300);
  });

  function initCurrencySelect() {
      var $currency = $('#currency');
      if (!$currency.length) {
          return;
      }

      if ($currency.hasClass('select2-hidden-accessible') && $currency.data('select2')) {
          $currency.select2('destroy');
      }
      $('.select2-container').remove();

      $currency.select2({
          placeholder: 'Select Currency',
          width: '100%',
          templateResult: function(data) {
              if (!data.id) return data.text;

              var currencyCode = data.id;
              var text = data.text || '';
              var parts = text.split(' - ');
              var name = parts.length > 1 ? parts[1].trim() : '';

              return $('<span><strong>' + currencyCode + ':</strong> ' + name + '</span>');
          },
          templateSelection: function(data) {
              if (!data.id) return data.text;

              var currencyCode = data.id;

              // Tìm currency name từ currenciesData
              var currencyName = '';
              for (var i = 0; i < currenciesData.length; i++) {
                  if (currenciesData[i].code === currencyCode) {
                      currencyName = currenciesData[i].name;
                      break;
                  }
              }

              return currencyCode + ': ' + currencyName;
          }
      });

      // add placeholder
      $currency.on('select2:open', function() {
          setTimeout(function() {
              $('.select2-search--dropdown').addClass('with-search-icon');
              $('.select2-search__field').attr('placeholder', 'Search');
          }, 0);
      });

      // onchange currency
      $currency.off('select2:select').on('select2:select', function(e) {
          var selectedCurrency = $(this).val();
          handleCurrencySelection(selectedCurrency);
      });

      // Kích hoạt sự kiện chọn ban đầu nếu đã có currency được chọn
      if ($currency.val()) {
          $currency.trigger('select2:select');
      }
  }

  // Hàm xử lý khi chọn currency
  function handleCurrencySelection(currencyCode) {
      var isWiseCurrency = wiseCurrencies.indexOf(currencyCode) !== -1;

      if (isWiseCurrency) {
          $('.ibox-content-right').css("background-color","white");
          $('#standard-bank-form-template').addClass('d-none');
          // Hiện form Wise
          $('#wise-form-container').removeClass('d-none');
          $('#standard-bank-form-template').find('input, select').prop('disabled', true);

          getWiseAccountRequirements(currencyCode);
      } else {
          showBankForm();
      }
  }

  // Hiển thị form ngân hàng tiêu chuẩn
  function showBankForm() {
      $('#standard-bank-form-template').find('input, select').prop('disabled', false);
      // Lấy nội dung từ template và đưa vào container
      $('#wise-form-container').addClass('d-none').empty();
      // Hiện luôn template bank bình thường
      $('#standard-bank-form-template').removeClass('d-none');
      $('.ibox-content-right').css("background-color","#E5E7EB");
  }

  // Hàm lưu trữ giá trị hiện tại của form
  function saveCurrentFormValues() {
      var formData = {};
      $('#wise-form-container').find('input, select').each(function() {
          var $field = $(this);
          var key = $field.attr('name');
          if (key) {
              if ($field.attr('type') === 'radio') {
                  if ($field.is(':checked')) {
                      formData[key] = $field.val();
                  }
              } else {
                  formData[key] = $field.val();
              }
          }
      });
      return formData;
  }

  // Hàm khôi phục giá trị form
  function restoreFormValues(formData) {
      if (!formData) return;

      setTimeout(function() {
          $('#wise-form-container').find('input, select').each(function() {
              var $field = $(this);
              var key = $field.attr('name');
              if (key && formData[key] !== undefined) {
                  if ($field.attr('type') === 'radio') {
                      if ($field.val() === formData[key]) {
                          $field.prop('checked', true);
                      }
                  } else {
                      $field.val(formData[key]);
                  }
              }
          });
      }, 50);
  }

  // Hàm gọi API Wise để lấy thông tin yêu cầu tài khoản - FIXED VERSION
  function getWiseAccountRequirements(targetCurrency, existingFormData = null) {
    // Lấy thông tin source currency từ shop_info
    var sourceAmount = 1000; // Mặc định

    // Nếu không có existingFormData, hiển thị loading
    if (!existingFormData) {
        $('#wise-form-container').html('<div class="text-center my-3"><i class="fa fa-spinner fa-spin"></i> Loading form...</div>');
        $('#wise-form-container').show();
    }

    // Query parameters - giống nhau cho cả GET và POST
    var queryParams = {
        source: currencyShopInfo,
        target: targetCurrency,
        sourceAmount: sourceAmount
    };

    var ajaxConfig = {
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Accept': 'application/json'
        },
        url: 'https://api.sandbox.transferwise.tech/v1/account-requirements?' + $.param(queryParams),
        beforeSend: function(request) {
            request.setRequestHeader("Accept-Minor-Version", 1);
        },
        success: function(response) {
            console.log('Wise account requirements response:', response);
            currentRequirements = response;

            if (!existingFormData) {
                // Lần đầu load - render form mới
                renderWiseForm(response);
            } else {
                // Update form - giữ nguyên dữ liệu
                updateWiseForm(response, existingFormData);
            }
            $('.ibox-content-right').css("background-color","#E5E7EB");
        },
        error: function(xhr, status, error) {
            console.error('Error fetching account requirements:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });

            if (!existingFormData) {
                $('#wise-form-container').html('<div class="alert alert-danger">Failed to load Wise form. Using default bank form.</div>');
                showBankForm();
            }
        }
    };

    // Nếu không có existingFormData → GET request
    if (!existingFormData) {
        ajaxConfig.type = 'GET';
    }
    // Nếu có existingFormData → POST request với payload
    else {
        ajaxConfig.type = 'POST';
        ajaxConfig.headers['Content-Type'] = 'application/json';

        // Tạo payload với structure đặc biệt
        var payload = {
            details: {}
        };

        // Đưa tất cả form data vào details object
        for (var key in existingFormData) {
            if (existingFormData[key]) {
                // Xử lý nested fields như address.country
                if (key.includes('.')) {
                    var parts = key.split('.');
                    var obj = payload.details;
                    for (var i = 0; i < parts.length - 1; i++) {
                        if (!obj[parts[i]]) {
                            obj[parts[i]] = {};
                        }
                        obj = obj[parts[i]];
                    }
                    obj[parts[parts.length - 1]] = existingFormData[key];
                } else {
                    // Simple fields
                    payload.details[key] = existingFormData[key];
                }

                // Duplicate một số fields quan trọng ở root level
                if (key === 'legalType') {
                    payload[key] = existingFormData[key];
                }
            }
        }

        ajaxConfig.data = JSON.stringify(payload);

        // Debug payload
        console.log('POST payload:', payload);
    }

    $.ajax(ajaxConfig);
  }

  // Hàm cập nhật form mà không làm mất dữ liệu - IMPROVED VERSION
  function updateWiseForm(data, existingFormData) {
      console.log('Updating form with data:', data);
      console.log('Existing form data:', existingFormData);

      if (!data || data.length === 0) {
          console.log('No data received, showing bank form');
          showBankForm();
          return;
      }

      // Lọc bỏ phương thức email
      var filteredData = data.filter(function(method) {
          return method.type !== 'email';
      });

      if (filteredData.length === 0) {
          console.log('No valid methods after filtering, showing bank form');
          showBankForm();
          return;
      }

      // Tìm method hiện tại đang được chọn
      var currentMethodType = $('#bank_transfer_method').val();
      var methodIndex = 0;

      if (currentMethodType) {
          for (var i = 0; i < filteredData.length; i++) {
              if (filteredData[i].type === currentMethodType) {
                  methodIndex = i;
                  break;
              }
          }
      }

      currentMethodIndex = methodIndex;
      var selectedMethod = filteredData[methodIndex];

      console.log('Selected method for update:', selectedMethod);

      // Kiểm tra xem có cần update selector không
      var needUpdateSelector = filteredData.length > 1 && !$('#bank_transfer_method').length;

      if (needUpdateSelector) {
          // Cần tạo lại toàn bộ form
          renderWiseForm(data);
          // Khôi phục dữ liệu sau khi render
          setTimeout(function() {
              restoreFormValues(existingFormData);
          }, 100);
      } else {
          // Chỉ cập nhật content của method
          if ($('.payment-method-content').length) {
              $('.payment-method-content').html(renderMethodForm(selectedMethod));
          } else {
              // Không có payment-method-content, render lại toàn bộ
              renderWiseForm(data);
              setTimeout(function() {
                  restoreFormValues(existingFormData);
              }, 100);
              return;
          }

          // Khôi phục dữ liệu
          restoreFormValues(existingFormData);
      }

      // Đăng ký lại event handlers
      setTimeout(function() {
          registerRefreshHandlers(selectedMethod);
      }, 150);
  }

  // Hàm render form từ dữ liệu API trả về - IMPROVED VERSION
  function renderWiseForm(data) {
      console.log('Rendering wise form with data:', data);

      if (!data || data.length === 0) {
          console.log('No data to render, showing bank form');
          showBankForm();
          return;
      }

      // Lọc bỏ phương thức email (như yêu cầu)
      var filteredData = data.filter(function(method) {
          return method.type !== 'email';
      });

      if (filteredData.length === 0) {
          console.log('No valid methods after filtering, showing bank form');
          showBankForm();
          return;
      }

      console.log('Filtered methods:', filteredData);

      // Tạo html cho form
      var formHtml = '<div class="wise-form-wrapper">';

      formHtml += '<input type="hidden" name="wise_form" value="1">';

      // Sử dụng select nếu có nhiều phương thức
      if (filteredData.length > 0) {
          formHtml += '<div class="form-wise-group mb-3">';
          formHtml += '<label for="bank_transfer_method" class="form-label">';
          formHtml += '<span><strong>Bank transfer method</strong></span>';
          formHtml += '</label>';
          formHtml += '<select class="form-control form-control-select label__pointer" id="bank_transfer_method" name="type">';

          for (var i = 0; i < filteredData.length; i++) {
              var method = filteredData[i];
              var selected = i === 0 ? 'selected' : '';
              formHtml += '<option value="' + method.type + '" ' + selected + '>' + method.title + '</option>';
          }

          formHtml += '</select>';
          formHtml += '</div>';

          formHtml += '<div class="payment-method-content">';
          // Khởi tạo với method đầu tiên
          formHtml += renderMethodForm(filteredData[0]);
          formHtml += '</div>';
      } else {
          // Nếu chỉ có 1 phương thức, hiển thị trực tiếp
          formHtml += '<div class="payment-method-content">';
          formHtml += renderMethodForm(filteredData[0]);
          formHtml += '</div>';
      }

      formHtml += '</div>'; // close wise-form-wrapper

      // Hiển thị form
      $('#wise-form-container').html(formHtml).show();

      // Đăng ký event handlers
      setTimeout(function() {
          if (filteredData.length > 1) {
              $('#bank_transfer_method').on('change', function() {
                  var selectedType = $(this).val();
                  var selectedMethod = filteredData.find(function(method) {
                      return method.type === selectedType;
                  });

                  if (selectedMethod) {
                      currentMethodIndex = filteredData.findIndex(function(method) {
                          return method.type === selectedType;
                      });

                      console.log('Method changed to:', selectedMethod);
                      $('.payment-method-content').html(renderMethodForm(selectedMethod));

                      // Đăng ký lại handlers cho method mới
                      setTimeout(function() {
                          registerRefreshHandlers(selectedMethod);
                      }, 50);
                  }
              });
          }

          // Đăng ký handlers cho method hiện tại
          registerRefreshHandlers(filteredData[0]);
      }, 100);
  }

  // Hàm đăng ký event handlers cho các field có refreshRequirementsOnChange - IMPROVED VERSION
  function registerRefreshHandlers(method) {
      if (!method || !method.fields) {
          console.log('No method or fields to register handlers');
          return;
      }

      console.log('Registering refresh handlers for method:', method.type);

      method.fields.forEach(function(field) {
          if (field.group) {
              field.group.forEach(function(groupField) {
                  if (groupField.refreshRequirementsOnChange) {
                      var fieldName = groupField.key;
                      console.log('Registering handler for field:', fieldName);

                      // Xử lý cho radio buttons
                      if (groupField.type === 'radio') {
                          // Tìm tất cả radio buttons có cùng name
                          var $radioFields = $('input[name="' + fieldName + '"]');

                          if ($radioFields.length) {
                              // Loại bỏ event cũ để tránh duplicate
                              $radioFields.off('change.refresh');

                              // Đăng ký event mới cho tất cả radio buttons
                              $radioFields.on('change.refresh', function() {
                                  console.log('Radio changed:', fieldName, $(this).val());
                                  // Thêm delay nhỏ để đảm bảo UI đã update
                                  setTimeout(function() {
                                      handleRefreshRequirements();
                                  }, 100);
                              });

                              console.log('Registered handlers for', $radioFields.length, 'radio buttons');
                          }
                      } else {
                          // Xử lý cho các field khác (select, input)
                          var fieldId = 'wise_' + groupField.key.replace(/\./g, '_');
                          var $field = $('#' + fieldId);

                          if ($field.length) {
                              // Loại bỏ event cũ để tránh duplicate
                              $field.off('change.refresh');

                              // Đăng ký event mới
                              $field.on('change.refresh', function() {
                                  console.log('Field changed:', fieldName, $(this).val());
                                  // Thêm delay nhỏ để đảm bảo UI đã update
                                  setTimeout(function() {
                                      handleRefreshRequirements();
                                  }, 100);
                              });
                          }
                      }
                  }
              });
          }
      });
  }

  // Hàm xử lý khi cần refresh requirements - IMPROVED VERSION
  function handleRefreshRequirements() {

      // Lưu dữ liệu hiện tại
      var currentFormData = saveCurrentFormValues();
      console.log('Current form data:', currentFormData);

      // Kiểm tra xem có dữ liệu form không
      if (Object.keys(currentFormData).length === 0) {
          console.log('No form data to refresh with');
          return;
      }

      // Lấy currency hiện tại
      var selectedCurrency = $('#currency').val();

      if (selectedCurrency) {
          // Hiển thị loading indicator nhỏ
          if (!$('.refresh-indicator').length) {
              $('#wise-form-container').prepend('<div class="refresh-indicator"><small><i class="fa fa-spinner fa-spin"></i> Updating form...</small></div>');
          }

          // Gọi lại API với dữ liệu hiện tại
          getWiseAccountRequirements(selectedCurrency, currentFormData);

          // Xóa loading indicator sau 2 giây
          setTimeout(function() {
              $('.refresh-indicator').remove();
          }, 2000);
      }
  }

  // Hàm render form cho từng phương thức
  function renderMethodForm(method) {
      console.log('Rendering method form:', method);
      var formHtml = '<div class="wise-method-form">';

      if (method.title) {
          formHtml += '<h4 class="text-center mb-3">' + method.title + '</h4>';
      }

      if (method.usageInfo) {
          formHtml += '<div class="alert alert-info">' + method.usageInfo + '</div>';
      }

      // Render các trường form
      if (method.fields && method.fields.length > 0) {
          for (var i = 0; i < method.fields.length; i++) {
              var field = method.fields[i];
              formHtml += renderFieldGroup(field);
          }
      } else {
          formHtml += '<div class="alert alert-warning">No fields available for this method.</div>';
      }

      formHtml += '</div>';
      return formHtml;
  }

  // Hàm render nhóm trường form
  function renderFieldGroup(field) {
      console.log('Rendering field group:', field);
      var groupHtml = '<div class="form-wise">';
      groupHtml += '<div class="form-wise-group">';

      if (field.name) {
          var firstFieldKey = field.group && field.group.length > 0 ? field.group[0].key : '';
          var labelFor = firstFieldKey ? 'wise_' + firstFieldKey.replace(/\./g, '_') : '';
          groupHtml += '<label class="form-label"' + (labelFor ? ' for="' + labelFor + '"' : '') + '>' + field.name + '</label>';
      }

      if (field.group && field.group.length > 0) {
          for (var i = 0; i < field.group.length; i++) {
              var groupField = field.group[i];
              groupHtml += renderField(groupField);
          }
      }

      groupHtml += '</div>';
      groupHtml += '</div>';
      return groupHtml;
  }

  // Hàm render từng trường form - IMPROVED VERSION
  function renderField(field) {
    var fieldHtml = '';
    var fieldId = 'wise_' + field.key.replace(/\./g, '_');

    var attrs = '';
    if (field.required)          attrs += ' required';
    if (field.minLength)         attrs += ' data-minlength="' + field.minLength + '"';
    if (field.maxLength)         attrs += ' data-maxlength="' + field.maxLength + '"';

    // FIX: Escape quotes and special characters in regex pattern
    if (field.validationRegexp) {
        var re = field.validationRegexp.replace(/^\^|\$$/g, '');
        // Escape problematic characters for HTML attributes
        re = re.replace(/"/g, '&quot;')
               .replace(/'/g, '&#39;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;')
               .replace(/\[/g, '&#91;')  // Escape [
               .replace(/\]/g, '&#93;'); // Escape ]
        attrs += ' data-regexp="' + re + '"';
    }

    // Thêm class để identify refresh fields
    var cssClass = 'form-control form-control-select label__pointer';
    if (field.refreshRequirementsOnChange) {
        cssClass += ' refresh-on-change';
        attrs += ' data-refresh="true"';
    }

    switch(field.type) {
        case 'text':
            fieldHtml +=
              '<input type="text" class="' + cssClass + '" ' +
                'id="' + fieldId + '" name="' + field.key + '"' +
                ' placeholder="' + (field.example || field.name || 'Enter ' + field.key) + '"' +
                attrs +
              '>';
            break;
        case 'select':
            fieldHtml +=
              '<select class="' + cssClass + ' form-control-select" ' +
                'id="' + fieldId + '" name="' + field.key + '"' +
                attrs +
              '>' +
                '<option value="">Select ' + (field.name || field.key) + '</option>';
            (field.valuesAllowed || []).forEach(function(opt){
                fieldHtml += '<option value="'+opt.key+'">'+opt.name+'</option>';
            });
            fieldHtml += '</select>';
            break;
        case 'radio':
            fieldHtml += '<div class="radio-group">';
            (field.valuesAllowed || []).forEach(function(opt, index){
                var radioId = fieldId + '_' + opt.key;
                var radioClass = 'form-check-input';
                if (field.refreshRequirementsOnChange) {
                    radioClass += ' refresh-on-change';
                }
                fieldHtml +=
                  '<div class="form-check">' +
                    '<input class="' + radioClass + '" type="radio" ' +
                      'name="' + field.key + '" id="' + radioId + '" value="' + opt.key + '"' +
                      (field.required ? ' required' : '') +
                      (field.refreshRequirementsOnChange ? ' data-refresh="true"' : '') +
                    '>' +
                    '<label class="form-check-label" for="' + radioId + '">' + opt.name + '</label>' +
                  '</div>';
            });
            fieldHtml += '</div>';
            break;
        case 'date':
            fieldHtml +=
              '<input type="date" class="' + cssClass + '" ' +
                'id="' + fieldId + '" name="' + field.key + '"' +
                attrs +
              '>';
            break;
        default:
            fieldHtml +=
              '<input type="text" class="' + cssClass + '" ' +
                'id="' + fieldId + '" name="' + field.key + '"' +
                ' placeholder="' + (field.example || field.name || 'Enter ' + field.key) + '"' +
                attrs +
              '>';
    }

    return fieldHtml;
  }


  /**
   * Client-side validate các trường trong #wise-form-container
   * Trả về true nếu tất cả OK, false nếu có lỗi.
   */
window.validateWiseFields = function(){
  var valid = true;


  // Clear previous errors
  $('#wise-form-container').find('.invalid-feedback').remove();
  $('#wise-form-container').find('.is-invalid').removeClass('is-invalid');

  $('#wise-form-container').find('input, select').each(function(){
      var $el   = $(this),
          val   = $el.val() || '',
          minL  = parseInt($el.data('minlength')) || 0,
          maxL  = parseInt($el.data('maxlength')) || 0,
          reStr = $el.data('regexp'),
          error = null,
          re    = null;
      // Skip empty optional fields for regex validation
      var shouldValidateRegex = reStr && val.length > 0;

      // 1) Check required field
      if ($el.prop('required') && (!val || val.trim() === '')) {
          error = 'This field is required';
      }
      // 2) Check minlength (only if field has value)
      else if (minL > 0 && val.length > 0 && val.length < minL) {
          error = 'Must contain at least ' + minL + ' characters';
      }
      // 3) Check maxlength (only if field has value)
      else if (maxL > 0 && val.length > maxL) {
          error = 'Must not exceed ' + maxL + ' characters';
      }
      // 4) Check regex pattern (only if field has value and regex exists)
      else if (shouldValidateRegex) {
          try {
              // Decode HTML entities back to original characters
              var decodedRegex = reStr.replace(/&quot;/g, '"')
                                     .replace(/&#39;/g, "'")
                                     .replace(/&lt;/g, '<')
                                     .replace(/&gt;/g, '>')
                                     .replace(/&#91;/g, '[')
                                     .replace(/&#93;/g, ']')
                                     .replace(/&amp;/g, '&'); // Handle & last

              // Try to create regex - handle both /pattern/flags and plain pattern
              var regexMatch = decodedRegex.match(/^\/(.+)\/([gimsuy]*)$/);
              if (regexMatch) {
                  // Format: /pattern/flags
                  re = new RegExp(regexMatch[1], regexMatch[2]);
              } else {
                  // Plain pattern without slashes - make sure it matches the entire string
                  // Add ^ and $ if not present
                  var pattern = decodedRegex;
                  if (!pattern.startsWith('^')) pattern = '^' + pattern;
                  if (!pattern.endsWith('$')) pattern = pattern + '$';

                  re = new RegExp(pattern);
              }

              var regexResult = re.test(val);

              if (!regexResult) {
                  error = 'Invalid format for this field';
              }

          } catch (e) {
              error = 'Invalid format (regex error)';
          }
      }

      // Remove existing errors for this field
      $el.removeClass('is-invalid');
      $el.siblings('.invalid-feedback').remove();

      // Show error if exists
      if (error) {
          valid = false;
          $el.addClass('is-invalid');

          // Insert error message after the field
          $el.parent(".form-wise-group").after('<span class="invalid-feedback" role="alert">' + error + '</span>');
      }
  });
  return valid;
}
});
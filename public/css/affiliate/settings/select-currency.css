     /* Loại bỏ border của input */
     .select2-selection {
         border: none !important;
         outline: none !important;
     }

     /* Loại bỏ border như mũi tên chỉ trong ảnh */
     .select2-selection--single {
         border: none !important;
         background-color: transparent !important;
     }

     /* Loại bỏ border của input trong dropdown */
     .select2-search__field {
         border: none !important;
         outline: none !important;
         box-shadow: none !important;
     }

     .form-label-line-select {
         border: 1px solid #e5e6e7;
         border-radius: 10px;
         display: block;
         font-weight: normal;
         padding-top: 10px;
     }

     .form-label-line-select:focus {
         border-color: #1D85E8;
         outline: 0;
         -webkit-box-shadow: none;
         box-shadow: none;
     }

     /* .form-label-line-select span, .form-label-line-select .form-label-line-select__content {
        padding-left: 20px;
    } */

     .form-label-line-select .form-label-line-select__title {
         padding: 5px 0 5px 20px;
     }

     .form-label-line-select .form-label-line-select__content {
         border-top: 1px solid #e5e6e7;
         padding: 20px;
     }

     .form-label-line-select span strong,
     .btn-file strong,
     .tooltip-email {
         font-size: 12px !important;
     }

     .currency-text {
         padding-left: 20px;
     }

     .select2-container .select2-selection--single .select2-selection__rendered {
         display: block;
         padding-left: 8px;
         padding-right: 20px;
         overflow: hidden;
         text-overflow: ellipsis;
         padding-left: 20px !important;
         white-space: nowrap;
     }

     .form-label-line-select {
         padding-bottom: 5px;
     }

     /* Icon tìm kiếm */
     .with-search-icon .select2-search__field {
         background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23aaa" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>');
         background-repeat: no-repeat;
         background-position: 10px center;
         padding-left: 35px !important;
     }

     /* Tùy chỉnh option để hiển thị đúng code thay vì số */
     .select2-results__option {
         padding: 8px 12px;
         border: none !important;
     }

     /* Style code currency */
     .select2-results__option strong {
         font-weight: bold;
         color: #333;
     }

     /* Màu nền khi hover và chọn */
     .select2-container--default .select2-results__option--highlighted[aria-selected] {
         background-color: #007bff;
         color: white !important;
     }

     .select2-container--default .select2-results__option--highlighted[aria-selected] strong {
         color: white;
     }

     /* Dropdown container */
     .select2-dropdown {
         border: none !important;
         margin-top: 0;
         box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
     }

     .ibox-content-left,
     .ibox-content-right {
         width: 50%;
     }

     /* .ibox-content-right{
        background-color: #E5E7EB;
    } */
     .form-label-select {
         background-color: white;
     }

     #wise-form-container {
         padding: 25px;
     }

     .form-wise {
         margin-bottom: 3rem !important;
     }

     .form-wise-group {
         background-color: white;
         border-radius: 10px;
     }

     .form-wise-group .form-label {
         padding-left: 20px;
     }

     .form-wise-group .form-control-select,
     .form-wise-group .form-check {
         padding-left: 20px;
     }

     .invalid-feedback {
         color: red;
         margin-top: 1rem !important;
     }

     .body-wise-confirm {
         margin: 0 50px 0 50px;
         background-color: white;
     }

     .body-wise-confirm p {
         padding: 10px;
     }

     input:-webkit-autofill,
     textarea:-webkit-autofill,
     select:-webkit-autofill {
         -webkit-text-fill-color: #333 !important;
         box-shadow: inset 0 0 0px 1000px white !important;
     }

     input:-webkit-autofill {
         transition: background-color 5000s ease-in-out 0s !important;
     }
     #standard-bank-form-template{
        padding:25px;
     }
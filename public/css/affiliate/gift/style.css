.modal-dialog .modal-content .tabs-container
{
    margin-bottom: 10px;
}

.ibox-content .table-responsive #gift_datatable
{
    width: 100%;
}

#list-history
{
    border:0;
}

#available-gift{
    border:0;
}

#commission_content .modal-body .ibox-content{
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 1px solid #e5e5e5;
}

#gift_history_datatable{
    width: 100%;
}

.gift-img
{
    width: 100%;
}

.ibox-content .select
{
    margin-top: 0px;
    border-radius: 0px;
    align-items: center;
    justify-content: flex-end;
}

.ibox-content .select p
{
    margin: 0 10px 0 0;
}

.content-option
{
    background: #EDF1F3;
    padding: 10px 20px;
}

.box-image img{
    height: 78px ;
    width: 78px ;
    margin: auto;
}

@media (max-width: 500px){
    .img-mobile-none{
        display: none !important;
    }
    .img-right {
        padding-right: 10px !important;
        padding-left: 0;
        height: 55px;
        width: 75px;
    }
    .claim-content .ibox-content{
        display: flex;
    }
    .claim-content .ibox-content .discount{
        margin-left: 10px;
    }
    .box-img-content{
        width: auto;
    }
    .gift-name{
        padding-left: 15px;
    }
    .box-text-modal{
        padding-left: 30px;
        padding-left: 30px;
    }
}

@media (min-width: 1920px){
    .box-image img{
        height: 78px !important;
        width: 78px !important;
    }
}


@media (min-width: 1366px){
    .box-image img{
        height: 58px ;
        width: 58px ;
    }
}

@media (max-width: 1024px){
    .box-none{
        display: none !important;
    }
}

@media (max-width: 500px){
    .box-image {
        padding-right: 20px !important;
    }
}
@media (min-width: 1024px) {
    #detail_order .modal-dialog{
        width: 35% !important;
    }

    #gift_history_datatable_length label{
        margin-bottom: 15px;
    }

    #gift_datatable_length label{
        margin-bottom: 15px;
    }

}
.total-price{
    display: flex;
    justify-content: flex-end;
}
#gift_quantity
{
    width: 85%;
    margin-left: 20px;
    border:0;
    background: #fff;
    outline:none;
}
.box-div div
{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#gift_history_datatable tbody tr .box-image{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.box-content img{
    padding: 0;
    height: 80px;
}
.box-content{
    padding: 0 10px;
}
#search_btn{
    background: #EDF6FD;
    color: #092C4C;
}#search_btn_history{
    background: #EDF6FD;
    color: #092C4C;
}
 .btn-send{

     margin-right: 4px;
     padding: 5px 20px;
     background: #1D85E8;
     color: #ffff;
     font-weight: bolder;
     border-radius: 7px;
 }

.content-option {
    padding: 17px;
    border-radius: 7px;
}

.content-row{
    margin-bottom: 20px ;
    border-bottom: 1px solid #E8E9F2;
    padding-bottom: 20px;
}

.content-row:last-child{
    margin-bottom: 0 !important;
    border-bottom: 0 !important;
    padding-bottom: 0 !important;
}
.claim-product{
    padding-bottom: 0;
}

.data-content{
    vertical-align: middle !important;
}
.btn-send:hover{
    color: white;
}

.box-img-content{
    height: 50px !important;
}
.action {
    vertical-align: middle !important;
}
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Affiliate;
use App\Models\AffiliateSetting;
use App\Models\MerchantSetting;
use App\Services\WisePayout;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Mockery;

class WisePaymentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test affiliate
        $this->affiliate = Affiliate::factory()->create([
            'shop_id' => 1,
            'email' => '<EMAIL>'
        ]);
        
        // Create affiliate setting
        $this->affiliateSetting = AffiliateSetting::create([
            'affiliate_id' => $this->affiliate->id,
            'payment_method' => null,
            'payment_info' => null
        ]);
        
        // Create merchant setting
        MerchantSetting::create([
            'shop_id' => 1,
            'tax_option' => 1,
            'tax_type' => 1
        ]);
    }

    /** @test */
    public function it_can_detect_old_bank_form()
    {
        $controller = new \App\Http\Controllers\Affiliate\SettingController(
            app(\App\Services\TaxYearlyService\TaxYearlyService::class)
        );
        
        $oldFormData = [
            'payment_method' => 'bank',
            'account_type' => 'checking',
            'name' => 'John Doe',
            'account' => '*********',
            'number' => '*********',
            'branch' => 'Main Branch',
            'swift' => 'ABCD1234'
        ];
        
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('isOldBankForm');
        $method->setAccessible(true);
        
        $result = $method->invoke($controller, $oldFormData);
        
        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_detect_new_wise_form()
    {
        $controller = new \App\Http\Controllers\Affiliate\SettingController(
            app(\App\Services\TaxYearlyService\TaxYearlyService::class)
        );
        
        $newFormData = [
            'payment_method' => 'bank',
            'currency' => 'AED',
            'legalType' => 'BUSINESS',
            'email' => '<EMAIL>',
            'accountHolderName' => 'Test Company',
            'IBAN' => 'AE07033*********0123456'
        ];
        
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('isOldBankForm');
        $method->setAccessible(true);
        
        $result = $method->invoke($controller, $newFormData);
        
        $this->assertFalse($result);
    }

    /** @test */
    public function it_can_format_wise_form_for_storage()
    {
        $controller = new \App\Http\Controllers\Affiliate\SettingController(
            app(\App\Services\TaxYearlyService\TaxYearlyService::class)
        );
        
        $wiseFormData = [
            'payment_method' => 'bank',
            'currency' => 'AED',
            'legalType' => 'PRIVATE',
            'email' => '<EMAIL>',
            'accountHolderName' => 'John Doe',
            'IBAN' => 'AE07033*********0123456'
        ];
        
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('formatWiseFormForStorage');
        $method->setAccessible(true);
        
        $result = $method->invoke($controller, $wiseFormData);
        
        $this->assertIsArray($result);
        $this->assertCount(5, $result); // Should have 5 fields (excluding payment_method)
        
        // Check structure
        foreach ($result as $field) {
            $this->assertArrayHasKey('key', $field);
            $this->assertArrayHasKey('label', $field);
            $this->assertArrayHasKey('value', $field);
            $this->assertArrayHasKey('name', $field);
        }
        
        // Check specific field
        $legalTypeField = collect($result)->firstWhere('key', 'legalType');
        $this->assertEquals('Recipient type', $legalTypeField['label']);
        $this->assertEquals('PRIVATE', $legalTypeField['value']);
        $this->assertEquals('Person', $legalTypeField['name']);
    }

    /** @test */
    public function wise_payout_service_has_create_recipient_method()
    {
        $wisePayoutService = new WisePayout();
        
        $this->assertTrue(method_exists($wisePayoutService, 'createRecipientAccount'));
    }
}

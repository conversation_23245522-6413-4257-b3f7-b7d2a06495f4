<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WisePaymentTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_detect_wise_form()
    {
        $controller = new \App\Http\Controllers\Affiliate\SettingController(
            app(\App\Services\TaxYearlyService\TaxYearlyService::class)
        );
        
        $wiseFormData = [
            'payment_method' => 'bank', // Still bank, but with Wise fields
            'currency' => 'AED',
            'legalType' => 'BUSINESS',
            'email' => '<EMAIL>',
            'accountHolderName' => 'Test Company',
            'IBAN' => 'AE07033*********0123456'
            // No old bank fields like account_type, name, account, number, branch, swift
        ];
        
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('isWiseForm');
        $method->setAccessible(true);
        
        $result = $method->invoke($controller, $wiseFormData);
        
        $this->assertTrue($result); // Should be Wise form
    }

    /** @test */
    public function it_can_detect_old_bank_form()
    {
        $controller = new \App\Http\Controllers\Affiliate\SettingController(
            app(\App\Services\TaxYearlyService\TaxYearlyService::class)
        );
        
        $oldBankFormData = [
            'payment_method' => 'bank',
            'account_type' => 'savings',
            'name' => 'John Doe',
            'account' => '*********',
            'number' => '*********',
            'branch' => 'Main Branch',
            'swift' => 'ABCDUS33'
        ];
        
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('isWiseForm');
        $method->setAccessible(true);
        
        $result = $method->invoke($controller, $oldBankFormData);
        
        $this->assertFalse($result); // Should NOT be Wise form
    }

    /** @test */
    public function it_can_format_wise_form_for_storage()
    {
        $controller = new \App\Http\Controllers\Affiliate\SettingController(
            app(\App\Services\TaxYearlyService\TaxYearlyService::class)
        );
        
        $inputs = [
            'legalType' => 'PRIVATE',
            'accountHolderName' => 'John Doe',
            'IBAN' => '**********************',
            'email' => '<EMAIL>',
            'currency' => 'GBP',
            'accountType' => 'CHECKING',
            'address.country' => 'GB',
            'address.city' => 'London',
            '_token' => 'test-token',
            'payment_method' => 'bank'
        ];
        
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('formatWiseFormForStorage');
        $method->setAccessible(true);
        
        $result = $method->invoke($controller, $inputs);
        
        $this->assertIsArray($result);
        $this->assertCount(7, $result); // Should exclude _token and payment_method
        
        // Check specific field formatting
        $legalTypeField = collect($result)->firstWhere('key', 'legalType');
        $this->assertEquals('Recipient type', $legalTypeField['label']);
        $this->assertEquals('PRIVATE', $legalTypeField['value']);
        $this->assertEquals('Person', $legalTypeField['name']);
        
        // Check account type field
        $accountTypeField = collect($result)->firstWhere('key', 'accountType');
        $this->assertEquals('Account type', $accountTypeField['label']);
        $this->assertEquals('CHECKING', $accountTypeField['value']);
        $this->assertEquals('Checking', $accountTypeField['name']);
        
        // Check address field
        $countryField = collect($result)->firstWhere('key', 'address.country');
        $this->assertEquals('Country', $countryField['label']);
        $this->assertEquals('GB', $countryField['value']);
        $this->assertEquals('', $countryField['name']); // No options mapping
    }

    /** @test */
    public function it_handles_unknown_fields_gracefully()
    {
        $controller = new \App\Http\Controllers\Affiliate\SettingController(
            app(\App\Services\TaxYearlyService\TaxYearlyService::class)
        );
        
        $inputs = [
            'unknownField' => 'some_value',
            'another_unknown_field' => 'another_value',
            'currency' => 'USD'
        ];
        
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('formatWiseFormForStorage');
        $method->setAccessible(true);
        
        $result = $method->invoke($controller, $inputs);
        
        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        
        // Check unknown field formatting
        $unknownField = collect($result)->firstWhere('key', 'unknownField');
        $this->assertEquals('Unknownfield', $unknownField['label']); // Auto-generated label
        $this->assertEquals('some_value', $unknownField['value']);
        $this->assertEquals('', $unknownField['name']); // No name mapping
    }
}
